import { describe, it, expect } from 'vitest'
import { detectCardBrand } from '../utils/payments'

describe('detectCardBrand', () => {
  it('detects Visa', () => {
    expect(detectCardBrand('****************')).toBe('visa')
  })

  it('detects Mastercard', () => {
    expect(detectCardBrand('****************')).toBe('mastercard')
  })

  it('detects Amex', () => {
    expect(detectCardBrand('***************')).toBe('amex')
  })

  it('detects Discover', () => {
    expect(detectCardBrand('****************')).toBe('discover')
  })

  it('detects JCB', () => {
    expect(detectCardBrand('3528000700000000')).toBe('jcb')
  })

  it('detects Diners Club', () => {
    expect(detectCardBrand('30000000000004')).toBe('diners')
  })

  it('returns empty string for unknown', () => {
    expect(detectCardBrand('1234567890123456')).toBe('')
  })
})
