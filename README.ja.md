# IPGO - 創造性と機会が出会う場所

<div align="center">
  <img src="public/images/logo-w-text.svg" alt="IPGO Logo" width="200"/>

**ファン、クリエイター、IP所有者をつなぐ革新的なプラットフォーム**

[![Nuxt 3](https://img.shields.io/badge/Nuxt-3.17.3-00DC82?logo=nuxt.js)](https://nuxt.com/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.6.3-3178C6?logo=typescript)](https://www.typescriptlang.org/)
[![Directus](https://img.shields.io/badge/Directus-19.1.0-6644FF?logo=directus)](https://directus.io/)
[![Vue 3](https://img.shields.io/badge/Vue-3.5.13-4FC08D?logo=vue.js)](https://vuejs.org/)

</div>

## 🌟 IPGOについて

IPGOは、ファンが愛する知的財産（IP）により近づけるよう設計された画期的なプラットフォームです。クリエイターやIP所有者との協力を通じて、IPGOはファンがユニークで高品質な作品にアクセスできるようにし、クリエイターには才能を収益化する機会を、IP所有者には新しい収益源を提供します。

### 🎯 ミッション

ファンが好きなフランチャイズとつながり、限定的な作品を探索し、創造性と共通の情熱を祝う活気あるコミュニティに参加するシームレスな方法を提供することで、ファンエンゲージメントを革新すること。

### 🏗️ プラットフォームアーキテクチャ

IPGOは、4つの主要セクションからなる多通貨、多役割、多言語プラットフォームです：

- **Directus CMS**
  - コンテンツ管理とデータストレージ
  - 柔軟なコンテンツ配信のためのヘッドレスCMS
  - Directus拡張機能でカスタマイズ可能

- **🛍️ ファンセクション**
  - ユニークなIPライセンス商品の閲覧と購入
  - クリエイターとその作品の発見
  - お気に入り、カート、注文の管理
  - 多言語サポート（英語、日本語、韓国語）
  - ✅ Stripe統合による決済処理
  - ✅ 注文管理と追跡
  - ✅ アドレス帳と支払い方法
  - ✅ 商品レビューと評価
  - ✅ クリエイターとのリアルタイムチャット

- **🎨 クリエイターセクション**
  - ✅ 商品リストの作成と管理
  - ✅ IPライセンスの申請
  - ✅ チャート付き売上分析の追跡
  - ✅ 注文チャットを通じたファンとのコミュニケーション
  - ✅ 注文履行と配送管理
  - ✅ 収益と数量分析ダッシュボード

- **👑 オーナーセクション**
  - ✅ IPポートフォリオの管理
  - ✅ クリエイター申請のレビューと承認
  - ✅ IP使用状況と収益の監視
  - ✅ クリエイターパフォーマンス付き分析ダッシュボード
  - ✅ IPアクティベーション/非アクティベーション制御

## 🚀 クイックスタート

### 前提条件

- Node.js 22+ または Bun
- PostgreSQLデータベース
- Directus CMSインスタンス
- AWS SES（メール機能用）
- Stripeアカウント（決済処理用）

### インストール

1. **リポジトリのクローン**

   ```bash
   git clone https://github.com/rafazafar/ipgo-nuxt.git
   cd ipgo-nuxt
   ```

2. **依存関係のインストール**

   ```bash
   # bunを使用（推奨）
   bun install

   # またはnpm/pnpm/yarnを使用
   npm install
   ```

3. **環境設定**

   ```bash
   cp .env.example .env
   ```

   環境変数を設定：

   ```env
   # Directus設定
   DIRECTUS_ADMIN_KEY=your_directus_admin_key
   NUXT_PUBLIC_DIRECTUS_BASE_URL=https://your-directus-instance.com

   # AWS SES設定
   AWS_REGION=ap-northeast-1
   AWS_ACCESS_KEY_ID=your_aws_access_key
   AWS_SECRET_ACCESS_KEY=your_aws_secret_key

   # メール設定
   EMAIL_DEFAULT_FROM=<EMAIL>
   DOMAIN_URL=http://localhost:3000

   # Stripe設定
   NUXT_STRIPE_PUBLIC_KEY=your_stripe_public_key
   STRIPE_SECRET_KEY=your_stripe_secret_key
   ```

4. **開発サーバーの起動**

   ```bash
   bun run dev
   ```

   `http://localhost:3000`にアクセスしてアプリケーションを確認してください。

## 🛠️ 技術スタック

### フロントエンド

- **[Nuxt 3](https://nuxt.com/)** - SSR/SSG対応のVue.jsフレームワーク
- **[Vue 3](https://vuejs.org/)** - プログレッシブJavaScriptフレームワーク
- **[TypeScript](https://www.typescriptlang.org/)** - 型安全なJavaScript
- **[Nuxt UI Pro](https://ui.nuxt.com/pro)** - プレミアムUIコンポーネント
- **[Tailwind CSS](https://tailwindcss.com/)** - ユーティリティファーストCSSフレームワーク

### バックエンド & CMS

- **[Directus](https://directus.io/)** - ヘッドレスCMSとAPI
- **[PostgreSQL](https://www.postgresql.org/)** - プライマリデータベース
- **[Kysely](https://kysely.dev/)** - 型安全SQLクエリビルダー

### 国際化

- **[@nuxtjs/i18n](https://i18n.nuxtjs.org/)** - 多言語サポート
- **言語**: 英語、日本語、韓国語

### 決済 & コミュニケーション

- **[Stripe](https://stripe.com/)** - 決済処理と金融取引
- **[AWS SES](https://aws.amazon.com/ses/)** - メール配信サービス

### 追加機能

- **[Nuxt Image](https://image.nuxt.com/)** - 最適化された画像処理
- **[Vue Easy Lightbox](https://github.com/XiongAmao/vue-easy-lightbox)** - 画像ギャラリー
- **[Nuxt Charts](https://github.com/nuxt-modules/charts)** - データ可視化
- **[Vue3 Star Ratings](https://github.com/craigh411/vue3-star-ratings)** - 商品評価システム
- **[FormKit Auto-animate](https://auto-animate.formkit.com/)** - スムーズなアニメーション

## 📁 プロジェクト構造

```
ipgo-nuxt/
├── app/                          # Nuxt 3アプリケーションコード
│   ├── components/               # Vueコンポーネント
│   │   ├── Fan/                 # ファン専用コンポーネント
│   │   ├── Creator/             # クリエイター専用コンポーネント
│   │   ├── Owner/               # オーナー専用コンポーネント
│   │   └── UI/                  # 再利用可能UIコンポーネント
│   ├── composables/             # Vueコンポーザブル
│   ├── layouts/                 # ページレイアウト
│   ├── middleware/              # ルートミドルウェア
│   ├── pages/                   # ファイルベースルーティング
│   │   ├── fan/                # ファンセクションページ
│   │   ├── creator/            # クリエイターセクションページ
│   │   ├── owner/              # オーナーセクションページ
│   │   ├── products/           # 商品ページ
│   │   ├── ip/                 # IP閲覧ページ
│   │   └── creators/           # クリエイター発見ページ
│   ├── plugins/                # Nuxtプラグイン
│   └── utils/                  # ユーティリティ関数
├── directus/                    # Directus CMS設定
│   ├── extensions/             # カスタムDirectus拡張機能
│   └── migration-scripts/      # データベースマイグレーションスクリプト
├── docs/                       # ドキュメント
├── i18n/                       # 国際化ファイル
│   └── locales/               # 翻訳ファイル
├── server/                     # NuxtサーバーAPI
│   └── api/                   # APIエンドポイント
├── shared/                     # 共有タイプとユーティリティ
│   ├── types/                 # TypeScript型定義
│   └── utils/                 # 共有ユーティリティ関数
└── tests/                      # テストファイル
    └── e2e/                   # エンドツーエンドテスト
```

## 🌐 機能

### ✅ 完了済み機能

#### ファンセクション
- **多言語サポート** - 英語、日本語、韓国語の自動検出
- **商品閲覧** - 高度なフィルタリングと検索機能付きIPライセンス商品の閲覧
- **クリエイター発見** - クリエイターとそのポートフォリオの探索
- **IP探索** - カテゴリ別知的財産の発見
- **ショッピングカート** - 商品追加、数量管理、クリエイター別グループ化
- **お気に入り/ウィッシュリスト** - 後で購入するための商品保存
- **ユーザー認証** - メール認証とOTPによる安全なログイン/サインアップ
- **決済処理** - 保存された支払い方法でのStripe統合
- **注文管理** - カートから完了までの完全な注文ライフサイクル
- **アドレス帳** - 複数の配送先住所の管理
- **商品レビュー** - 購入商品の評価とレビュー
- **リアルタイムチャット** - 注文チャットを通じたクリエイターとのコミュニケーション
- **レスポンシブデザイン** - モバイルファーストのレスポンシブインターフェース
- **画像最適化** - 最適化された画像読み込みとギャラリー

#### クリエイターセクション
- **商品管理** - 商品リストの作成、編集、管理
- **IP申請** - 知的財産のライセンス申請
- **注文履行** - 注文処理と配送済みマーク
- **分析ダッシュボード** - チャート付き収益と売上分析
- **クリエイターチャット** - 注文を通じた顧客とのコミュニケーション
- **プロフィール管理** - クリエイタープロフィールとポートフォリオの管理

#### オーナーセクション
- **IPポートフォリオ管理** - IPプロパティの作成、編集、管理
- **クリエイター申請レビュー** - クリエイターライセンス申請の承認/拒否
- **収益分析** - IPパフォーマンスとクリエイター収益の監視
- **IP制御** - IPプロパティのアクティベート/非アクティベート
- **クリエイターパフォーマンス** - クリエイターパフォーマンスの追跡と比較

#### 技術的機能
- **多通貨サポート** - USD、JPY、KRWの自動変換
- **役割ベースアクセス** - ファン、クリエイター、オーナー向け個別インターフェース
- **リアルタイム更新** - プラットフォーム全体でのライブデータ更新
- **セキュリティ** - JWT認証、CSRF保護、入力検証
- **パフォーマンス** - 最適化された読み込み、キャッシュ、画像配信

### 🔄 開発中

- **高度な検索** - フィルターと提案機能付き拡張検索
- **通知システム** - 注文更新のプッシュ通知
- **モバイルアプリ** - ネイティブモバイルアプリケーション
- **高度な分析** - より詳細なレポートと洞察

## 🚀 開発

### 利用可能なスクリプト

```bash
# 開発
bun run dev              # 開発サーバー起動
bun run build            # 本番用ビルド
bun run preview          # 本番ビルドプレビュー
bun run generate         # 静的サイト生成

# ユーティリティ
bun run add:t            # 新しいi18n翻訳エントリ追加

# テスト
bun run test:e2e         # エンドツーエンドテスト実行
```

### 開発ガイドライン

1. **コードスタイル**
   - 型安全性のためにTypeScriptを使用
   - Vue 3 Composition APIパターンに従う
   - mixinよりもcomposableを優先
   - 可能な限り汎用的な再利用可能関数を使用

2. **コンポーネント構成**
   - セクション固有のコンポーネントは各フォルダに配置（`Fan/`、`Creator/`、`Owner/`）
   - 再利用可能なコンポーネントは`UI/`フォルダを使用
   - PascalCase命名規則に従う

3. **状態管理**
   - 状態管理にはcomposableを使用
   - Nuxtの組み込み状態管理を活用
   - 必要でない限り複雑な状態管理ライブラリは避ける

4. **国際化**
   - 3つの言語ファイル（en.json、ja.json、ko.json）すべてに翻訳を追加
   - 新しい翻訳エントリには`add:t`スクリプトを使用
   - 言語切り替え機能をテスト
   - 動的コンテンツには翻訳ヘルパーユーティリティを使用

5. **データ管理**
   - クライアントサイドデータ取得には`useDirectusFetch`を使用
   - 昇格された権限が必要なサーバーサイド操作には`dFetch`を使用
   - 適切なエラーハンドリングとローディング状態を実装
   - 商品更新にはアーカイブ・作成パターンに従う

### API統合

アプリケーションは複数のサービスと統合：

- **Directus CMS** - コンテンツ管理、データストレージ、API
- **Stripe** - 決済処理と金融取引
- **AWS SES** - 通知と認証のためのメール配信サービス
- **カスタムサーバーAPI** - ビジネスロジック、認証、専用エンドポイント

主要な統合パターン：
- クライアントサイド：標準データ操作用`useDirectusFetch`
- サーバーサイド：管理操作用`dFetch`と`elevatedFetch`
- 決済：安全な決済処理用Stripe Elements
- メール：ユーザーコミュニケーション用カスタムテンプレート付きAWS SES

詳細なエンドポイント情報については[APIドキュメント](./APIs.md)を参照してください。

## 📚 ドキュメント

### コアドキュメント

- [📋 プロジェクト概要](./docs/project-overview.md) - ビジネスモデル、ビジョン、ロードマップ
- [🏗️ アーキテクチャ](./docs/architecture.md) - 技術アーキテクチャとデータモデル
- [💻 開発ガイド](./docs/development.md) - 開発セットアップとガイドライン
- [🚀 デプロイメントガイド](./docs/deployment.md) - 本番デプロイメント手順

### API & 統合

- [🔗 APIリファレンス](./APIs.md) - クイックAPIリファレンスガイド
- [📊 詳細APIドキュメント](./docs/api.md) - 包括的APIドキュメント

### 機能 & ユーティリティ

- [📧 メールシステム](./docs/email.md) - メールユーティリティとテンプレート
- [🌐 翻訳](./docs/translations.md) - 国際化ガイド
- [🧪 テストガイド](./E2E_CHECKLIST.md) - エンドツーエンドテストチェックリスト
- [💳 決済統合](./docs/payments.md) - Stripe統合ガイド

### 環境設定

本番デプロイメントでは、以下の環境変数を設定してください：

```env
# 本番Directus
NUXT_PUBLIC_DIRECTUS_BASE_URL=https://your-production-directus.com
DIRECTUS_ADMIN_KEY=your_production_admin_key

# 本番API
NUXT_PUBLIC_API_BASE=https://your-production-api.com
NUXT_PUBLIC_DOMAIN_URL=https://your-production-domain.com

# AWS SES（本番）
AWS_REGION=your_aws_region
AWS_ACCESS_KEY_ID=your_production_access_key
AWS_SECRET_ACCESS_KEY=your_production_secret_key

# Stripe（本番）
NUXT_STRIPE_PUBLIC_KEY=your_production_stripe_public_key
STRIPE_SECRET_KEY=your_production_stripe_secret_key
```

### デプロイメントプラットフォーム

アプリケーションは様々なプラットフォームにデプロイ可能：

- **Cloudflare Workers**（現在の設定）
- **Vercel**
- **Netlify**
- **AWS Amplify**
- **従来のVPS/Docker**

## 🤝 貢献

1. リポジトリをフォーク
2. 機能ブランチを作成（`git checkout -b feature/amazing-feature`）
3. 変更をコミット（`git commit -m 'Add amazing feature'`）
4. ブランチにプッシュ（`git push origin feature/amazing-feature`）
5. プルリクエストを開く

### 貢献者向け開発セットアップ

1. 上記のインストール手順に従う
2. 開発設定で`.env`ファイルを作成
3. ローカルDirectusインスタンスを設定するか、ステージング環境を使用
4. 決済テスト用にStripeテストキーを設定
5. メールテスト用にAWS SESを設定（またはステージング認証情報を使用）
6. 開発サーバーを実行してコーディング開始！

### コード品質

- TypeScriptベストプラクティスに従う
- 新機能の包括的なテストを作成
- ユーザーフローテスト用E2Eチェックリストを使用
- モバイルレスポンシブ性を確保
- サポートされているすべての言語でテスト

## 📄 ライセンス

このプロジェクトはSMG（Super Massive Global）によって開発されたプロプライエタリソフトウェアです。

## 🆘 サポート

サポートと質問については：

- **技術的問題**: このリポジトリでissueを作成
- **ビジネスに関するお問い合わせ**: [SMG](https://supermassiveglobal.co.jp/)にお問い合わせ
- **ドキュメント**: [docs](./docs/)フォルダを確認

---

<div align="center">
  <p><a href="https://supermassiveglobal.co.jp/">Super Massive Global</a>によって❤️で構築</p>
  <img src="public/images/logo-massive.svg" alt="SMG Logo" width="100"/>
</div>
