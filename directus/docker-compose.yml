services:
  directus:
    container_name: directus
    image: directus/directus:11
    restart: always
    ports:
      - 8055:8055
    volumes:
      - ./extensions:/directus/extensions
      - ./uploads:/directus/uploads
      - ./templates:/directus/templates
    environment:
      KEY: ${DIRECTUS_KEY}
      SECRET: ${DIRECTUS_SECRET}
      EXTENSIONS_AUTO_RELOAD: ${EXTENSIONS_AUTO_RELOAD:-true}
      ACCESS_TOKEN_TTL: ${ACCESS_TOKEN_TTL:-7d}
      CONTENT_SECURITY_POLICY_DIRECTIVES__FRAME_SRC: ${CSP_FRAME_SRC}
      CONTENT_SECURITY_POLICY_directives__child-src: ${CSP_CHILD_SRC}
      DB_CLIENT: 'pg'
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT:-5432}
      DB_DATABASE: ${DB_DATABASE}
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_SSL__REJECT_UNAUTHORIZED: false
      FILES_MAX_UPLOAD_SIZE: ${FILES_MAX_UPLOAD_SIZE:-100mb}
      ADMIN_EMAIL: ${ADMIN_EMAIL}
      ADMIN_PASSWORD: ${ADMIN_PASSWORD}
      CORS_ENABLED: ${CORS_ENABLED:-true}
      CORS_ORIGIN: ${CORS_ORIGIN}
      CORS_METHODS: ${CORS_METHODS:-GET,POST,PATCH,DELETE}
      CORS_ALLOWED_HEADERS: ${CORS_ALLOWED_HEADERS:-Content-Type,Authorization,X-Requested-With}
      CORS_EXPOSED_HEADERS: ${CORS_EXPOSED_HEADERS:-Content-Range,X-Content-Range}
      CACHE_ENABLED: ${CACHE_ENABLED:-false}
      CACHE_TTL: ${CACHE_TTL:-1m}
      CACHE_AUTO_PURGE: ${CACHE_AUTO_PURGE:-true}
      EMAIL_VERIFY_SETUP: ${EMAIL_VERIFY_SETUP:-true}
      EMAIL_FROM: ${EMAIL_FROM}
      EMAIL_TRANSPORT: ${EMAIL_TRANSPORT:-smtp}
      #EMAIL_SES_CREDENTIALS__ACCESS_KEY_ID: ${EMAIL_SES_ACCESS_KEY_ID}
      #EMAIL_SES_CREDENTIALS__SECRET_ACCESS_KEY: ${EMAIL_SES_SECRET_ACCESS_KEY}
      #EMAIL_SES_REGION: ${EMAIL_SES_REGION}
      EMAIL_SMTP_SECURE: ${EMAIL_SMTP_SECURE:-false}
      EMAIL_SMTP_HOST: ${EMAIL_SMTP_HOST}
      EMAIL_SMTP_PASSWORD: ${EMAIL_SMTP_PASSWORD}
      EMAIL_SMTP_PORT: ${EMAIL_SMTP_PORT:-587}
      EMAIL_SMTP_USER: ${EMAIL_SMTP_USER}
      PUBLIC_URL: ${PUBLIC_URL}
      STORAGE_LOCATIONS: ${STORAGE_LOCATIONS:-AWS}
      STORAGE_AWS_DRIVER: ${STORAGE_AWS_DRIVER:-s3}
      STORAGE_AWS_KEY: ${STORAGE_AWS_KEY}
      STORAGE_AWS_SECRET: ${STORAGE_AWS_SECRET}
      STORAGE_AWS_BUCKET: ${STORAGE_AWS_BUCKET}
      STORAGE_AWS_REGION: ${STORAGE_AWS_REGION:-ap-northeast-1}
      STORAGE_AWS_SERVER_SIDE_ENCRYPTION: ${STORAGE_AWS_SERVER_SIDE_ENCRYPTION:-false}
      ASSETS_TRANSFORM_IMAGE_MAX_DIMENSION: ${ASSETS_TRANSFORM_IMAGE_MAX_DIMENSION:-8000}
      # AUTH_PROVIDERS: "google,linkedin,github"
      # AUTH_GOOGLE_MODE: "json"
      # AUTH_GOOGLE_DRIVER: "openid"
      # AUTH_GOOGLE_CLIENT_ID: "***********-no1rvo0aqkals7glqf8th732ln698d3s.apps.googleusercontent.com"
      # AUTH_GOOGLE_CLIENT_SECRET: "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"
      # AUTH_GOOGLE_ISSUER_URL: "https://accounts.google.com/.well-known/openid-configuration"
      # AUTH_GOOGLE_IDENTIFIER_KEY: "email"
      # AUTH_GOOGLE_ALLOW_PUBLIC_REGISTRATION: "true"
      # AUTH_GOOGLE_DEFAULT_ROLE_ID: "38d4fed7-ba46-44d3-a69c-617e121823cd"
      # AUTH_GOOGLE_REDIRECT_ALLOW_LIST: "http://localhost:3001/webhooks/auth/google"
      # AUTH_LINKEDIN_DRIVER: "oauth2"
      # AUTH_LINKEDIN_CLIENT_ID: "77exp7mnr97w2c"
      # AUTH_LINKEDIN_CLIENT_SECRET: "qMkAiC7LBKxYxmH0"
      # AUTH_LINKEDIN_IDENTIFIER_KEY: "r_emailaddress"
      # AUTH_LINKEDIN_AUTHORIZE_URL: "https://www.linkedin.com/oauth/v2/authorization"
      # AUTH_LINKEDIN_ACCESS_URL: "https://www.linkedin.com/oauth/v2/accessToken"
      # AUTH_LINKEDIN_PROFILE_URL: "https://api.linkedin.com/v2/me"
      # AUTH_LINKEDIN_SCOPE: "r_emailaddress,r_liteprofile"
      # AUTH_LINKEDIN_ALLOW_PUBLIC_REGISTRATION: "true"
      # AUTH_LINKEDIN_DEFAULT_ROLE_ID: "38d4fed7-ba46-44d3-a69c-617e121823cd"
      # AUTH_LINKEDIN_REDIRECT_ALLOW_LIST: "http://localhost:3001/webhooks/auth/linkedin"
      # AUTH_FACEBOOK_DRIVER: "oauth2"
      # AUTH_FACEBOOK_CLIENT_ID: "..."
      # AUTH_FACEBOOK_CLIENT_SECRET: "..."
      # AUTH_FACEBOOK_AUTHORIZE_URL: "https://www.facebook.com/dialog/oauth"
      # AUTH_FACEBOOK_ACCESS_URL: "https://graph.facebook.com/oauth/access_token"
      # AUTH_FACEBOOK_PROFILE_URL: "https://graph.facebook.com/me?fields=email"
      # AUTH_GITHUB_DRIVER: "oauth2"
      # AUTH_GITHUB_CLIENT_ID: "********************"
      # AUTH_GITHUB_CLIENT_SECRET: "4c37b004346849fe68f35b9c280a53df15cb017b"
      # AUTH_GITHUB_AUTHORIZE_URL: "https://github.com/login/oauth/authorize"
      # AUTH_GITHUB_ACCESS_URL: "https://github.com/login/oauth/access_token"
      # AUTH_GITHUB_PROFILE_URL: "https://api.github.com/user"
      # AUTH_GITHUB_ALLOW_PUBLIC_REGISTRATION: "true"
      # AUTH_GITHUB_DEFAULT_ROLE_ID: "38d4fed7-ba46-44d3-a69c-617e121823cd"
      # AUTH_GITHUB_REDIRECT_ALLOW_LIST: "http://localhost:3001/webhooks/auth/github"
      # REFRESH_TOKEN_COOKIE_DOMAIN: "directus.seekers.my"
      # REFRESH_TOKEN_COOKIE_SECURE: "true"
      # REFRESH_TOKEN_COOKIE_SAME_SITE: "None"
