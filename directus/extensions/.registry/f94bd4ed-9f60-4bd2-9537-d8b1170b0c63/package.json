{"name": "directus-extension-field-actions", "version": "1.9.0", "type": "module", "icon": "web_traffic", "directus:extension": {"host": "^10.10.0", "type": "bundle", "path": {"app": "dist/app.js", "api": "dist/api.js"}, "entries": [{"type": "interface", "name": "interface", "source": "src/interface/index.ts"}, {"type": "display", "name": "display", "source": "src/display/index.ts"}]}, "description": "Add advanced link & copy functionalities to your directus fields. Supports interfaces as well as displays.", "repository": {"type": "git", "url": "https://github.com/utomic-media/directus-extension-field-actions.git"}, "keywords": ["directus", "directus-extension", "directus-interface", "directus-display", "directus-field-action", "directus-copy-to-clipboard", "directus-link", "directus-custom-bundle"], "author": "<PERSON> | Utomic Media", "license": "GPL-3.0", "bugs": {"url": "https://github.com/utomic-media/directus-extension-field-actions/issues"}, "homepage": "https://github.com/utomic-media/directus-extension-field-actions.git#readme", "devDependencies": {"@directus/extensions-sdk": "^11.0.2", "sass": "^1.74.1", "sass-loader": "^14.1.1", "typescript": "^5.4.3", "vue": "^3.4.21"}, "files": ["dist/**/*"], "scripts": {"build": "directus-extension build", "dev": "directus-extension build -w --no-minify", "link": "directus-extension link", "add": "directus-extension add", "test": "echo \\\"No test specified\\\" && exit 0"}}