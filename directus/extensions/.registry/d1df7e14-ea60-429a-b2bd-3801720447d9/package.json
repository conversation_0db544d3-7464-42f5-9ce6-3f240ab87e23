{"name": "@directus-labs/ai-writer-operation", "description": "Use OpenAI, Claude, Meta and Mistral Text Generation APIs to generate text.", "icon": "extension", "version": "1.4.0", "license": "MIT", "author": "Directus Labs", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["directus", "directus-extension", "directus-extension-operation", "chat-gpt", "openai", "artificial-intelligence", "ai"], "type": "module", "files": ["dist"], "directus:extension": {"type": "operation", "path": {"app": "dist/app.js", "api": "dist/api.js"}, "source": {"app": "src/app.ts", "api": "src/api.ts"}, "host": "^10.0.0 || ^11.0.0", "sandbox": {"enabled": true, "requestedScopes": {"log": {}, "request": {"methods": ["POST", "GET"], "urls": ["https://api.openai.com/v1/**", "https://api.anthropic.com/v1/**", "https://api.replicate.com/v1/**"]}, "sleep": {}}}}, "scripts": {"build": "directus-extension build", "dev": "directus-extension build -w --no-minify", "link": "directus-extension link", "validate": "directus-extension validate"}, "devDependencies": {"@directus/extensions": "^2.0.2", "@directus/extensions-sdk": "^13.0.0", "@types/node": "^22.7.7", "typescript": "^5.6.3", "vue": "^3.5.12"}, "dependencies": {"@directus/errors": "^1.0.0"}}