# Display Link extension for Directus

Display URLs, phone numbers, and emails with a link button in
[Directus](https://directus.io)

## Install

Search for "display link" in the Marketplace of your app settings, navigate to
the extension page, and click "Install Extension"

## Screenshots

![](https://raw.githubusercontent.com/jacoborus/directus-extension-display-link/main/screenshot.png)

![](https://raw.githubusercontent.com/jacoborus/directus-extension-display-link/main/screenshot-list.png)

![](https://raw.githubusercontent.com/jacoborus/directus-extension-display-link/main/screenshot-options.png)
