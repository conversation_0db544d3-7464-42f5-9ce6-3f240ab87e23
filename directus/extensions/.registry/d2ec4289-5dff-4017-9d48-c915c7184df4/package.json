{"name": "directus-extension-schema-management-module", "version": "2.0.1", "description": "UI to easily share schema between Directus apps", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>"}, "license": "gpl-3.0", "repository": {"type": "git", "url": "https://github.com/rezo-labs/directus-extension-schema-management-module.git"}, "keywords": ["directus", "directus-extension", "directus-custom-module"], "directus:extension": {"type": "module", "path": "dist/index.js", "source": "src/index.ts", "host": "^11.0.0"}, "scripts": {"build": "directus-extension build", "dev": "directus-extension build -w --no-minify"}, "devDependencies": {"@directus/extensions-sdk": "12.0.1", "sass": "^1.55.0", "typescript": "^4.8.4", "vue": "^3.2.40"}}