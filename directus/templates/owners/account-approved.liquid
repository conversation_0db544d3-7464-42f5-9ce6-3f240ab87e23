{% assign title = "Owner Registration Update" %}
{% assign header_title = "IP Owner Registration Update" %}
{% assign footer_message = "Thank you for your interest in IPGO!" %}

{% layout "base" %}
{% block content %}
<div class="content">
  {% if status == "approved" %}
    <div class="status-message status-approved">
      🎉 Congratulations! Your IP owner registration has been approved.
    </div>
  {% elsif status == "rejected" %}
    <div class="status-message status-rejected">
      🥺 Unfortunately, your IP owner registration has been rejected.
    </div>
  {% else %}
    <div class="status-message">
      📋 Your IP owner registration status has been updated.
    </div>
  {% endif %}

  <h2>Registration Details</h2>

  <div class="info-row">
    <span class="info-label">Contact Person:</span>
    <span class="info-value">{{ contact_person_name | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Company Name:</span>
    <span class="info-value">{{ company_name | default: "N/A" }}</span>
  </div>

  {% if status == "rejected" and rejection_reason %}
  <div class="info-row" style="border-left-color: #dc3545;">
    <span class="info-label">Rejection Reason:</span>
    <span class="info-value">{{ rejection_reason }}</span>
  </div>
  {% endif %}

  {% if status == "approved" %}
  <div class="text-center" style="margin-top: 30px; padding: 20px; background-color: #f0f9ff; border-radius: 8px; border-left: 4px solid #667eea;">
    <h3 style="color: #667eea; margin-bottom: 15px;">Welcome to IPGO!</h3>
    <p style="margin-bottom: 20px; color: #333;">Your IP owner account is now active. You can start managing your intellectual properties and reviewing creator applications.</p>
    <a href="{{ dashboard_url | default: 'https://owner.ipgo.space' }}" class="cta-button">
      Access Your Dashboard
    </a>
  </div>
  
  <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 6px;">
    <h4 style="color: #667eea; margin-bottom: 10px;">Next Steps:</h4>
    <ul style="margin: 0; padding-left: 20px; color: #666;">
      <li>Complete your IP portfolio setup</li>
      <li>Review and approve creator applications</li>
      <li>Monitor product sales and analytics</li>
      <li>Manage licensing agreements</li>
    </ul>
  </div>
  
  {% elsif status == "rejected" %}
  <div class="text-center" style="margin-top: 30px; padding: 20px; background-color: #fef2f2; border-radius: 8px; border-left: 4px solid #dc3545;">
    <h3 style="color: #dc3545; margin-bottom: 15px;">Registration Not Approved</h3>
    <p style="margin-bottom: 20px; color: #333;">Please review the rejection reason above and feel free to contact our support team if you have any questions.</p>
    <a href="{{ support_url | default: 'mailto:<EMAIL>' }}" class="cta-button" style="background-color: #dc3545;">
      Contact Support
    </a>
  </div>
  
  <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 6px;">
    <h4 style="color: #dc3545; margin-bottom: 10px;">What You Can Do:</h4>
    <ul style="margin: 0; padding-left: 20px; color: #666;">
      <li>Review the rejection reason carefully</li>
      <li>Gather additional documentation if needed</li>
      <li>Contact our support team for clarification</li>
      <li>Submit a new registration request when ready</li>
    </ul>
  </div>
  {% endif %}
</div>
{% endblock %}

{% comment %} 
Variables example JSON:
{
  "status": "approved", // "approved", "rejected", or other
  "contact_person_name": "John Doe",
  "company_name": "ACME Corporation", 
  "rejection_reason": "Business license document is not clear enough", // only for rejected status
  "dashboard_url": "https://owner.ipgo.space/dashboard", // for approved
  "support_url": "mailto:<EMAIL>" // for rejected
}
{% endcomment %}
