{% assign title = "Order Completed" %}
{% assign header_title = "Order Successfully Completed!" %}
{% assign footer_message = "Thank you for providing excellent service to your customers." %}

{% layout "base" %}
{% block content %}
<div class="content">
  <div class="status-message status-approved">
    <strong>🎉 Congratulations! Your order has been completed successfully.</strong>
  </div>

  <h2>Order Summary</h2>

  <div class="info-row">
    <span class="info-label">Order Number:</span>
    <span class="info-value">#{{ id | default: "N/A" }}</span>
  </div>

  <div class="text-center" style="margin: 30px 0;">
    <a href="{{ dashboard_url | default: 'https://creator.ipgo.space/orders' }}" class="cta-button">View Order Details</a>
  </div>

  <div style="margin-top: 30px; padding: 20px; background-color: #f0f9ff; border-radius: 8px; border-left: 4px solid #667eea;">
    <h3 style="color: #667eea; margin-bottom: 15px;">What's Next?</h3>
    <p style="margin-bottom: 15px; color: #333;">Your order has been marked as completed. The customer has confirmed receipt of their items.</p>
    <ul style="margin: 0; padding-left: 20px; color: #666; line-height: 1.6;">
      <li>Payment processing is now finalized</li>
      <li>Customer may leave a review for this order</li>
      <li>Order details remain accessible in your dashboard</li>
    </ul>
  </div>

  <p style="text-align: center; color: #666; font-size: 14px; margin-top: 20px;">
    Thank you for delivering quality products and excellent customer service!
  </p>
</div>
{% endblock %}

{% comment %}
Required payload data example:
{
  "order_number": "ORD-2024-001234",
  "order_date": "2024-01-15 14:30:00",
  "completion_date": "2024-01-22 16:45:00",
  "total_items": "3",
  "currency": "$",
  "order_total": "86.38",
  "dashboard_url": "https://ipgo.space/creator/orders/123"
}
{% endcomment %}