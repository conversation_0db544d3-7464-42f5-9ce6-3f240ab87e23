{% assign title = "New Order Confirmation" %}
{% assign header_title = "Order Confirmed!" %}
{% assign footer_message = "Thank you for your order. We'll keep you updated on its progress." %}

{% layout "base" %}
{% block content %}
<div class="content">
  <div class="status-message status-approved">
    <strong>Your order has been successfully placed!</strong>
  </div>

  <h2>Order Details</h2>

  <div class="info-row">
    <span class="info-label">Order Number:</span>
    <span class="info-value">#{{ id | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Order Date:</span>
    <span class="info-value">{{ date_created | date: "%B %d, %Y at %I:%M %p" | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Customer:</span>
    <span class="info-value">{{ user.first_name }} {{ user.last_name }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Status:</span>
    <span class="info-value">{{ status | default: "Processing" }}</span>
  </div>

  <h2>Items Ordered</h2>
  {% if order_items and order_items.size > 0 %}
    {% for item in order_items %}
    <div class="product-info">
      <div class="info-row">
        <span class="info-label">Product:</span>
        <span class="info-value">{{ item.product_name_at_order | default: "N/A" }}</span>
      </div>
      <div class="info-row">
        <span class="info-label">Quantity:</span>
        <span class="info-value">{{ item.quantity | default: 1 }}</span>
      </div>
    </div>
    {% endfor %}
  {% else %}
    <p>No items found in this order.</p>
  {% endif %}

  <h2>Order Summary</h2>

  <div class="info-row">
    <span class="info-label">Payment ID:</span>
    <span class="info-value">{{ payment_id | default: "N/A" }}</span>
  </div>

  <h2>Shipping Address</h2>
  <div class="info-row">
    <span class="info-label">Name:</span>
    <span class="info-value">{{ shipping_name | default: "N/A" }}</span>
  </div>
  <div class="info-row">
    <span class="info-label">Phone:</span>
    <span class="info-value">{{ shipping_phone_number | default: "N/A" }}</span>
  </div>
  <div style="margin-bottom: 15px;">
    <div style="font-weight: bold; margin-bottom: 5px;">Address:</div>
    <div style="margin-left: 0; line-height: 1.4;">
      {{ shipping_address_line_1 }}<br>
      {% if shipping_address_line_2 %}{{ shipping_address_line_2 }}<br>{% endif %}
      {{ shipping_city }}, {{ shipping_province }} {{ shipping_postcode }}<br>
      {{ shipping_country }}
    </div>
  </div>

  <div class="text-center" style="margin-top: 30px;">
    <a href="{{ dashboard_url | default: "https://ipgo.space/fan/orders" }}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
      View Order in Dashboard
    </a>
  </div>

  <div class="text-center" style="margin-top: 20px;">
    <p>We'll send you another email when your order ships. If you have any questions, please contact our support team.</p>
  </div>
</div>
{% endblock %}

{% comment %}
Expected payload data structure:
{
  "id": 69,
  "status": "processing",
  "date_created": "2025-07-23T12:24:57.333Z",
  "currency_code": "KRW",
  "total_amount": 10000,
  "payment_id": "pi_3Ro1j9Fh0L396NUj1D4Ahr27",
  "shipping_name": "John Doe",
  "shipping_phone_number": "1234567890",
  "shipping_address_line_1": "東京都大田区北千束",
  "shipping_address_line_2": "Apt 4B",
  "shipping_city": "大田区",
  "shipping_postcode": "1450062",
  "shipping_country": "JP",
  "shipping_province": "東京都",
  "user": {
    "first_name": "Demo",
    "last_name": "Fan",
    "email": "<EMAIL>"
  },
  "order_items": [
    {
      "id": 81,
      "quantity": 1,
      "message": "qewrgrqefq",
      "product_name_at_order": "Evangelion Pilot Ikari Shinji (7cm)",
      "price_at_order": "7.25000",
      "price_jpy_at_order": "1067.00000",
      "price_krw_at_order": "10000.00000"
    }
  ]
}
{% endcomment %}
