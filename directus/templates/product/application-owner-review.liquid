{% assign title = "Product Application Review Required" %}
{% assign header_title = "New Product Application Requires Your Approval" %}
{% assign footer_message = "This is an automated notification for a product application requiring your review." %}

{% layout "base" %}
{% block content %}
<div class="content">
  <div class="status-message" style="background-color: #fff3cd; border-left: 4px solid #ffc107; color: #856404;">
    ⏳ A new product application for your IP requires your review and approval.
  </div>

  <h2>Application Details</h2>

  <div class="info-row">
    <span class="info-label">Your IP:</span>
    <span class="info-value">{{ ip_name | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Creator's Username:</span>
    <span class="info-value">{{ creator_username | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Creator's Email:</span>
    <span class="info-value" style="color: black !important;">{{ creator_email | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Product Name:</span>
    <span class="info-value">{{ product_name | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Product Description:</span>
    <span class="info-value">{{ product_description | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Product Category:</span>
    <span class="info-value">{{ product_category | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Application Type:</span>
    <span class="info-value">{{ application_type | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Proposed Price:</span>
    <span class="info-value">{{ currency }}{{ price | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Total Stock:</span>
    <span class="info-value">{{ total_stock | default: 0 }}</span>
  </div>

  {% if images and images.size > 0 %}
  <div class="images-section">
    <div class="info-label">Product Design / Images:</div>
    <div style="margin-top: 10px;">
      {% for image in images %}
        <img src="{{ base_url | default: 'https://stg-admin.ipgo.space' }}/assets/{{ image.directus_files_id }}?width=400" alt="Product Image" style="max-width: 200px; margin: 5px; border-radius: 4px;">
        {% unless forloop.last %}<br>{% endunless %}
      {% endfor %}
    </div>
  </div>
  {% else %}
  <div class="images-section">
    <div class="info-label">Product Design / Images:</div>
    <div style="margin-top: 10px;">
      <p>No images provided</p>
    </div>
  </div>
  {% endif %}

  <div class="text-center" style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 8px;">
    <h3 style="margin-bottom: 15px; color: #667eea;">Action Required</h3>
    <p style="margin-bottom: 20px; color: #666;">Please review this product application and decide whether to approve or reject it. The creator is waiting for your decision.</p>
    
    <div style="margin-bottom: 20px;">
      <a href="{{ review_url | default: 'https://ipgo.space/owner/application-review' }}" class="cta-button" style="margin-right: 10px;">
        Review Application
      </a>
    </div>
    
    <div style="margin-top: 15px; padding: 15px; background-color: #e3f2fd; border-radius: 6px; text-align: left;">
      <h4 style="color: #1976d2; margin-bottom: 10px; font-size: 14px;">Review Guidelines:</h4>
      <ul style="margin: 0; padding-left: 20px; color: #666; font-size: 13px;">
        <li>Check if the product aligns with your IP brand and values</li>
        <li>Verify the quality and appropriateness of the design</li>
        <li>Review the proposed pricing and stock levels</li>
        <li>Ensure the creator has the necessary rights and permissions</li>
        <li>Consider the potential market impact and brand representation</li>
      </ul>
    </div>
  </div>
</div>
{% endblock %}

{% comment %}
Required payload data example:
{
  "base_url": "https://admin.ipgo.space",
  "ip_name": "My Awesome IP",
  "creator_username": "creator123",
  "creator_email": "<EMAIL>",
  "product_name": "Limited Edition Figure",
  "product_description": "High-quality collectible figure with detailed craftsmanship",
  "product_category": "Figures & Collectibles",
  "application_type": "New Product",
  "currency": "$",
  "price": "49.99",
  "total_stock": 100,
  "images": [
    {
      "directus_files_id": "abc123-def456-ghi789"
    },
    {
      "directus_files_id": "xyz789-uvw456-rst123"
    }
  ],
  "review_url": "https://owner.ipgo.space/applications/123",
  "application_deadline": "March 15, 2024"
}

Note: 
- base_url parameter is optional and defaults to "https://stg-admin.ipgo.space" if not provided
- application_deadline is optional and will show a deadline notice if provided
- review_url is optional and defaults to the general applications page if not provided
{% endcomment %}
