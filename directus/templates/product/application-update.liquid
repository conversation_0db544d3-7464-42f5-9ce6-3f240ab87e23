{% assign title = "Product Application Update" %}
{% assign header_title = "Product Application Update" %}
{% assign footer_message = "Thank you for using our platform!" %}

{% layout "base" %}
{% block content %}
<div class="content">
  {% if status == "approved" %}
    <div class="status-message status-approved">
      🎉 Congratulations! Your product application has been approved.
    </div>
  {% elsif status == "rejected" %}
    <div class="status-message status-rejected">
      🥺 Unfortunately, your product application has been rejected.
    </div>
  {% else %}
    <div class="status-message">
      📋 Your product application status has been updated.
    </div>
  {% endif %}

  <h2>Product Details</h2>

  <div class="info-row">
    <span class="info-label">IP:</span>
    <span class="info-value">{{ ip_name | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Product Name:</span>
    <span class="info-value">{{ product_name | default: "N/A" }}</span>
  </div>

  {% if status == "rejected" and rejection_reason %}
  <div class="info-row" style="border-left-color: #dc3545;">
    <span class="info-label">Rejection Reason:</span>
    <span class="info-value">{{ rejection_reason }}</span>
  </div>
  {% endif %}

  {% if product_images and product_images.size > 0 %}
  <div class="images-section">
    <div class="info-label">Design / Images:</div>
    <div style="margin-top: 10px;">
      {% for image in product_images %}
        <img src="{{ image }}" alt="Product Image" style="max-width: 200px; margin: 5px; border-radius: 4px;">
        {% unless forloop.last %}<br>{% endunless %}
      {% endfor %}
    </div>
  </div>
  {% endif %}

  {% if status == "approved" %}
  <div class="text-center" style="margin-top: 30px;">
    <p>Your product is ready to be published. Please log in to your dashboard to manage your product!</p>
    <a href="{{ product_url | default: 'https://creator.ipgo.space' }}" class="cta-button">View Your Product</a>
  </div>
  {% elsif status == "rejected" %}
  <div class="text-center" style="margin-top: 30px;">
    <p>You can review the feedback and submit a new application.</p>
    <a href="{{ reapply_url | default: 'https://creator.ipgo.space' }}" class="cta-button">View Your Product</a>
  </div>
  {% endif %}
</div>
{% endblock %}

{% comment %}
Required payload data example:
{
  "status": "approved",
  "ip_name": "My Awesome IP",
  "product_name": "Limited Edition Figure",
  "rejection_reason": "Product images need higher resolution",
  "product_images": [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg"
  ],
  "product_url": "https://creator.ipgo.space/products/123",
  "reapply_url": "https://creator.ipgo.space/products/123/edit"
}
{% endcomment %}
