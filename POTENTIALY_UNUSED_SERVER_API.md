# Potentially Unused Server API Endpoints

This document lists server API endpoints that appear to be unused based on frontend code analysis. These should be reviewed before removal to ensure they're not called from other sources (external services, webhooks, etc.).

## Analysis Method
- Searched all frontend code for: `$fetch`, `useFetch`, `useDirectusFetch`, `dFetch`
- Checked all components, pages, composables, and utilities
- Cross-referenced with existing server API endpoints

## ❌ CONFIRMED UNUSED ENDPOINTS

### Authentication
- `server/api/auth/refresh.get.ts` - Not used (commented out in useUser.ts)
- `server/api/auth/reset-password.ts` - Not found in frontend

### Creator
- `server/api/creator/ips.get.ts` - Not found in frontend

### Owner
- `server/api/owner/ips/*` - Directory exists but endpoints not used

### Orders
- `server/api/orders/message.post.ts` - Not found in frontend
- `server/api/orders/messages.get.ts` - Not found in frontend

### Payment
- `server/api/payment/create-intent.ts` - Not found in frontend (different from create-intent-for-order)
- `server/api/payment/customer/create.post.ts` - Not found in frontend
- `server/api/payment/method/create.post.ts` - Not found in frontend

### Other
- `server/api/creators/index.get.ts` - Not found in frontend
- `server/api/emails/send-product-application.post.ts` - Not found in frontend
- `server/api/favourites/index.get.ts` - Not found in frontend
- `server/api/products/old-rating/*` - Directory exists but not used
- `server/api/send-email.post.ts` - Not found in frontend
- `server/api/debug.ts` - Only used in debug.vue (development only)

## ✅ CONFIRMED USED ENDPOINTS

### Authentication & User Management
- `server/api/auth/login.post.ts` - Used in Login.vue, SignupForm.vue, Creator/SignupForm.vue
- `server/api/auth/logout.post.ts` - Used in useUser.ts
- `server/api/auth/signup-verification.post.ts` - Used in Otp.vue
- `server/api/auth/verify-code.post.ts` - Used in Otp.vue
- `server/api/signup.ts` - Used in SignupForm.vue, Creator/SignupForm.vue, Owner/SignupForm.vue
- `server/api/me.get.ts` - Used in multiple headers and profile components

### Profile Management
- `server/api/creator/profile.get.ts` - Used in Creator/EditProfileForm.vue
- `server/api/creator/update-profile.patch.ts` - Used in Creator/EditProfileForm.vue
- `server/api/owner/profile.get.ts` - Used in Owner/EditProfileForm.vue
- `server/api/owner/update-profile.patch.ts` - Used in Owner/EditProfileForm.vue
- `server/api/update-profile.patch.ts` - Used in Fan/EditProfileForm.vue

### File Uploads
- `server/api/upload-avatar.post.ts` - Used in Fan/EditProfileForm.vue, Creator/EditProfileForm.vue, Owner/EditProfileForm.vue
- `server/api/upload-product-images.post.ts` - Used in ProductImageDropzone.vue

### Cart & Orders
- `server/api/cart/calculate.post.ts` - Used in fan/cart.vue
- `server/api/orders/create-from-payment.post.ts` - Used in CheckoutModal.vue
- `server/api/orders/index.get.ts` - Used in fan/orders/index.vue
- `server/api/creator/orders/index.get.ts` - Used in creator/orders/index.vue, Creator/Header.vue

### Products & Creator
- `server/api/creator/products/create.post.ts` - Used in Creator/NewProductApplicationForm.vue
- `server/api/creator/products/[id]/reapply.post.ts` - Used in Creator/EditProductForm.vue
- `server/api/creator/performance/charts.get.ts` - Used in creator/analysis.client.vue
- `server/api/creator/performance/products.get.ts` - Used in creator/analysis.client.vue
- `server/api/products/categories.get.ts` - Used in creator/my-products and owner/ip pages

### Owner & IP Management
- `server/api/owner/ip/create.post.ts` - Used in Owner/NewIPForm.vue
- `server/api/owner/ip/[id]/update.patch.ts` - Used in Owner/EditIpForm.vue
- `server/api/owner/performance/charts.get.ts` - Used in owner/analysis.client.vue
- `server/api/owner/performance/creators.get.ts` - Used in owner/analysis.client.vue

### Favorites
- `server/api/favourites/check.post.ts` - Used in products/[id].vue
- `server/api/favourites/add.post.ts` - Used in useFavorites.ts
- `server/api/favourites/remove.post.ts` - Used in useFavorites.ts
- `server/api/favourites/products.get.ts` - Used in fan/favorites.vue

### Payment
- `server/api/payment/create-intent-for-order.post.ts` - Used in CheckoutModal.vue
- `server/api/payment/method/list.get.ts` - Used in usePaymentMethods.ts
- `server/api/payment/method/[id].delete.ts` - Used in usePaymentMethods.ts
- `server/api/payment/method/[id].patch.ts` - Used in Fan/PaymentMethodModal.vue
- `server/api/payment/method/[id]/default.patch.ts` - Used in Fan/PaymentMethodModal.vue
- `server/api/payment/setup-intent/create.post.ts` - Used in Fan/PaymentMethodModal.vue

### Addresses
- `server/api/addresses/list.get.ts` - Used in useAddresses.ts

### IP Categories
- `server/api/ips/categories.get.ts` - Used in Fan/LeftMenu.vue, Fan/MobileLeftMenu.vue, IpCategoriesFilter.vue

### Postcode Lookup
- `server/api/postcode/jp/[postcode].get.ts` - Used in Fan/ShippingAddressModal.vue

### Email Services
- `server/api/emails/send-verification-code.post.ts` - Used in Otp.vue

## Notes

1. **Legacy Order Endpoint Removed**: `server/api/orders/submit.post.ts` was already removed as it was confirmed unused legacy code.

2. **External API Calls**: Fan/RecoverPassword.vue makes calls to external APIs (`${config.public.apiBase}/auth/...`) which are not local server endpoints.

3. **Debug Endpoint**: `server/api/debug.ts` is only used in debug.vue for development purposes.

4. **Review Needed**: Before removing any endpoints, verify they're not:
   - Called by external services or webhooks
   - Used in server-side code or middleware
   - Required for future features
   - Used in tests or other non-frontend code

## Recommendation

Start by removing the most obviously unused endpoints (debug, old-rating directory, etc.) and monitor for any issues before removing others.
