#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to add a new entry to all i18n language files
 * Usage: node scripts/add-i18n-entry.js <key> <en_value> [ja_value] [ko_value]
 *
 * If ja_value or ko_value are not provided, "TBD" will be used as a placeholder
        placeholder-class="blur-xs" * If the key already exists, you will be prompted for confirmation before overwriting
 */

import { existsSync, readFileSync, writeFileSync } from 'fs';
import { join } from 'path';
import { createInterface } from 'readline';

// Get command line arguments
const args = process.argv.slice(2);

// Check if we have at least a key and English value
if (args.length < 2) {
  console.log('Usage: node scripts/add-i18n-entry.js <key> <en_value> [ja_value] [ko_value]');
  console.log('Example: node scripts/add-i18n-entry.js welcome_message "Welcome to our site" "私たちのサイトへようこそ" "우리 사이트에 오신 것을 환영합니다"');
  console.log('Note: If Japanese or Korean translations are not provided, "TBD" will be used as a placeholder');
  process.exit(1);
}

// Extract values from arguments, use "TBD" as default for missing translations
const key = args[0];
const enValue = args[1];
const jaValue = args.length >= 3 ? args[2] : "TBD";
const koValue = args.length >= 4 ? args[3] : "TBD";

// Paths to language files
const enFile = join(process.cwd(), 'i18n/locales/en.json');
const jaFile = join(process.cwd(), 'i18n/locales/ja.json');
const koFile = join(process.cwd(), 'i18n/locales/ko.json');

// Check if files exist
if (!existsSync(enFile) || !existsSync(jaFile) || !existsSync(koFile)) {
  console.error('Error: One or more language files not found.');
  console.error('Make sure you\'re running this script from the project root directory.');
  process.exit(1);
}

/**
 * Create a readline interface for user input
 */
const rl = createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Ask a yes/no question and get a promise for the answer
 * @param {string} question - The question to ask
 * @returns {Promise<boolean>} - Promise resolving to true for yes, false for no
 */
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(`${question} (y/n): `, (answer) => {
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

/**
 * Check if a key already exists in any of the language files
 * @param {string} key - The key to check
 * @returns {Promise<boolean>} - Promise resolving to true if it's safe to proceed
 */
async function checkExistingKey(key) {
  try {
    // Read all language files
    const enData = JSON.parse(readFileSync(enFile, 'utf8'));
    const jaData = JSON.parse(readFileSync(jaFile, 'utf8'));
    const koData = JSON.parse(readFileSync(koFile, 'utf8'));

    // Check if the key exists in any file
    const existsInEn = enData.global && enData.global[key] !== undefined;
    const existsInJa = jaData.global && jaData.global[key] !== undefined;
    const existsInKo = koData.global && koData.global[key] !== undefined;

    if (existsInEn || existsInJa || existsInKo) {
      console.log(`⚠️ Warning: The key "${key}" already exists in one or more language files:`);

      if (existsInEn) console.log(`  - English: "${enData.global[key]}"`);
      if (existsInJa) console.log(`  - Japanese: "${jaData.global[key]}"`);
      if (existsInKo) console.log(`  - Korean: "${koData.global[key]}"`);

      const shouldOverwrite = await askQuestion('Do you want to overwrite the existing translations?');

      if (!shouldOverwrite) {
        console.log('Operation cancelled by user.');
        rl.close();
        process.exit(0);
      }

      return true;
    }

    return true;
  } catch (error) {
    console.error('Error checking for existing keys:', error.message);
    return false;
  }
}

/**
 * Add entry to a language file
 * @param {string} filePath - Path to the language file
 * @param {string} key - The key to add
 * @param {string} value - The value to add
 * @param {string} lang - Language name for logging
 * @returns {boolean} - Success status
 */
function addEntry(filePath, key, value, lang) {
  try {
    // Read the file
    const fileContent = readFileSync(filePath, 'utf8');

    // Parse JSON
    const data = JSON.parse(fileContent);

    // Add the new entry to the global object
    data.global[key] = value;

    // Write the updated content back to the file
    writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');

    console.log(`✅ Added "${key}": "${value}" to ${lang} file. Path: ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error: Failed to add entry to ${lang} file.`);
    console.error(error.message);
    return false;
  }
}

// Main function to run the script
async function main() {
  try {
    // Check if the key already exists and confirm overwrite if needed
    const canProceed = await checkExistingKey(key);

    if (!canProceed) {
      console.error('Cannot proceed due to errors checking existing keys.');
      process.exit(1);
    }

    // Add entries to each language file
    const enSuccess = addEntry(enFile, key, enValue, 'English');
    const jaSuccess = addEntry(jaFile, key, jaValue, 'Japanese');
    const koSuccess = addEntry(koFile, key, koValue, 'Korean');

    if (enSuccess && jaSuccess && koSuccess) {
      console.log('');
      console.log(`✨ Successfully added "${key}" to all language files!`);

      // Show a reminder if we used TBD placeholders
      if (args.length < 3) {
        console.log('⚠️ Japanese translation is using "TBD" placeholder. Remember to update it later.');
      }
      if (args.length < 4) {
        console.log('⚠️ Korean translation is using "TBD" placeholder. Remember to update it later.');
      }

      console.log(`To use this in your application, reference it with: ${key}`);
    } else {
      console.error('');
      console.error('⚠️ There were errors adding the entry to one or more files.');
      process.exit(1);
    }
  } catch (error) {
    console.error('Unexpected error:', error.message);
    process.exit(1);
  } finally {
    // Always close the readline interface
    rl.close();
  }
}

// Run the main function
main();
