name: Deploy to VPS

on:
  push:
    branches: [ main, production ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

jobs:
  # Check for changes to determine what needs to be deployed
  changes:
    runs-on: ubuntu-latest
    outputs:
      directus: ${{ steps.changes.outputs.directus }}
      nuxt: ${{ steps.changes.outputs.nuxt }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check for changes
        uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            directus:
              - 'directus/**'
            nuxt:
              - '**'
              - '!directus/**'
              - '!migrate-scripts/**'

  deploy-directus:
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.directus == 'true'
    environment: ${{ github.ref == 'refs/heads/production' && 'production' || (github.event.inputs.environment || 'staging') }}

    steps:
    - name: Setup SSH key
      run: |
        # Check if required secrets are set
        if [ -z "${{ secrets.VPS_HOST }}" ]; then
          echo "Error: VPS_HOST secret is not set"
          exit 1
        fi
        if [ -z "${{ secrets.VPS_USER }}" ]; then
          echo "Error: VPS_USER secret is not set"
          exit 1
        fi
        if [ -z "${{ secrets.VPS_SSH_PRIVATE_KEY }}" ]; then
          echo "Error: VPS_SSH_PRIVATE_KEY secret is not set"
          exit 1
        fi

        # Setup SSH
        mkdir -p ~/.ssh
        echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.VPS_HOST }} >> ~/.ssh/known_hosts

        echo "SSH setup completed for ${{ secrets.VPS_HOST }}"

    - name: Deploy Directus
      run: |
        echo "Deploying Directus to: ${{ secrets.VPS_HOST }}"

        # Execute Directus deployment commands on VPS
        ssh ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
          # Navigate to app directory
          cd ~/ipgo-nuxt || { echo "App directory not found"; exit 1; }

          # Update and restart Directus
          echo "Directus changes detected, updating..."
          if [ -d "directus" ]; then
            cd directus
            git pull
            docker compose restart
            echo "Directus updated and restarted successfully!"
            cd ..
          else
            echo "Directus directory not found"
            exit 1
          fi
        EOF

  build-and-deploy:
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.nuxt == 'true'
    environment: ${{ github.ref == 'refs/heads/production' && 'production' || (github.event.inputs.environment || 'staging') }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
      with:
        bun-version: latest

    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/.bun/install/cache
        key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lock') }}
        restore-keys: |
          ${{ runner.os }}-bun-

    - name: Install dependencies
      run: bun install

    - name: Build application
      env:
        NUXT_UI_PRO_LICENSE: ${{ secrets.NUXT_UI_PRO_LICENSE }}
        NODE_OPTIONS: "--max-old-space-size=8192"
        NITRO_PRESET: "node_cluster"
      run: bun run build

    - name: Create deployment archive
      run: |
        tar -czf deployment.tar.gz \
          .output \
          package.json \
          bun.lock

    - name: Setup SSH key
      run: |
        # Check if required secrets are set
        if [ -z "${{ secrets.VPS_HOST }}" ]; then
          echo "Error: VPS_HOST secret is not set"
          exit 1
        fi
        if [ -z "${{ secrets.VPS_USER }}" ]; then
          echo "Error: VPS_USER secret is not set"
          exit 1
        fi
        if [ -z "${{ secrets.VPS_SSH_PRIVATE_KEY }}" ]; then
          echo "Error: VPS_SSH_PRIVATE_KEY secret is not set"
          exit 1
        fi

        # Setup SSH
        mkdir -p ~/.ssh
        echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.VPS_HOST }} >> ~/.ssh/known_hosts

        echo "SSH setup completed for ${{ secrets.VPS_HOST }}"

    - name: Deploy to VPS
      run: |
        echo "Target server: ${{ secrets.VPS_HOST }}"

        # Upload files to VPS
        scp deployment.tar.gz ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }}:~/

        # Execute deployment commands on VPS
        ssh ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
          # Source NVM to make node, npm, and pm2 available
          export NVM_DIR="$HOME/.nvm"
          [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
          [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

          # Verify Node.js tools are available
          echo "Node version: $(node --version 2>/dev/null || echo 'not found')"
          echo "NPM version: $(npm --version 2>/dev/null || echo 'not found')"
          echo "PM2 status: $(pm2 --version 2>/dev/null || echo 'not found')"

          # Navigate to app directory
          cd ~/ipgo-nuxt || { echo "App directory not found"; exit 1; }

          # Backup current deployment
          if [ -d ".output" ]; then
            mv .output .output.backup.$(date +%Y%m%d_%H%M%S)
          fi

          # Extract new deployment
          tar -xzf ~/deployment.tar.gz

          # No need to install dependencies - .output folder contains everything needed

          # Restart PM2 process
          if command -v pm2 &> /dev/null; then
            echo "Restarting PM2 process..."
            pm2 restart ipgo-nuxt || echo "PM2 process not found, assuming ecosystem.config.cjs exists on server"
            pm2 save
          else
            echo "Error: PM2 not found. Please ensure PM2 is installed globally."
            exit 1
          fi

          # Clean up
          rm ~/deployment.tar.gz

          # Remove old backups (keep last 3)
          ls -t .output.backup.* 2>/dev/null | tail -n +4 | xargs rm -rf 2>/dev/null || true

          echo "Deployment completed successfully!"
        EOF
