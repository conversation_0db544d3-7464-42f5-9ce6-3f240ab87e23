# IPGO - Where Creativity Meets Opportunity

> **日本語版**: [README.ja.md](./README.ja.md) | **한국어**: Coming Soon

<div align="center">
  <img src="public/images/logo-w-text.svg" alt="IPGO Logo" width="200"/>

**A revolutionary platform connecting fans, creators, and IP owners**

[![Nuxt 3](https://img.shields.io/badge/Nuxt-3.17.3-00DC82?logo=nuxt.js)](https://nuxt.com/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.6.3-3178C6?logo=typescript)](https://www.typescriptlang.org/)
[![Directus](https://img.shields.io/badge/Directus-19.1.0-6644FF?logo=directus)](https://directus.io/)
[![Vue 3](https://img.shields.io/badge/Vue-3.5.13-4FC08D?logo=vue.js)](https://vuejs.org/)

</div>

## 🌟 About IPGO

IPGO is a groundbreaking platform designed to bring fans closer to the intellectual properties (IPs) they love. Through collaboration with creators and IP owners, IPGO enables fans to access unique, high-quality creations while providing creators with opportunities to monetize their talents and IP owners with new revenue streams.

### 🎯 Mission

To revolutionize fan engagement by offering a seamless way for fans to connect with their favorite franchises, explore exclusive creations, and participate in a vibrant community celebrating creativity and shared passion.

### 🏗️ Platform Architecture

IPGO is a multi-currency, multi-role, multi-language platform consisting of 4 main sections:

- **Directus CMS**

  - Content management and data storage
  - Headless CMS for flexible content delivery
  - Customizable with Directus extensions

- **🛍️ Fan Section**

  - Browse and purchase unique IP-licensed products
  - Discover creators and their works
  - Manage favorites, cart, and orders
  - Multi-language support (English, Japanese, Korean)
  - ✅ Payment processing with Stripe integration
  - ✅ Order management and tracking
  - ✅ Address book and payment methods
  - ✅ Product reviews and ratings
  - ✅ Real-time chat with creators

- **🎨 Creator Section**

  - ✅ Create and manage product listings
  - ✅ Apply for IP licensing
  - ✅ Track sales and analytics with charts
  - ✅ Communicate with fans through order chat
  - ✅ Order fulfillment and shipping management
  - ✅ Revenue and quantity analytics dashboard

- **👑 Owner Section**
  - ✅ Manage IP portfolios
  - ✅ Review and approve creator applications
  - ✅ Monitor IP usage and revenue
  - ✅ Analytics dashboard with creator performance
  - ✅ IP activation/deactivation controls

## 🚀 Quick Start

### Prerequisites

- Node.js 22+ or Bun
- PostgreSQL database
- Directus CMS instance
- AWS SES (for email functionality)
- Stripe account (for payment processing)

### Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/rafazafar/ipgo-nuxt.git
   cd ipgo-nuxt
   ```

2. **Install dependencies**

   ```bash
   # Using bun (recommended)
   bun install

   # Or using npm/pnpm/yarn
   npm install
   ```

3. **Environment Setup**

   ```bash
   cp .env.example .env
   ```

   Configure your environment variables:

   ```env
   # Directus Configuration
   DIRECTUS_ADMIN_KEY=your_directus_admin_key
   NUXT_PUBLIC_DIRECTUS_BASE_URL=https://your-directus-instance.com

   # AWS SES Configuration
   AWS_REGION=ap-northeast-1
   AWS_ACCESS_KEY_ID=your_aws_access_key
   AWS_SECRET_ACCESS_KEY=your_aws_secret_key

   # Email Configuration
   EMAIL_DEFAULT_FROM=<EMAIL>
   DOMAIN_URL=http://localhost:3000

   # Stripe Configuration
   NUXT_STRIPE_PUBLIC_KEY=your_stripe_public_key
   STRIPE_SECRET_KEY=your_stripe_secret_key
   ```

4. **Start Development Server**

   ```bash
   bun run dev
   ```

   Visit `http://localhost:3000` to see the application.

## 🛠️ Technology Stack

### Frontend

- **[Nuxt 3](https://nuxt.com/)** - Vue.js framework with SSR/SSG
- **[Vue 3](https://vuejs.org/)** - Progressive JavaScript framework
- **[TypeScript](https://www.typescriptlang.org/)** - Type-safe JavaScript
- **[Nuxt UI Pro](https://ui.nuxt.com/pro)** - Premium UI components
- **[Tailwind CSS](https://tailwindcss.com/)** - Utility-first CSS framework

### Backend & CMS

- **[Directus](https://directus.io/)** - Headless CMS and API
- **[PostgreSQL](https://www.postgresql.org/)** - Primary database
- **[Kysely](https://kysely.dev/)** - Type-safe SQL query builder

### Internationalization

- **[@nuxtjs/i18n](https://i18n.nuxtjs.org/)** - Multi-language support
- **Languages**: English, Japanese (日本語), Korean (한국어)

### Payment & Communication

- **[Stripe](https://stripe.com/)** - Payment processing and financial transactions
- **[AWS SES](https://aws.amazon.com/ses/)** - Email delivery service

### Additional Features

- **[Nuxt Image](https://image.nuxt.com/)** - Optimized image handling
- **[Vue Easy Lightbox](https://github.com/XiongAmao/vue-easy-lightbox)** - Image galleries
- **[Nuxt Charts](https://github.com/nuxt-modules/charts)** - Data visualization
- **[Vue3 Star Ratings](https://github.com/craigh411/vue3-star-ratings)** - Product rating system
- **[FormKit Auto-animate](https://auto-animate.formkit.com/)** - Smooth animations

## 📁 Project Structure

```
ipgo-nuxt/
├── app/                          # Nuxt 3 application code
│   ├── components/               # Vue components
│   │   ├── Fan/                 # Fan-specific components
│   │   ├── Creator/             # Creator-specific components
│   │   ├── Owner/               # Owner-specific components
│   │   └── UI/                  # Reusable UI components
│   ├── composables/             # Vue composables
│   ├── layouts/                 # Page layouts
│   ├── middleware/              # Route middleware
│   ├── pages/                   # File-based routing
│   │   ├── fan/                # Fan section pages
│   │   ├── creator/            # Creator section pages
│   │   ├── owner/              # Owner section pages
│   │   ├── products/           # Product pages
│   │   ├── ip/                 # IP browsing pages
│   │   └── creators/           # Creator discovery pages
│   ├── plugins/                # Nuxt plugins
│   └── utils/                  # Utility functions
├── directus/                    # Directus CMS configuration
│   ├── extensions/             # Custom Directus extensions
│   └── migration-scripts/      # Database migration scripts
├── docs/                       # Documentation
├── i18n/                       # Internationalization files
│   └── locales/               # Translation files
├── server/                     # Nuxt server API
│   └── api/                   # API endpoints
├── shared/                     # Shared types and utilities
│   ├── types/                 # TypeScript type definitions
│   └── utils/                 # Shared utility functions
└── tests/                      # Test files
    └── e2e/                   # End-to-end tests
```

## 🌐 Features

### ✅ Completed Features

#### Fan Section
- **Multi-language Support** - English, Japanese, Korean with automatic detection
- **Product Browsing** - Browse IP-licensed products with advanced filtering and search
- **Creator Discovery** - Explore creators and their portfolios
- **IP Exploration** - Discover intellectual properties by category
- **Shopping Cart** - Add products, manage quantities, grouped by creator
- **Favorites/Wishlist** - Save favorite products for later purchase
- **User Authentication** - Secure login/signup with email verification and OTP
- **Payment Processing** - Stripe integration with saved payment methods
- **Order Management** - Complete order lifecycle from cart to completion
- **Address Book** - Manage multiple shipping addresses
- **Product Reviews** - Rate and review purchased products
- **Real-time Chat** - Communicate with creators through order chat
- **Responsive Design** - Mobile-first responsive interface
- **Image Optimization** - Optimized image loading and galleries

#### Creator Section
- **Product Management** - Create, edit, and manage product listings
- **IP Application** - Apply for licensing of intellectual properties
- **Order Fulfillment** - Process orders and mark as shipped
- **Analytics Dashboard** - Revenue and sales analytics with charts
- **Creator Chat** - Communicate with customers through orders
- **Profile Management** - Manage creator profile and portfolio

#### Owner Section
- **IP Portfolio Management** - Create, edit, and manage IP properties
- **Creator Application Review** - Approve/reject creator licensing applications
- **Revenue Analytics** - Monitor IP performance and creator revenue
- **IP Controls** - Activate/deactivate IP properties
- **Creator Performance** - Track and compare creator performance

#### Technical Features
- **Multi-currency Support** - USD, JPY, KRW with automatic conversion
- **Role-based Access** - Separate interfaces for fans, creators, and owners
- **Real-time Updates** - Live data updates across the platform
- **Security** - JWT authentication, CSRF protection, input validation
- **Performance** - Optimized loading, caching, and image delivery

### 🔄 In Development

- **Advanced Search** - Enhanced search with filters and suggestions
- **Notification System** - Push notifications for order updates
- **Mobile App** - Native mobile applications
- **Advanced Analytics** - More detailed reporting and insights

## 🚀 Development

### Available Scripts

```bash
# Development
bun run dev              # Start development server
bun run build            # Build for production
bun run preview          # Preview production build
bun run generate         # Generate static site

# Utilities
bun run add:t            # Add new i18n translation entry

# Testing
bun run test:e2e         # Run end-to-end tests
```

### Development Guidelines

1. **Code Style**

   - Use TypeScript for type safety
   - Follow Vue 3 Composition API patterns
   - Prefer composables over mixins
   - Use generic reusable functions when possible

2. **Component Organization**

   - Place section-specific components in respective folders (`Fan/`, `Creator/`, `Owner/`)
   - Use `UI/` folder for reusable components
   - Follow PascalCase naming convention

3. **State Management**

   - Use composables for state management
   - Leverage Nuxt's built-in state management
   - Avoid complex state management libraries unless necessary

4. **Internationalization**
   - Add translations to all three language files (en.json, ja.json, ko.json)
   - Use the `add:t` script for new translation entries
   - Test language switching functionality
   - Use translation helper utils for dynamic content

5. **Data Management**
   - Use `useDirectusFetch` for client-side data fetching
   - Use `dFetch` for server-side operations requiring elevated permissions
   - Implement proper error handling and loading states
   - Follow the archive-and-create pattern for product updates

### API Integration

The application integrates with multiple services:

- **Directus CMS** - Content management, data storage, and API
- **Stripe** - Payment processing and financial transactions
- **AWS SES** - Email delivery service for notifications and verification
- **Custom Server API** - Business logic, authentication, and specialized endpoints

Key integration patterns:
- Client-side: `useDirectusFetch` for standard data operations
- Server-side: `dFetch` and `elevatedFetch` for admin operations
- Payment: Stripe Elements for secure payment processing
- Email: AWS SES with custom templates for user communications

See [API Documentation](./APIs.md) for detailed endpoint information.

## 📚 Documentation

### Core Documentation

- [📋 Project Overview](./docs/project-overview.md) - Business model, vision, and roadmap
- [🏗️ Architecture](./docs/architecture.md) - Technical architecture and data model
- [💻 Development Guide](./docs/development.md) - Development setup and guidelines
- [🚀 Deployment Guide](./docs/deployment.md) - Production deployment instructions

### API & Integration

- [🔗 API Reference](./APIs.md) - Quick API reference guide
- [📊 Detailed API Docs](./docs/api.md) - Comprehensive API documentation

### Features & Utilities

- [📧 Email System](./docs/email.md) - Email utilities and templates
- [🌐 Translations](./docs/translations.md) - Internationalization guide
- [🧪 Testing Guide](./E2E_CHECKLIST.md) - End-to-end testing checklist
- [💳 Payment Integration](./docs/payments.md) - Stripe integration guide

### Environment Configuration

For production deployment, ensure these environment variables are set:

```env
# Production Directus
NUXT_PUBLIC_DIRECTUS_BASE_URL=https://your-production-directus.com
DIRECTUS_ADMIN_KEY=your_production_admin_key

# Production API
NUXT_PUBLIC_API_BASE=https://your-production-api.com
NUXT_PUBLIC_DOMAIN_URL=https://your-production-domain.com

# AWS SES (Production)
AWS_REGION=your_aws_region
AWS_ACCESS_KEY_ID=your_production_access_key
AWS_SECRET_ACCESS_KEY=your_production_secret_key

# Stripe (Production)
NUXT_STRIPE_PUBLIC_KEY=your_production_stripe_public_key
STRIPE_SECRET_KEY=your_production_stripe_secret_key
```

### Deployment Platforms

The application can be deployed to various platforms:

- **Cloudflare Workers** (Current setup)
- **Vercel**
- **Netlify**
- **AWS Amplify**
- **Traditional VPS/Docker**

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup for Contributors

1. Follow the installation steps above
2. Create a `.env` file with development configuration
3. Set up a local Directus instance or use the staging environment
4. Configure Stripe test keys for payment testing
5. Set up AWS SES for email testing (or use staging credentials)
6. Run the development server and start coding!

### Code Quality

- Follow TypeScript best practices
- Write comprehensive tests for new features
- Use the E2E checklist for testing user flows
- Ensure mobile responsiveness
- Test across all supported languages

## 📄 License

This project is proprietary software developed by SMG (Super Massive Global).

## 🆘 Support

For support and questions:

- **Technical Issues**: Create an issue in this repository
- **Business Inquiries**: Contact [SMG](https://supermassiveglobal.co.jp/)
- **Documentation**: Check the [docs](./docs/) folder

---

<div align="center">
  <p>Built with ❤️ by <a href="https://supermassiveglobal.co.jp/">Super Massive Global</a></p>
  <img src="public/images/logo-massive.svg" alt="SMG Logo" width="100"/>
</div>
