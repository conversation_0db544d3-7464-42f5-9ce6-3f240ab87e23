<template>
  <NuxtLoadingIndicator color="#ff0055" />
  <div class="video-background-container min-h-screen" >
    <!-- Video Background -->
    <video
      class="video-background"
      autoplay
      muted
      playsinline
      loop
    >
      <source src="/videos/login-bg.mp4" type="video/mp4">
      <!-- Fallback to image if video fails to load -->
      <img src="/images/fan-login-bg.png" alt="Background" class="fallback-image">
    </video>

    <!-- Content Overlay -->
    <div class="content-overlay">
      <LangSwitcher class="absolute z-10 top-2 right-2" />
      <div>
        <slot />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup></script>

<style scoped>
.video-background-container {
  position: relative;
  overflow: hidden;
}

.video-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

.fallback-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

.content-overlay {
  position: relative;
  z-index: 1;
  min-height: 100vh;
}
</style>
