<template>
  <NuxtLoadingIndicator color="#ff0055" />
  <FanHeader />
  <UContainer>
    <UPage class="min-h-screen">
      <UPageBody>
        <div class="flex flex-col gap-4">
          <!-- Fan profile card -->
          <FanProfileCard :profile="profile!" />
          <!-- Navigation Tabs (My Cart, Order List, My Favorites) -->
          <div id="navigation-tabs" class="mt-8 flex flex-row gap-4 text-xl font-bold">
            <ULink :to="localePath('fan-cart')">
              {{ $t('global.my_cart') }}
            </ULink>
            <ULink :to="localePath('fan-orders')">
              {{ $t('global.order_list') }}
            </ULink>
            <ULink :to="localePath('fan-favorites')">
              {{ $t('global.my_favorites') }}
            </ULink>
          </div>
          <slot />
        </div>
      </UPageBody>
    </UPage>
  </UContainer>
  <FanFooter />
</template>

<script setup lang="ts">
const { logout, profile } = useUser();
const localePath = useLocalePath();
// Get User Profile Data
const { data: profileData, error } = await useFetch('/api/me', {
  key: 'profile',
});
profile.value = profileData.value || null;
if (error.value) {
  await logout();
  navigateTo(localePath('index'));
}
</script>
