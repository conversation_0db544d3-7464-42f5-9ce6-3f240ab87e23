<template>
  <NuxtLoadingIndicator color="#ff0055" />
  <div class="bg-cherry min-h-screen">
    <LangSwitcher class="absolute z-10 top-2 right-2" />
    <div>
      <slot />
    </div>
  </div>
</template>

<script lang="ts" setup></script>

<style scoped>
.bg-cherry {
  background: url('/images/fan-login-bg.png') no-repeat center center fixed;
  background-size: cover;
  background-attachment: fixed;
  position: relative;
}

/* Ensure background stays fixed on iOS devices which have issues with background-attachment: fixed */
@supports (-webkit-overflow-scrolling: touch) {
  .bg-cherry::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('/images/fan-login-bg.png') no-repeat center center;
    background-size: cover;
    z-index: -1;
  }
}
</style>
