/**
 * Translation utilities for working with Directus translations
 *
 * This file provides helper functions to work with reactive translations
 * that automatically update when the i18n locale changes.
 */

import type { Languages } from '../../shared/types/directus';

// Mapping from i18n locale codes to Directus language IDs
export const localeToLanguageId = (localeCode: string): number => {
  switch (localeCode) {
    case 'en':
      return 1; // English
    case 'jp':
      return 2; // Japanese
    case 'kr':
      return 3; // Korean
    default:
      return 1; // Default to English
  }
};

// Helper to extract language ID from languages_id field (handles both number and Languages object)
export const getLanguageId = (languagesId: number | Languages | null | undefined): number | null => {
  if (typeof languagesId === 'number') {
    return languagesId;
  }
  if (languagesId && typeof languagesId === 'object' && 'id' in languagesId) {
    return languagesId.id;
  }
  return null;
};

/**
 * Generic reactive translation helper
 *
 * @param translations - Array of translation objects
 * @param field - Field to extract from translation ('name' or 'description')
 * @param fallbackLocale - Locale to fallback to if current locale not found
 * @returns Computed ref that updates when locale changes
 */
export const useTranslation = <
  T extends {
    languages_id?: number | Languages | null;
    name?: string | null;
    description?: string | null;
    html?: string | null;
  },
>(
  translations: T[],
  field: keyof T = 'name' as keyof T,
  fallbackLocale: string = 'en',
) => {
  const { locale } = useI18n();

  return computed(() => {
    if (!translations || translations.length === 0) {
      return '';
    }

    const currentLanguageId = localeToLanguageId(locale.value);

    // Try to find translation for current language
    let translation = translations.find((t) => getLanguageId(t.languages_id) === currentLanguageId);

    // If not found, try fallback language
    if (!translation || !translation[field]) {
      const fallbackLanguageId = localeToLanguageId(fallbackLocale);
      translation = translations.find((t) => getLanguageId(t.languages_id) === fallbackLanguageId);
    }

    // If still not found, get first available translation
    if (!translation || !translation[field]) {
      translation = translations.find((t) => t[field]);
    }

    return (translation?.[field] as string) || '';
  });
};

/**
 * Get reactive translated name (works with any translation type)
 */
export const useTranslatedName = <T extends { languages_id?: number | Languages | null; name?: string | null }>(
  translations: T[],
) => {
  return useTranslation(translations, 'name');
};

/**
 * Get reactive translated description (works with any translation type)
 */
export const useTranslatedDescription = <
  T extends { languages_id?: number | Languages | null; description?: string | null },
>(
  translations: T[],
) => {
  return useTranslation(translations, 'description');
};

export const useTranslatedHtml = <T extends { languages_id?: number | Languages | null; html?: string | null }>(
  translations: T[],
) => {
  return computed(() => {
    const htmlContent = useTranslation(translations, 'html').value;
    return htmlContent ? htmlContent.replace(/<br\s*\/?>/g, '') : '';
  });
};
