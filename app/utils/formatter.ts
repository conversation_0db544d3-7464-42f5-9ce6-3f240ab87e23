export const priceFormatter = (price: number) => {
  return price.toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

export const currencyPriceFormatter = (price: number, currency: string = 'USD') => {
  const currencyCode = currency.toUpperCase();

  // Handle different currency formatting
  switch (currencyCode) {
    case 'JPY':
      // Round to whole number and format without decimals
      const roundedJpy = Math.round(price);
      return new Intl.NumberFormat('ja-JP', {
        style: 'currency',
        currency: 'JPY',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(roundedJpy);
    case 'KRW':
      // Round to whole number and format without decimals
      const roundedKrw = Math.round(price);
      return new Intl.NumberFormat('ko-KR', {
        style: 'currency',
        currency: 'KRW',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(roundedKrw);
    case 'USD':
    default:
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(price);
  }
};

// Product pricing utility functions
/**
 * Get the appropriate price based on product's base currency
 */
export const getProductCurrentPrice = (product: Products): number => {
  switch (product.base_currency?.toUpperCase()) {
    case 'JPY':
      return product.price_jpy || 0;
    case 'KRW':
      return product.price_krw || 0;
    case 'USD':
    default:
      return product.price || 0;
  }
};

/**
 * Get the appropriate price based on user's locale
 */
export const getProductPriceByLocale = (product: Products, locale: string): number => {
  switch (locale) {
    case 'jp':
      return product.price_jpy || product.price || 0;
    case 'kr':
      return product.price_krw || product.price || 0;
    case 'en':
    default:
      return product.price || 0;
  }
};

/**
 * Get the appropriate currency code based on user's locale
 */
export const getCurrencyByLocale = (locale: string): string => {
  switch (locale) {
    case 'jp':
      return 'JPY';
    case 'kr':
      return 'KRW';
    case 'en':
    default:
      return 'USD';
  }
};

/**
 * Check if product has a meaningful discount
 */
export const hasProductDiscount = (product: Products): boolean => {
  return !!(product.discount && product.discount > 0);
};

/**
 * Calculate discounted price based on the current currency
 */
export const getProductDiscountedPrice = (product: Products): number => {
  const currentPrice = getProductCurrentPrice(product);

  if (!hasProductDiscount(product) || !currentPrice) return currentPrice;

  // If discount_percentage exists, use percentage-based discount
  if (product.discount_percentage && product.discount_percentage > 0) {
    const discountAmount = (currentPrice * product.discount_percentage) / 100;
    return currentPrice - discountAmount;
  }

  // Otherwise use absolute discount amount
  return currentPrice - (product.discount || 0);
};

// Rating aggregation utility functions
/**
 * Compute aggregated ratings from an array of ratings
 * Returns count and percentage for each rating level (1-5 stars)
 */
export const calculateAggregatedRatings = (ratings: number[]): {
  total: number;
  1: { count: number; percentage: number };
  2: { count: number; percentage: number };
  3: { count: number; percentage: number };
  4: { count: number; percentage: number };
  5: { count: number; percentage: number };
} => {
  const total = ratings.length;

  // Initialize counts for each rating level
  const counts = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };

  // Count occurrences of each rating
  ratings.forEach(rating => {
    const roundedRating = Math.round(rating) as 1 | 2 | 3 | 4 | 5;
    if (roundedRating >= 1 && roundedRating <= 5) {
      counts[roundedRating]++;
    }
  });

  // Calculate percentages and return formatted result
  return {
    total,
    1: {
      count: counts[1],
      percentage: total > 0 ? Math.round((counts[1] / total) * 100) : 0,
    },
    2: {
      count: counts[2],
      percentage: total > 0 ? Math.round((counts[2] / total) * 100) : 0,
    },
    3: {
      count: counts[3],
      percentage: total > 0 ? Math.round((counts[3] / total) * 100) : 0,
    },
    4: {
      count: counts[4],
      percentage: total > 0 ? Math.round((counts[4] / total) * 100) : 0,
    },
    5: {
      count: counts[5],
      percentage: total > 0 ? Math.round((counts[5] / total) * 100) : 0,
    },
  };
};

/**
 * Format product's current price with appropriate currency
 */
export const formatProductCurrentPrice = (product: Products): string => {
  const currentPrice = getProductCurrentPrice(product);
  if (!currentPrice) return '';
  return currencyPriceFormatter(currentPrice, product.base_currency || 'USD');
};

/**
 * Format product's discounted price with appropriate currency
 */
export const formatProductDiscountedPrice = (product: Products): string => {
  const discountedPrice = getProductDiscountedPrice(product);
  if (!discountedPrice) return '';
  return currencyPriceFormatter(discountedPrice, product.base_currency || 'USD');
};

/**
 * Format product's current price based on user's locale
 */
export const formatProductCurrentPriceByLocale = (product: Products, locale: string): string => {
  const currentPrice = getProductPriceByLocale(product, locale);
  if (!currentPrice) return '';
  const currency = getCurrencyByLocale(locale);
  return currencyPriceFormatter(currentPrice, currency);
};

/**
 * Calculate discounted price based on locale-specific pricing
 */
export const getProductDiscountedPriceByLocale = (product: Products, locale: string): number => {
  const currentPrice = getProductPriceByLocale(product, locale);

  if (!hasProductDiscount(product) || !currentPrice) return currentPrice;

  // If discount_percentage exists, use percentage-based discount
  if (product.discount_percentage && product.discount_percentage > 0) {
    const discountAmount = (currentPrice * product.discount_percentage) / 100;
    return currentPrice - discountAmount;
  }

  // Otherwise use absolute discount amount
  return currentPrice - (product.discount || 0);
};

/**
 * Format product's discounted price based on user's locale
 */
export const formatProductDiscountedPriceByLocale = (product: Products, locale: string): string => {
  const discountedPrice = getProductDiscountedPriceByLocale(product, locale);
  if (!discountedPrice) return '';
  const currency = getCurrencyByLocale(locale);
  return currencyPriceFormatter(discountedPrice, currency);
};
