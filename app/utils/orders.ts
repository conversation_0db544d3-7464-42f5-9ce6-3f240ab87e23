/**
 * Utility functions for order operations
 */

// Type definition for product review data
export interface ProductReviewData {
  productId: number;
  rating: number;
  message: string;
}

// Type definition for order completion parameters
export interface CompleteOrderParams {
  orderId: number;
  productReviews: ProductReviewData[];
}

/**
 * Complete an order by creating reviews and updating order status
 */
export async function completeOrder({ orderId, productReviews }: CompleteOrderParams) {
  const { dFetch } = useDirectus();

  try {
    // Create reviews for each product
    const reviewPromises = productReviews.map(async (review) => {
      return await dFetch('/items/review', {
        method: 'POST',
        body: {
          order: orderId,
          product: review.productId,
          rating: review.rating,
          text: review.message,
          status: 'published',
        },
      });
    });

    // Wait for all reviews to be created
    await Promise.all(reviewPromises);

    // Mark order as completed
    await dFetch(`/items/orders/${orderId}`, {
      method: 'PATCH',
      body: {
        status: 'completed',
      },
    });

    return { success: true };
  } catch (error) {
    console.error('Error completing order:', error);
    throw error;
  }
}
