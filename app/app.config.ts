export default defineAppConfig({
  toaster: {
    position: 'bottom-right' as const,
    expand: true,
    duration: 3000,
  },
  ui: {
    colors: {
      primary: 'red',
      neutral: 'zinc',
      error: 'yellow',
    },
    button: {
      slots: {
        base: '!text-white !cursor-pointer',
      },
      variants:{
        color: {
          warning: 'text-neutral!',
          success: 'text-neutral!'
        }
      }
    },
    carousel: {
      variants: {
        active: {
          true: {
            dot: 'bg-primary-500',
          },
        },
      },
    },
    modal: {
      slots:{
        header: 'border-b-0'
      }
    },
  },
  toast: {
    position: 'bottom-left',
  },
});
