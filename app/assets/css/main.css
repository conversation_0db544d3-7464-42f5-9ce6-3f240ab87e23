@import 'tailwindcss';
@import '@nuxt/ui-pro';
@import 'transition-style';

@utility no-scrollbar {
  @apply [scrollbar-width:none] [&::-webkit-scrollbar]:hidden;
}

@utility hover-brightness {
  @apply cursor-pointer transition-all duration-300 hover:brightness-120;
}

@theme {
  --font-sans: 'Radio Canada Big', sans-serif;
}

:root {
  --ui-primary: #ff0055;
  --ui-secondary: #514f59;
  --ui-warning: #ffc653;
  --ui-error: var(--ui-primary);
  --ui-radius: 0.375rem;
  --ui-bg: #1b1a21;
  --ui-header-height: --spacing(16);
  --ui-container: 96rem;

  /* Nuxt-Charts */
  --vis-color0: oklch(0.72 0.192 149.58) !important;
  --vis-color1: oklch(0.63 0.1963 157.86) !important;

  --tooltip-label-color: rgba(255, 255, 255, 0.5) !important;
  --tooltip-value-color: rgba(255, 255, 255, 1) !important;

  --vis-axis-grid-color: rgba(255, 255, 255, 0.1) !important;
  --vis-tooltip-background-color: #121212 !important;
  --vis-tooltip-border-color: none !important;
  --vis-tooltip-text-color: rgba(255, 255, 255, 0.5) !important;
  --vis-axis-tick-label-color: rgba(255, 255, 255, 0.5) !important;
  --vis-legend-label-color: rgba(255, 255, 255, 0.75) !important;

  --vis-axis-label-color: rgba(255, 255, 255, 0.5) !important;
  --vis-legend-label-color: rgba(255, 255, 255, 0.5) !important;
}
