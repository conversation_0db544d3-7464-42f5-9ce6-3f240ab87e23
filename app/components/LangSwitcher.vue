<script setup lang="ts">
import type { DropdownMenuItem } from "@nuxt/ui";
const { locale, locales } = useI18n();
const switchLocalePath = useSwitchLocalePath();

const items = ref<DropdownMenuItem[][]>([
  locales.value.map((lang) => ({
    icon: `circle-flags:${lang.code}`,
    type: "link",
    to: switchLocalePath(lang.code),
  })),
]);
</script>

<template>
  <UDropdownMenu
    :items="items"
    :ui="{
      content: 'min-w-0',
    }"
  >
    <UButton icon="i-heroicons-globe-alt" color="neutral" variant="ghost" />
  </UDropdownMenu>
</template>
