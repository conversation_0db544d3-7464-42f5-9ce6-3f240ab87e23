<template>
  <div class="no-scrollbar flex gap-2 overflow-x-auto" v-auto-animate>
    <!-- IP Category Filters -->
    <UButton
      v-for="category in ipCategories"
      :key="category.code"
      :variant="modelValue === category.code ? 'solid' : 'soft'"
      :color="modelValue === category.code ? 'primary' : 'neutral'"
      @click="toggleCategory(category.code)"
      size="sm"
      :trailing-icon="modelValue === category.code ? 'i-heroicons-x-mark' : ''"
    >
      <p class="whitespace-nowrap">{{ useTranslatedName(category.translations) }}</p>
    </UButton>
  </div>
</template>

<script lang="ts" setup>
const { useDirectusFetch } = useDirectus();
const { locale } = useI18n();
const modelValue = defineModel<string>('modelValue');

const { data: populatedIpCategories } = await useFetch('/api/ips/categories', { key: 'populated-ip-categories' });

const { data: ipCategories } = await useDirectusFetch<IpCategories[]>('/items/ip_categories', {
  key: 'ip-categories',
  watch: [locale], // Make reactive to locale changes
  params: {
    fields: ['code, translations.name, translations.languages_id'],
    filter: {
      id: {
        _in: populatedIpCategories,
      },
    },
    sort: ['translations.name'],
  },
});

// Toggle category selection - only one active at a time
const toggleCategory = (categoryCode: string) => {
  modelValue.value = modelValue.value === categoryCode ? '' : categoryCode;
};
</script>
