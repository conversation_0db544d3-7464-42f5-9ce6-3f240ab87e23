<template>
  <div class="flex min-h-screen flex-col items-center justify-center">
    <div
      class="flex flex-col items-center rounded-lg border border-[#3F3D47] bg-[#1B1A2199] p-8 backdrop-blur-xl lg:w-md"
      :class="mode === 'owner' ? 'bg-transparent! border-0 backdrop-blur-none!' : ''"
    >
      <ULink to="/">
        <NuxtImg src="/images/ipgo-cherry.svg" alt="IPGO" class="-mt-16 h-12 w-12" loading="eager" />
      </ULink>
      <h2 class="my-2 text-center text-xl font-bold">
        {{ $t('global.log_in_spaced') }}
      </h2>

      <UForm class="flex w-full flex-col gap-4" :schema="schema" :state="state" @submit="onSubmit">
        <UFormField name="email" :label="$t('global.email')">
          <UInput
            v-model="state.email"
            type="email"
            id="email"
            class="w-full"
            size="xl"
            :placeholder="$t('global.input_email_placeholder')"
          />
        </UFormField>

        <UFormField name="password" :label="$t('global.password')">
          <UInput
            v-model="state.password"
            :type="showPassword ? 'text' : 'password'"
            id="password"
            class="w-full"
            size="xl"
            :placeholder="$t('global.input_password_placeholder')"
          >
            <template #trailing>
              <UButton
                variant="ghost"
                :icon="showPassword ? 'i-heroicons-eye-20-solid' : 'i-heroicons-eye-slash-20-solid'"
                size="xs"
                class="cursor-pointer rounded-full opacity-50"
                @click="showPassword = !showPassword"
              />
            </template>
          </UInput>
        </UFormField>

        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <UFormField name="rememberMe">
              <div class="flex items-center">
                <UCheckbox v-model="state.rememberMe" />
                <span class="ml-2 block text-sm">{{ $t('global.remember_me') }}</span>
              </div>
            </UFormField>
          </div>
          <ULink :to="localePath('recover-password')" class="text-sm" external>
            {{ $t('global.recover_password') }}
          </ULink>
        </div>

        <UButton
          type="submit"
          color="primary"
          size="lg"
          class="w-full justify-center rounded-full"
          :loading="isLoading"
        >
          {{ $t('global.log_in') }}
        </UButton>

        <p class="text-center text-sm text-zinc-400">
          {{ $t('global.no_account') }}
          <ULink :to="getSignupRoute()" class="cursor-pointer underline" external>
            {{ $t('global.sign_up') }}
          </ULink>
        </p>
      </UForm>
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as z from 'zod/v4';
import type { FormSubmitEvent } from '@nuxt/ui';

const props = defineProps<{
  mode: 'fan' | 'creator' | 'owner';
}>();

const { t } = useI18n();
const localePath = useLocalePath();
const { leftOffUrl, profile } = useUser();
const toast = useToast();
const isLoading = ref(false);
const showPassword = ref(false);

// Get signup route based on mode
function getSignupRoute() {
  switch(props.mode) {
    case 'fan':
      return localePath('signup');
    case 'creator':
      return localePath('creator-signup');
    case 'owner':
      return localePath('owner-signup');
    default:
      return localePath('signup');
  }
}

// Define validation schema
const schema = z.object({
  email: z.string().email(t('global.email_must_valid')),
  password: z.string().min(1, t('global.missing_password')),
  rememberMe: z.boolean().optional().default(false),
});

type Schema = z.output<typeof schema>;

// Form state
const state = reactive<Partial<Schema>>({
  email: '',
  password: '',
  rememberMe: true,
});

// Handle form submission
async function onSubmit(event: FormSubmitEvent<Schema>) {
  isLoading.value = true;
  try {
    // For now, simulate a successful login
    await $fetch('/api/auth/login', {
      method: 'POST',
      body: {
        email: state.email,
        password: state.password,
        mode: props.mode,
      },
    });

    const profileData = await $fetch('/api/me');

    profile.value = profileData;

    // Navigate to dashboard or home page
    if (leftOffUrl.value) {
      navigateTo(leftOffUrl.value);
      leftOffUrl.value = null;
      return;
    }
    switch(props.mode) {
      case 'fan':
        navigateTo(localePath('fan'));
        break;
      case 'creator':
        navigateTo(localePath('creator-ip'));
        break;
      case 'owner':
        // check if ip_owner_profile status is active
        if (profileData.ip_owner_profile[0]?.status === 'active') {
          navigateTo(localePath('owner-ip'));
          break;
        }
        toast.add({
          title: t('global.account_pending'),
          description: t('global.wait_for_approval'),
          duration: 10000,
          color: 'warning',
        });
        break;
    }
  } catch (error) {
    console.error('Login failed', error);
    toast.add({
      title: t('global.error'),
      description: t('global.failed_to_login'),
    });
  } finally {
    isLoading.value = false;
  }
}
</script>
