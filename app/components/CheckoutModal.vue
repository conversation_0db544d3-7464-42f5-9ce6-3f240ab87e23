<template>
  <UModal
    v-model:open="isOpen"
    :title="title"
    :close="{
      color: 'primary',
      variant: 'ghost',
      class: 'rounded-full',
    }"
    :ui="{ header: 'border-b-0' }"
  >
    <slot />
    <template #body>
      <div class="-mt-6 flex w-full flex-col gap-8">
        <UForm :schema="orderSchema" :state="orderForm" class="space-y-4" @submit="submitOrder">
          <!-- Items Display -->
          <div class="space-y-3">
            <div 
              v-for="item in items" 
              :key="item.id"
              class="flex flex-row gap-4 w-full"
            >
              <NuxtImg 
                :src="directusAssetsUrl(item.image as string, 200, 200) ?? '/images/missing-product.png'"
                class="h-20 w-20 rounded-lg object-cover" 
                placeholder 
                placeholder-class="blur-xs" 
              />
              
              <div class="flex flex-col gap-1 flex-grow">
                <p class="text-sm font-bold">{{ item.name }}</p>
                <p class="text-sm text-primary">{{ formatPrice(item.price) }}</p>
                <p class="text-xs text-neutral-400">{{ $t('global.quantity') }}: {{ item.quantity }}</p>
              </div>

              <!-- For single item, show quantity input -->
              <!-- <div v-if="items.length === 1" class="flex items-center">
                <UFormField name="quantity">
                  <UInputNumber :min="1" v-model="orderForm.quantity" />
                </UFormField>
              </div> -->
            </div>
          </div>

          <!-- Shipping Address -->
          <div 
            class="p-4 bg-[#22202C] rounded-xl border border-muted flex cursor-pointer hover:brightness-110"
            :class="{ 'justify-center cursor-pointer border-dashed border-warning hover:bg-warning-700/10': !selectedAddress }" 
            @click="isShippingAddressModalOpen = true"
          >
            <div v-if="!selectedAddress" class="flex gap-2">
              <UIcon name="i-heroicons-plus"/>
              <span class="text-sm">
                {{ $t('global.add_new_shipping_address') }}
              </span>
            </div>
            <template v-else>
              <UFormField name="shipping_address" :label="$t('global.shipping_address')" class="grow">
                <div id="selected-address-details" class="grow">
                  <p class="text-xs">{{ selectedAddress?.name }}</p>
                  <p class="text-xs">{{ selectedAddress?.address_line_1 }} {{ selectedAddress?.address_line_2 }}</p>
                  <p class="text-xs">{{ selectedAddress?.city }}</p>
                  <p class="text-xs" v-if="selectedAddress?.province">{{ selectedAddress?.province }}</p>
                  <p class="text-xs">{{ selectedAddress?.postcode }}</p>
                  <p class="text-xs">{{ selectedAddress?.phone_number }}</p>
                  <p class="text-xs">{{ COUNTRIES[locale].find(country => country.value === selectedAddress?.country)?.label }}</p>
                </div>
              </UFormField>
              <UButton icon="i-heroicons-chevron-right" variant="ghost" color="neutral" size="sm" />
            </template>
          </div>

          <!-- Payment Methods -->
          <div
            class="p-4 bg-[#22202C] rounded-xl border border-muted flex cursor-pointer hover:brightness-110"
            :class="{ 'justify-center cursor-pointer border-dashed border-warning hover:bg-warning-700/10': !selectedPaymentMethod }" 
            @click="isPaymentMethodModalOpen = true"
          >
            <template v-if="!selectedPaymentMethod">
              <div class="flex gap-2">
                <UIcon name="i-heroicons-plus"/>
                <span class="text-sm">
                  {{ $t('global.add_new_payment_method') }}
                </span>
              </div>
            </template>
           <template v-else>
            <UFormField name="payment_method" :label="$t('global.payment_method')" class="grow">
              <div id="selected-payment-details" class="grow">
                <div v-if="selectedPaymentMethod" class="text-xs flex items-center gap-2">
                  <UIcon name="i-heroicons-credit-card" />
                  <span>{{ getCardBrandDisplay(selectedPaymentMethod.card?.brand) }} •••• {{ selectedPaymentMethod.card?.last4 }}</span>
                </div>
                <div v-else class="text-xs flex items-center gap-2 text-neutral-400">
                  <UIcon name="i-heroicons-credit-card" />
                  <span>{{ $t('global.select_payment_method') }}</span>
                </div>
              </div>
            </UFormField>
            <UButton icon="i-heroicons-chevron-right" variant="ghost" color="neutral" size="sm" />
            </template>
          </div>

          <!-- Message to Creator (only show if not hidden) -->
          <UFormField v-if="!props.hideMessageField" :label="$t('global.message_to_creator')" name="message">
            <UTextarea
              v-model="orderForm.message"
              class="w-full"
              :placeholder="$t('global.send_message_to_complete_transaction')"
            />
          </UFormField>

          <!-- Total -->
          <div class="flex">
            <p class="grow text-xl font-bold">{{ $t('global.total') }}</p>
            <p class="text-primary text-xl font-bold">{{ formatPrice(totalAmount) }}</p>
          </div>
          
          <UButton 
            color="primary" 
            size="lg" 
            class="w-full justify-center rounded-full" 
            type="submit"
            :loading="isSubmitting"
          >
            {{ $t('global.pay_now') }}
          </UButton>
        </UForm>
      </div>
    </template>
  </UModal>

  <!-- Shipping Address Modal -->
  <UModal v-model:open="isShippingAddressModalOpen" :title="$t('global.shipping_address')" :close="true">
    <template #body>
      <FanAddressList :defaultAddress="selectedAddress || undefined" @selected="handleSelectedAddress" />
    </template>
  </UModal>

  <!-- Payment Method Modal -->
  <UModal v-model:open="isPaymentMethodModalOpen" :title="$t('global.payment_method')" :close="true">
    <template #body>
      <div>
        <FanPaymentMethodList :defaultPaymentMethod="selectedPaymentMethod || undefined" @selected="handleSelectedPaymentMethod" />
      </div>
    </template>
  </UModal>
</template>

<script lang="ts" setup>
import z from 'zod/v4';
import type { FormSubmitEvent } from '@nuxt/ui';
import { COUNTRIES } from '~/utils/countries';

// Component props
interface CheckoutItem {
  id: number;
  name: string;
  price: number;
  quantity: number;
  image: string;
  message?: string;
}

interface Props {
  items: CheckoutItem[];
  title?: string;
  predefinedMessage?: string;
  hideMessageField?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Order Details',
  predefinedMessage: '',
  hideMessageField: false,
});

const emit = defineEmits(['success', 'error']);

// Composables
const { t, locale } = useI18n();
const { directusAssetsUrl } = useDirectus();
const { formatPrice } = useCurrency();
const { profile } = useUser();
const toast = useToast();
const { stripe } = useClientStripe();

// Modal states
const isOpen = ref(false);
const isShippingAddressModalOpen = ref(false);
const isPaymentMethodModalOpen = ref(false);
const isSubmitting = ref(false);

// Get user profile
const { data: profileData } = await useFetch('/api/me',{
  headers: {
    Authorization: `Bearer ${useUser().accessToken.value}`,
  },
});
profile.value = profileData.value || null;

// Use addresses composable
const { selectedAddress, fetchAddresses, setSelectedAddress } = useAddresses();

// Use payment methods composable
const { selectedPaymentMethod, fetchPaymentMethods, setSelectedPaymentMethod } = usePaymentMethods();

// Payment utility functions (inline for now)
const getCardBrandDisplay = (brand?: string): string => {
  if (!brand) return 'Card';
  return brand.charAt(0).toUpperCase() + brand.slice(1);
};

// Form schema and state
const orderSchema = z.object({
  quantity: z.number().min(1).optional(), // Optional for multi-item orders
  message: props.hideMessageField
    ? z.string().optional() // Optional when message is predefined from cart
    : z.string().min(5, t('global.required_fields')), // Required when shown in modal
});

type OrderSchema = z.output<typeof orderSchema>;

const orderForm = reactive<Partial<z.infer<typeof orderSchema>>>({
  quantity: props.items.length === 1 ? props.items[0]?.quantity : undefined,
  message: props.predefinedMessage || '',
});

// Computed total amount
const totalAmount = computed(() => {
  if (props.items.length === 1 && orderForm.quantity) {
    // Single item: use form quantity
    return (props.items[0]?.price || 0) * orderForm.quantity;
  } else {
    // Multiple items: use item quantities
    return props.items.reduce((total, item) => total + (item.price * item.quantity), 0);
  }
});

// Address handling
const handleSelectedAddress = (address: Addresses) => {
  setSelectedAddress(address);
  isShippingAddressModalOpen.value = false;
};

// Payment method handling
const handleSelectedPaymentMethod = (paymentMethod: any) => {
  setSelectedPaymentMethod(paymentMethod);
  isPaymentMethodModalOpen.value = false;
};

// Payment and order submission with payment-first flow
async function submitOrder(event: FormSubmitEvent<OrderSchema>) {
  if (!selectedAddress.value) {
    toast.add({
      title: t('global.please_select_shipping_address'),
      color: 'error',
    });
    return;
  }

  if (!selectedPaymentMethod.value) {
    toast.add({
      title: t('global.please_select_payment_method'),
      color: 'error',
    });
    return;
  }

  if (!stripe.value) {
    toast.add({
      title: t('global.payment_system_not_ready'),
      color: 'error',
    });
    return;
  }

  isSubmitting.value = true;

  try {
    // Step 1: Prepare order data
    // Use predefined message when message field is hidden, otherwise use form message
    const messageToUse = props.hideMessageField ? (props.predefinedMessage || '') : (event.data.message || '');

    const orderItems = props.items.map((item) => ({
      id: item.id,
      quantity: props.items.length === 1 ? (orderForm.quantity || item.quantity) : item.quantity,
      message: messageToUse, // Use the appropriate message based on context
    }));

    // Step 2: Create Payment Intent
    const paymentIntentResponse = await $fetch('/api/payment/create-intent-for-order', {
      method: 'POST',
      body: {
        items: orderItems,
        payment_method_id: selectedPaymentMethod.value.id,
        shipping_address_id: selectedAddress.value.id, // Pass selected address
      },
    });

    if (!paymentIntentResponse.success || !paymentIntentResponse.client_secret) {
      throw new Error('Failed to create payment intent');
    }

    // Step 3: Confirm Payment with Stripe
    const { error: paymentError, paymentIntent } = await stripe.value.confirmCardPayment(
      paymentIntentResponse.client_secret,
      {
        payment_method: selectedPaymentMethod.value.id,
      }
    );

    if (paymentError) {
      throw new Error(paymentError.message || 'Payment failed');
    }

    if (paymentIntent?.status !== 'succeeded') {
      throw new Error('Payment was not completed successfully');
    }

    // Step 4: Create Order from successful payment
    const orderResponse = await $fetch('/api/orders/create-from-payment', {
      method: 'POST',
      body: {
        payment_intent_id: paymentIntent.id,
      },
    });

    if (orderResponse.success) {
      toast.add({
        title: t('global.order_created_successfully'),
        color: 'success',
      });

      isOpen.value = false;
      // Emit success with order response and ordered item IDs for cart cleanup
      emit('success', {
        ...orderResponse,
        orderedItemIds: orderItems.map(item => item.id)
      });

      // Navigate to orders page
      await navigateTo('/fan/orders');
    } else {
      throw new Error('Failed to create order after payment');
    }

  } catch (error) {
    // Provide specific error messages
    let errorMessage = t('global.order_failed');
    if (error instanceof Error) {
      if (error.message.includes('stock')) {
        errorMessage = t('global.insufficient_stock_please_refresh_cart');
      } else if (error.message.includes('card')) {
        errorMessage = t('global.card_declined');
      } else if (error.message.includes('payment')) {
        errorMessage = t('global.payment_failed');
      }
    }

    toast.add({
      title: errorMessage,
      color: 'error',
    });
    emit('error', errorMessage);
  } finally {
    isSubmitting.value = false;
  }
}

// Initialize addresses and set default
fetchAddresses();

// Initialize payment methods and set default
fetchPaymentMethods();

// Expose methods for parent components
defineExpose({
  open: async () => {
    isOpen.value = true;
  },
  close: () => { isOpen.value = false; },
});
</script>
