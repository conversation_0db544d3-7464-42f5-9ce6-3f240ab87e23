<template>
  <UModal
    :title="$t('global.share_this_product')"
    :close="{
      color: 'primary',
      variant: 'ghost',
      class: 'rounded-full',
    }"
    :ui="{ header: 'border-b-0' }"
  >
    <UButton icon="i-heroicons-share" size="lg" variant="ghost" />
    <template #body>
      <div class="flex flex-col items-center gap-4 p-2">
        <div id="social-media-deeplinks-with-product-link" class="flex flex-row justify-center gap-8">
          <NuxtLink
            id="line-share"
            external
            target="_blank"
            :to="`https://line.me/R/msg/text/?${$t('global.check_out_this_product')} ${translatedProductName} ${currentUrl}`"
          >
            <img src="/images/social-media-line.png" class="h-1- w-10 rounded-full" />
          </NuxtLink>
          <NuxtLink
            id="whatsapp-share"
            external
            target="_blank"
            :to="`https://api.whatsapp.com/send?text=${$t('global.check_out_this_product')} ${translatedProductName} ${currentUrl}`"
          >
            <img src="/images/social-media-whatsapp.png" class="h-1- w-10 rounded-full" />
          </NuxtLink>
          <NuxtLink
            id="facebook-share"
            external
            target="_blank"
            :to="`https://www.facebook.com/sharer/sharer.php?u=${$t('global.check_out_this_product')} ${translatedProductName} ${currentUrl}`"
          >
            <img src="/images/social-media-fb-messenger.png" class="h-1- w-10 rounded-full" />
          </NuxtLink>
          <NuxtLink
            id="x-share"
            external
            target="_blank"
            :to="`https://twitter.com/intent/tweet?text=${$t('global.check_out_this_product')} ${translatedProductName} ${currentUrl}`"
          >
            <img src="/images/social-media-twitter.png" class="h-1- w-10 rounded-full" />
          </NuxtLink>
          <NuxtLink
            id="sms-share"
            external
            target="_blank"
            :to="`sms:?body=${$t('global.check_out_this_product')} ${translatedProductName}`"
          >
            <img src="/images/social-media-messages.png" class="h-1- w-10 rounded-full" />
          </NuxtLink>
        </div>
        <p>{{ $t('global.or') }}</p>
        <div class="flex gap-2" v-auto-animate>
          <!-- <UInput id="copy-product-link" :value="currentUrl+ 'asdsffAFJKWEDHKJDHAKDJANS'" class="w-full" size="lg"> -->
          <!-- </UInput> -->
          <UButton
          variant="ghost"
          class="cursor-pointer rounded-full"
          @click="copyToClipboard"
          :label="$t('global.copy_product_link')"
          />
          <UIcon name="i-heroicons-check" v-if="hasCopied" class="text-success-400 h-6 w-6 self-center" />
        </div>
      </div>
    </template>
  </UModal>
</template>

<script lang="ts" setup>
const { t } = useI18n();
const { copy } = useClipboard();
const toast = useToast();
const { href: currentUrl } = useRequestURL();

// Clipboard
const hasCopied = ref(false);
const copyToClipboard = async () => {
  await copy(currentUrl);
  toast.add({
    title: t('global.copied_to_clipboard'),
    duration: 2000,
    color: 'success',
    progress: false,
  });
  hasCopied.value = true;
};
</script>
