<template>
  <NuxtLinkLocale :to="{ name: getRouteName(mode), params: { id: ip.id } }">
    <div
      class="relative aspect-3/2 w-full cursor-pointer overflow-hidden rounded-lg border border-[#3F3D47] transition-all hover:brightness-125"
    >
      <NuxtImg
        :src="directusAssetsUrl(ip.main_image as string) ?? undefined"
        class="h-full w-full rounded-lg object-cover blur-none transition-all duration-100"
        :alt="name"
        loading="lazy"
        placeholder
        placeholder-class="blur-xs"
      />
      <div class="absolute inset-0 rounded-lg bg-black/40 transition-opacity duration-300 group-hover:opacity-30"></div>
      <div class="absolute bottom-0 left-0 p-4">
        <p class="line-clamp-2 text-lg font-bold">{{ name }}</p>
        <div
          class="mt-2 w-max rounded-sm border border-neutral-500 bg-neutral-500/50 px-1 py-0.5 text-xs backdrop-blur-md"
        >
          {{ useTranslatedName((ip.category as IpCategories).translations) }}
        </div>
      </div>
    </div>
  </NuxtLinkLocale>
</template>

<script lang="ts" setup>
const { locale } = useI18n();
const { getTranslation, directusAssetsUrl } = useDirectus();

const props = withDefaults(
  defineProps<{
    ip: Ip;
    locale?: string;
    mode?: 'creator' | 'fan' | 'owner';
  }>(),
  {
    locale: 'en',
    mode: 'fan',
  },
);

const name = getTranslation(props.ip.translations, 'name');

// Get route name based on mode
const getRouteName = (mode: 'creator' | 'fan' | 'owner') => {
  switch (mode) {
    case 'creator':
      return 'creator-ip-id';
    case 'owner':
      return 'owner-ip-id';
    default:
      return 'ip-id'; // fan mode
  }
};
</script>
