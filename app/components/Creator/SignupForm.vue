<template>
  <!-- Avatar Upload -->
  <UFormField name="avatar" class="mb-6 flex w-full flex-col px-1">
    <div class="mx-auto flex flex-row items-center space-x-4 sm:mx-0">
      <UAvatar
        :src="avatarPreview"
        :alt="state.firstName ? `${state.firstName} ${state.lastName}` : ''"
        size="xl"
        class="h-20 w-20 sm:h-24 sm:w-24"
      />

      <UButton
        size="xl"
        color="secondary"
        class="rounded-full px-4 text-sm whitespace-nowrap"
        @click="triggerFileInput"
        leading-icon="heroicons:pencil-square"
      >
        {{ $t('global.change_avatar') }}
      </UButton>
    </div>

    <input ref="fileInput" type="file" accept="image/jpeg,image/jpg,image/png" class="hidden" @change="handleFileChange" />
    <div v-if="!avatarPreview && formErrors.avatar" class="text-warning-500 mt-2 text-sm">
      {{ formErrors.avatar }}
    </div>
  </UFormField>

  <UForm
    class="grid w-full gap-x-6 gap-y-4 lg:grid-cols-2"
    :schema="creatorSignupSchema"
    :state="state"
    @submit="onSubmit"
  >
    <UFormField name="nickname" :label="$t('global.nickname')">
      <UInput v-model="state.nickname" type="text" id="nickname" size="lg" class="w-full" />
    </UFormField>

    <UFormField name="email" :label="$t('global.email')">
      <UInput disabled :value="state.email" type="text" id="email" size="lg" class="w-full" />
    </UFormField>

    <!-- First Name | Last Name -->
    <UFormField name="firstName" :label="$t('global.first_name')">
      <UInput v-model="state.firstName" type="text" id="firstName" size="lg" class="w-full" />
    </UFormField>
    <UFormField name="lastName" :label="$t('global.last_name')">
      <UInput v-model="state.lastName" type="text" id="lastName" class="w-full" size="lg" />
    </UFormField>

    <!-- Introduction (Creator-specific) -->
    <UFormField name="introduction" :label="$t('global.introduction')" class="lg:col-span-2">
      <UTextarea
        v-model="state.introduction"
        :placeholder="$t('global.introduction_placeholder')"
        type="text"
        id="introduction"
        class="w-full"
        size="lg"
        autoresize
        :rows="3"
      />
    </UFormField>

    <!-- Address -->
    <UFormField name="address" :label="$t('global.address')" class="lg:col-span-2">
      <UTextarea
        v-model="state.address"
        :placeholder="$t('global.address_placeholder')"
        type="text"
        id="address"
        class="w-full"
        size="lg"
        autoresize
      />
    </UFormField>

    <!-- City and Province -->
    <UFormField name="city" :label="$t('global.city')">
      <UInput v-model="state.city" type="text" id="city" class="w-full" size="lg" />
    </UFormField>
    <UFormField name="province">
      <template #label>{{ `${$t('global.province')} (${$t('global.optional')})` }}</template>
      <UInput v-model="state.province" type="text" id="province" class="w-full" size="lg" />
    </UFormField>
    
    <!-- Country and Postal Code -->
    <UFormField name="country" :label="$t('global.country')">
      <USelect v-model="state.country" :items="COUNTRIES[locale]" id="country" class="w-full" size="lg" />
    </UFormField>
    <UFormField name="postalCode" :label="$t('global.postal_code')">
      <UInput v-model="state.postalCode" type="text" id="postalCode" class="w-full" size="lg" />
    </UFormField>

    <!-- Password -->
    <UFormField name="password" class="lg:col-span-2" :label="$t('global.create_your_password')">
      <UInput
        v-model="state.password"
        :type="showPassword ? 'text' : 'password'"
        id="password"
        class="w-full"
        size="lg"
        :placeholder="$t('global.input_password_placeholder')"
      >
        <template #trailing>
          <UButton
            variant="ghost"
            :icon="showPassword ? 'i-heroicons-eye-20-solid' : 'i-heroicons-eye-slash-20-solid'"
            size="xs"
            class="cursor-pointer rounded-full opacity-50"
            @click="showPassword = !showPassword"
          />
        </template>
      </UInput>
    </UFormField>

    <!-- Confirm Password -->
    <UFormField required name="confirmPassword" class="lg:col-span-2" :label="$t('global.re_enter_password')">
      <UInput
        v-model="state.confirmPassword"
        :type="showConfirmPassword ? 'text' : 'password'"
        id="confirmPassword"
        class="w-full"
        size="lg"
      >
        <template #trailing>
          <UButton
            variant="ghost"
            :icon="showConfirmPassword ? 'i-heroicons-eye-20-solid' : 'i-heroicons-eye-slash-20-solid'"
            size="xs"
            class="cursor-pointer rounded-full opacity-50"
            @click="showConfirmPassword = !showConfirmPassword"
          />
        </template>
      </UInput>
    </UFormField>
    
    <!-- Terms and Conditions -->
    <UFormField name="agreeTerms" class="lg:col-span-2">
      <div class="flex flex-row gap-2">
        <UCheckbox v-model="state.agreeTerms" />
        {{ $t('global.agree_with_terms') }}
        <ULink class="cursor-pointer underline">
          {{ $t('global.privacy_policy') || 'Privacy Policy' }}
        </ULink>
      </div>
    </UFormField>

    <UButton
      color="primary"
      type="submit"
      size="lg"
      class="w-full justify-center rounded-full text-sm sm:text-base lg:col-span-2"
      :loading="isLoading"
    >
      {{ $t('global.sign_up') }}
    </UButton>
    
    <DevOnly>
      <UButton
        color="success"
        @click="fillUpMock"
        size="lg"
        class="w-full justify-center rounded-full text-sm sm:text-base lg:col-span-2"
      >
        {{ $t('dev_only.mock_fill_up') }}
      </UButton>
    </DevOnly>
  </UForm>
</template>

<script lang="ts" setup>
import * as z from 'zod/v4';
import type { FormSubmitEvent } from '@nuxt/ui';
import { COUNTRIES } from '~/utils/countries';

const { t, locale } = useI18n();
const { email } = useSignup();
const localePath = useLocalePath();

// Creator signup schema
const creatorSignupSchema = z.object({
  agreeTerms: z.boolean().refine((value) => value, {
    message: t('global.terms_agreement_required'),
  }),
  avatar: z.string(),
  email: z.string(),
  firstName: z.string().min(3, t('global.missing_first_name')),
  lastName: z.string().min(3, t('global.missing_last_name')),
  nickname: z.string().min(3, t('global.missing_nickname')),
  introduction: z.string().min(10, t('global.missing_introduction')),
  address: z.string().min(1, t('global.missing_address')),
  city: z.string().min(1, t('global.missing_city')),
  country: z.string().min(1, t('global.missing_country')),
  postalCode: z.string().min(1, t('global.missing_postal_code')),
  province: z.string().optional(),
  password: z.string().min(5, t('global.password_requirements')),
  confirmPassword: z.string().min(5, t('global.passwords_must_match')),
});

type CreatorSignupFormSchema = z.infer<typeof creatorSignupSchema>;

const toast = useToast();
const fileInput = ref<HTMLInputElement | null>(null);

const state = useState<Partial<CreatorSignupFormSchema>>('creatorSignupState', () => ({
  agreeTerms: false,
  avatar: '',
  email: email.value!,
  firstName: '',
  lastName: '',
  nickname: '',
  introduction: '',
  address: '',
  city: '',
  country: '',
  postalCode: '',
  province: '',
  password: '',
  confirmPassword: '',
}));

// UI state
const showPassword = ref(false);
const showConfirmPassword = ref(false);
const isLoading = ref(false);
const avatarPreview = ref<string | undefined>(undefined);
const formErrors = ref<Record<string, string>>({});

// Avatar upload functions
function triggerFileInput() {
  if (fileInput.value) {
    fileInput.value.click();
  }
}

function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    const file = target.files[0];

    // Validate file type - only allow JPEG and PNG
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type?.toLowerCase() || '')) {
      toast.add({
        title: t('global.error'),
        description: t('global.avatar_invalid_format') || 'Please upload only JPEG or PNG images',
        color: 'error',
      });
      // Clear the input
      target.value = '';
      return;
    }

    // Create a preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target && typeof e.target.result === 'string') {
        avatarPreview.value = e.target.result;
        state.value.avatar = e.target.result;
      }
    };
    reader.readAsDataURL(file as File);
  }
}

// Form submission
async function onSubmit(event: FormSubmitEvent<CreatorSignupFormSchema>) {
  // Validate avatar separately since it's not directly tied to the form input
  if (!avatarPreview.value) {
    formErrors.value.avatar = t('global.avatar_required');
    return;
  }

  await creatorSignup(event.data);
}

// Mock data for development
function fillUpMock() {
  state.value = {
    agreeTerms: true,
    avatar: '',
    email: email.value!,
    firstName: 'John',
    lastName: 'Creator',
    nickname: 'johncreator',
    introduction: 'I am a passionate creator with years of experience in digital art and design.',
    address: '123 Creator Street',
    city: 'Tokyo',
    country: 'JP',
    postalCode: '100-0001',
    province: 'Tokyo',
    password: 'DemoDemo1',
    confirmPassword: 'DemoDemo1',
  };
}

// Creator Signup
async function creatorSignup(data: CreatorSignupFormSchema) {
  isLoading.value = true;

  try {
    await $fetch('/api/signup', {
      method: 'POST',
      body: {
        ...data,
        role: 'creator',
      },
    });

    // Show success notification
    toast.add({
      color: 'success',
      title: t('global.success'),
      description: t('global.signup_success'),
    });
    
    // Log user in
    await $fetch('/api/auth/login', {
      method: 'POST',
      body: {
        email: data.email,
        password: data.password,
        mode: 'creator',
      },
    });

    navigateTo(localePath('creator-ip'), { replace: true });
  } catch (error) {
    // Show error notification
    console.error('Failed to complete creator signup', error);
    toast.add({
      title: t('global.error'),
      description: t('global.form_validation_error'),
    });
  } finally {
    isLoading.value = false;
  }
}
</script>
