<template>
  <!-- Add scrollable container for form overflow -->
  <div class="h-full overflow-y-auto">
    <UForm :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
    <UFormField :label="$t('global.product_name')" name="name">
      <UInput v-model="state.name" class="w-full" />
    </UFormField>

    <UFormField :label="$t('global.product_description')" name="description">
      <UTextarea v-model="state.description" class="w-full"/>
    </UFormField>

    <UFormField :label="$t('global.product_category')" name="category">
      <USelect v-model="state.category" :items="localizedProductCategoryItems" class="w-full" />
    </UFormField>

    <UFormField :label="$t('global.product_application_type')" name="application-type">
      <USelect v-model="state.applicationType" :items="applicationCategoryItems" class="w-full"/>
    </UFormField>

    <!-- Product Images Dropzone -->
    <ProductImageDropzone
      v-model="state.images!"
      :product-name="state.name"
      :max-images="5"
      :existing-images="existingImages"
    />

    <div class="grid grid-cols-2 gap-4">
      <UFormField :label="$t('global.price')" name="price">
        <UButtonGroup>
          <USelect v-model="state.currency" :items="CURRENCIES" class="w-24" />
          <UInput type="number" v-model="state.price" />
        </UButtonGroup>
      </UFormField>

      <UFormField :label="$t('global.total_stock')" name="total-stock">
        <UInput v-model="state.totalStock" type="number" />
      </UFormField>
    </div>

    <UButton type="submit" :loading="isSubmitting">
      {{ $t('global.edit_and_reapply') }}
    </UButton>
  </UForm>
  </div>
</template>

<script lang="ts" setup>
import z from 'zod/v4';
import type { FormSubmitEvent } from '@nuxt/ui';

interface Props {
  product: Products;
}

const props = defineProps<Props>();
const emit = defineEmits(['close', 'updated']);

const { t, locale } = useI18n();
const { directusAssetsUrl, useDirectusFetch } = useDirectus();
const toast = useToast();
const isSubmitting = ref(false);
const localePath = useLocalePath();

// Form validation schema
const schema = z.object({
  name: z.string().min(3, t('global.add_new_product_tooltip')),
  description: z.string().min(10, t('global.product_description_tooltip')),
  category: z.number().min(1, t('global.product_category_tooltip')),
  applicationType: z.number().min(1, t('global.product_application_type')),
  images: z.array(z.string()).min(1, t('global.at_least_one_image_required')),
  price: z.number().min(1, t('global.price_tooltip')),
  currency: z.string().min(1, t('global.currency')),
  totalStock: z.number().min(1, t('global.total_stock_tooltip')),
});

type Schema = z.output<typeof schema>

// Get current translation for the locale
const getCurrentTranslation = () => {
  const currentLanguageId = locale.value === 'en' ? 1 : locale.value === 'jp' ? 2 : 3;
  return props.product.translations?.find((t: any) => 
    (typeof t.languages_id === 'number' ? t.languages_id : t.languages_id?.id) === currentLanguageId
  ) || props.product.translations?.[0] || { name: '', description: '' };
};

// Extract existing images for ProductImageDropzone
const existingImages = computed(() => {
  if (!props.product.images || props.product.images.length === 0) return [];

  return props.product.images.map((img: any) => {
    const fileId = typeof img.directus_files_id === 'string' ? img.directus_files_id : img.directus_files_id?.id;
    return {
      id: fileId,
      preview: directusAssetsUrl(fileId, 400, 400) || '/images/missing-product.png',
      uploaded: true,
    };
  });
});

// Initialize form state with existing product data
const currentTranslation = getCurrentTranslation();
const state = reactive<Partial<Schema>>({
  name: currentTranslation.name || '',
  description: currentTranslation.description || '',
  category: typeof props.product.category === 'number' ? props.product.category : props.product.category?.id || 0,
  applicationType: typeof props.product.product_application_category === 'number' 
    ? props.product.product_application_category 
    : props.product.product_application_category?.id || 1,
  images: props.product.images?.map((img: any) => 
    typeof img.directus_files_id === 'string' ? img.directus_files_id : img.directus_files_id?.id
  ) || [],
  price: Number(props.product.price) || 0,
  currency: props.product.base_currency || 'USD',
  totalStock: props.product.stock_total || 0,
});

// Fetch product categories
const { data: productCategories } = useDirectusFetch<ProductCategories[]>('/items/product_categories', {
  key: 'product-categories',
  params: {
    fields: ['id', 'translations.*'],
  },
});

const localizedProductCategoryItems = computed(() => {
  if (!productCategories.value) return [];
  return productCategories.value.map((item) => ({
    label: item.translations[localeToIndex(locale.value)].name ?? '',
    value: item.id,
  }));
});

// Fetch application categories
const { data: applicationCategory } = useDirectusFetch<ProductApplicationCategory[]>('/items/product_application_category',{
  key: 'product-application-category',
  params: {
    fields: ['id', 'name']
  }
});

const applicationCategoryItems = computed(() => {
  return (applicationCategory.value ?? []).map((item) => ({
    label: item.name ?? '',
    value: item.id ?? '',
  }));
});

// Form submission handler
async function onSubmit(event: FormSubmitEvent<Schema>) {
  isSubmitting.value = true;
  
  try {
    // Use the reapply endpoint for rejected products
    const response = await $fetch(`/api/creator/products/${props.product.id}/reapply`, {
      method: 'POST',
      body: {
        ...event.data,
        locale: locale.value,
      }
    });

    toast.add({
      title: t('global.success'),
      description: t('global.product_reapplied_successfully'),
      color: 'success'
    });
      
    emit('updated');
    emit('close');

    navigateTo(localePath('creator-my-products'),{replace: true});
  } catch (error) {
    console.error('Failed to update product:', error);
    toast.add({
      title: t('global.error'),
      description: t('global.unexpected_error'),
      color: 'error',
    });
  } finally {
    isSubmitting.value = false;
  }
}
</script>
