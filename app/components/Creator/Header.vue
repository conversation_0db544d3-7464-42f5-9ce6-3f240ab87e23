<script setup lang="ts">
import type { NavigationMenuItem } from '@nuxt/ui';

// Props
const props = withDefaults(
  defineProps<{
    translucent?: boolean;
  }>(),
  {
    translucent: false,
  },
);

const { t } = useI18n();
const localePath = useLocalePath();
const { accessToken, profile } = useUser();
const { directusAssetsUrl, useDirectusFetch } = useDirectus();

if (accessToken.value) {
  const { data: profileData } = await useFetch('/api/me', {
    key: 'profile-creator',
  });
  profile.value = profileData.value || null;
}

const items = computed<NavigationMenuItem[]>(() => [
  {
    label: t('global.available_ip'),
    to: localePath('creator-ip'),
  },
  {
    label: t('global.my_products'),
    to: localePath('creator-my-products'),
  },
  {
    label: t('global.order_list'),
    to: localePath('creator-orders'),
  },
  {
    label: t('global.analysis'),
    to: localePath('creator-analysis'),
  }
]);

const avatarUrl = computed(() => {
  if (!profile.value?.avatar) return undefined;
  return directusAssetsUrl(profile.value?.avatar as string) ?? undefined;
});

// UI configuration - conditionally apply translucent background
const headerUI = computed(() => ({
  left: '!flex-none',
  right: 'flex items-center justify-end flex-1 gap-3 lg:gap-1.5',
  ...(props.translucent && {
    root: 'backdrop-blur-none !bg-gradient-to-b from-[#1b1a21]/60 via-[#1b1a21]/20 to-transparent',
  }),
}));

const isProfileEditModalOpen = ref(false);

const { data: orders } = await useFetch<Orders[]>('/api/creator/orders', {
  key: 'creator-orders-header',
  query: {
    status: 'processing',
    fields: 'id'
  },
});

const ordersProcessing = computed(() => {
  return (orders.value?.length ?? 0) > 0 ? true : false;
})
</script>

<template>
  <UHeader :ui="headerUI" :to="localePath('creator-ip')">
    <template #title>
      <LogoWithText class="w-auto" />
    </template>
    <!-- <UNavigationMenu :items="items" class="justify-start" variant="link" color="neutral" /> -->
    <div class="ml-4 flex flex-row gap-4 text-sm">
      <ULink :to="localePath('creator-ip')" :class="{ 'text-white hover:text-neutral-700': props.translucent }">
        {{ $t('global.available_ip') }}
      </ULink>
      <ULink :to="localePath('creator-my-products')" :class="{ 'text-white hover:text-neutral-700': props.translucent }">
        {{ $t('global.my_products') }}
      </ULink>
      <UChip :show="ordersProcessing" :text="orders?.length">
        <ULink :to="localePath('creator-orders')" :class="{ 'text-white hover:text-neutral-700': props.translucent }">
          {{ $t('global.order_list') }}
        </ULink>
      </UChip>
      <ULink :to="localePath('creator-analysis')" :class="{ 'text-white hover:text-neutral-700': props.translucent }">
        {{ $t('global.analysis') }}
      </ULink>
    </div>
    <template #right>
      <LangSwitcher />

      <UModal v-model:open="isProfileEditModalOpen" :title="$t('global.edit_profile')" :close="true">
          <UButton variant="ghost" color="neutral">
            <ClientOnly>
              <UAvatar id="no-avatar" v-if="!profile?.avatar" src="/images/missing-product.png" />
              <UAvatar id="avatar" v-else="profile.avatar && refreshToken" :src="avatarUrl" aria-label="profile" />
              <template #fallback>
                <UAvatar id="no-avatar" src="/images/missing-product.png" />
              </template>
            </ClientOnly>
            <p class="hidden lg:block" v-if="profile">{{ t('global.account') }}</p>
            <p class="hidden lg:block" v-else>{{ t('global.log_in') }}</p>
          </UButton>
          <template #body>
            <CreatorProfileDetails mode="edit" @updated="isProfileEditModalOpen = false"/>
          </template>
      </UModal>
    </template>

    <template #body>
      <UNavigationMenu :items="items" orientation="vertical" class="-mx-2.5" />
    </template>
  </UHeader>
</template>
