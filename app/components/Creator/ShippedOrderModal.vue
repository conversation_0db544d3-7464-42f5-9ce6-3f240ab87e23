<template>
  <UModal v-model:open="isCompleteOrderModalOpen" :title="$t('global.complete_order') + ' #' + orderId">
    <slot/>
    <template #body>
      <div class="grid gap-4">
        <p class="text-muted mb-2">{{ $t('global.complete_transaction_confirmation') }}</p>
        <div class="grid grid-cols-2 gap-4">
          <UButton color="secondary" size="lg" class="w-full justify-center rounded-full" @click="isCompleteOrderModalOpen = false">
            {{ $t('global.cancel') }}
          </UButton>
          <UButton color="primary" size="lg" class="w-full justify-center rounded-full" @click="handleCompleteOrder">
            {{ $t('global.complete_order') }}
          </UButton>

        </div>
      </div>
    </template>
  </UModal>
</template>

<script lang="ts" setup>
const props = defineProps<{
  orderId: number;
}>();

const emit = defineEmits(['confirm']);

const toast = useToast();
const { t } = useI18n();
const { dFetch } = useDirectus();
const isCompleteOrderModalOpen = ref(false);
const handleCompleteOrder = async () => {
  try {
    await dFetch('/items/orders/' + props.orderId, {
      method: 'PATCH',
      body: {
        status: 'shipped',
      },
    });
    
    emit('confirm');
    toast.add({
      title: t('global.order_is_complete'),
      color: 'success',
    });
  } catch (error) {
    console.error('Failed to complete order:', error);
    toast.add({
      title: t('global.unexpected_error'),
      color: 'error',
    });
  }
  isCompleteOrderModalOpen.value = false;
};

</script>