<template>
  <footer>
    <UContainer class="py-10 pt-14">
      <div class="flex flex-col items-center justify-between md:flex-row">
        <NuxtLinkLocale to="index">
          <img src="/images/logo-w-text.svg" alt="IPGO" class="h-10" />
        </NuxtLinkLocale>
        <NuxtLink to="https://supermassiveglobal.co.jp/" external target="_blank">
          <img src="/images/logo-massive.svg" alt="IPGO" class="mt-4 h-10 md:mt-0" />
        </NuxtLink>
      </div>
      <div class="mt-6 flex flex-col items-center justify-between gap-4 text-xs md:flex-row md:gap-0">
        <div class="flex flex-col gap-4 text-center md:flex-row md:text-left">
          <ULink :to="localePath('creator-ip')" active-class="font-bold text-white">
            {{ $t('global.dashboard') }}
          </ULink>
          <ULink :to="localePath('about')">
            {{ $t('global.about_ipgo') }}
          </ULink>
          <ULink :to="`/ipl/privacy-policy-creator-ipl-${locale}.pdf`" target="_blank" external>
            {{ $t('global.ip_lic') }}
          </ULink>
          <ULink :to="`/pnc/privacy-policy-creator-pnc-${locale}.pdf`" target="_blank" external>
            {{ $t('global.privacy_policy') }}
          </ULink>
        </div>
        <p class="text-center md:text-right">Copyright Super Massive Global Co., Ltd.</p>
      </div>
    </UContainer>
  </footer>
</template>

<script setup lang="ts">
const localePath = useLocalePath();
const { locale } = useI18n();
</script>
