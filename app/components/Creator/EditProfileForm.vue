<template>
  <!-- Avatar Upload -->
  <UFormField name="avatar" class="mb-6 flex w-full flex-col px-1">
    <div class="mx-auto flex flex-row items-center space-x-4 sm:mx-0">
      <UAvatar
        :src="avatarPreview"
        :alt="state.firstName ? `${state.firstName} ${state.lastName}` : ''"
        size="xl"
        class="h-20 w-20 sm:h-24 sm:w-24"
      />

      <UButton
        size="xl"
        color="secondary"
        class="rounded-full px-4 text-sm whitespace-nowrap"
        @click="triggerFileInput"
        leading-icon="heroicons:pencil-square"
      >
        {{ $t('global.change_avatar') }}
      </UButton>
    </div>

    <input ref="fileInput" type="file" accept="image/jpeg,image/jpg,image/png" class="hidden" @change="handleFileChange" />
  </UFormField>

  <UForm class="grid w-full gap-x-6 gap-y-4 lg:grid-cols-2" :schema="editSchema" :state="state" @submit="onSubmit">
    <UFormField name="nickname" :label="$t('global.nickname')">
      <UInput v-model="state.nickname" type="text" id="nickname" size="lg" class="w-full" />
    </UFormField>

    <UFormField name="email" :label="$t('global.email')">
      <UInput disabled :value="state.email" type="text" id="email" size="lg" class="w-full" />
    </UFormField>

    <!-- First Name | Last Name -->
    <UFormField name="firstName" :label="$t('global.first_name')">
      <UInput v-model="state.firstName" type="text" id="firstName" size="lg" class="w-full" />
    </UFormField>
    <UFormField name="lastName" :label="$t('global.last_name')">
      <UInput v-model="state.lastName" type="text" id="lastName" class="w-full" size="lg" />
    </UFormField>

    <!-- Introduction (Creator-specific) -->
    <UFormField name="introduction" :label="$t('global.introduction')" class="lg:col-span-2">
      <UTextarea
        v-model="state.introduction"
        :placeholder="$t('global.introduction_placeholder')"
        type="text"
        id="introduction"
        class="w-full"
        size="lg"
        autoresize
        :rows="3"
      />
    </UFormField>

    <!-- Address -->
    <UFormField name="address" :label="$t('global.address')" class="lg:col-span-2">
      <UTextarea
        v-model="state.address"
        :placeholder="$t('global.address_placeholder')"
        type="text"
        id="address"
        class="w-full"
        size="lg"
        autoresize
      />
    </UFormField>

    <!-- City and Province -->
    <UFormField name="city" :label="$t('global.city')">
      <UInput v-model="state.city" type="text" id="city" class="w-full" size="lg" />
    </UFormField>
    <UFormField name="province">
      <template #label>{{ `${$t('global.province')} (${$t('global.optional')})` }}</template>
      <UInput v-model="state.province" type="text" id="province" class="w-full" size="lg" />
    </UFormField>

    <!-- Country and Postal Code -->
    <UFormField name="country" :label="$t('global.country')">
      <USelect v-model="state.country" :items="COUNTRIES[locale]" id="country" class="w-full" size="lg" />
    </UFormField>
    <UFormField name="postalCode" :label="$t('global.postal_code')">
      <UInput v-model="state.postalCode" type="text" id="postalCode" class="w-full" size="lg" />
    </UFormField>

    <UButton
      color="primary"
      type="submit"
      size="lg"
      class="w-full justify-center rounded-full text-sm sm:text-base lg:col-span-2"
      :loading="isLoading"
    >
      {{ $t('global.save_changes') }}
    </UButton>
  </UForm>
</template>

<script lang="ts" setup>
import * as z from 'zod/v4';
import type { FormSubmitEvent } from '@nuxt/ui';
import { COUNTRIES } from '~/utils/countries';

const emit = defineEmits(['updated']);

const { t, locale } = useI18n();
const { directusAssetsUrl } = useDirectus();

// Edit schema (no password or terms required)
const editSchema = z.object({
  avatar: z.string(),
  email: z.string(),
  city: z.string().min(1, t('global.missing_city')),
  country: z.string().min(1, t('global.missing_country')),
  firstName: z.string().min(3, t('global.missing_first_name')),
  introduction: z.string().min(10, t('global.missing_introduction')),
  lastName: z.string().min(3, t('global.missing_last_name')),
  nickname: z.string().min(3, t('global.missing_nickname')),
  postalCode: z.string().min(1, t('global.missing_postal_code')),
  province: z.string().optional(),
  address: z.string().min(1, t('global.missing_address')),
});

type EditFormSchema = z.infer<typeof editSchema>;

const toast = useToast();
const fileInput = ref<HTMLInputElement | null>(null);

const state = ref<Partial<EditFormSchema>>({
  avatar: '',
  email: '',
  firstName: '',
  lastName: '',
  nickname: '',
  introduction: '',
  address: '',
  city: '',
  country: '',
  postalCode: '',
  province: '',
});

// UI state
const isLoading = ref(false);
const avatarPreview = ref<string | undefined>(undefined);
const avatarFile = ref<File | null>(null);

// Avatar upload functions
function triggerFileInput() {
  if (fileInput.value) {
    fileInput.value.click();
  }
}

function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    const file = target.files[0];

    // Validate file type - only allow JPEG and PNG
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type?.toLowerCase() || '')) {
      toast.add({
        title: t('global.error'),
        description: t('global.avatar_invalid_format') || 'Please upload only JPEG or PNG images',
        color: 'error',
      });
      // Clear the input
      target.value = '';
      return;
    }

    avatarFile.value = file;

    // Create a preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target && typeof e.target.result === 'string') {
        avatarPreview.value = e.target.result;
        state.value.avatar = e.target.result;
      }
    };
    reader.readAsDataURL(file as File);
  }
}

// Load user profile data on mount
onMounted(async () => {
  await loadUserProfile();
});

// Load user profile data
async function loadUserProfile() {
  isLoading.value = true;
  try {
    // Fetch user data with creator profile
    const data = await $fetch('/api/creator/profile');
    const creatorProfile = data.creator_profile?.[0];

    // Populate form with user data
    state.value.email = data.email ?? '';
    state.value.firstName = data.first_name ?? '';
    state.value.lastName = data.last_name ?? '';
    state.value.nickname = data.nickname ?? '';

    // Populate form with creator profile data (prioritize creator profile over user data)
    if (creatorProfile) {
      state.value.introduction = creatorProfile.introduction ?? '';
      state.value.address = creatorProfile.street_address_1 ?? '';
      state.value.city = creatorProfile.city ?? data.city ?? '';
      state.value.country = creatorProfile.country ?? data.country ?? '';
      state.value.postalCode = creatorProfile.postal_code ?? data.postcode ?? '';
      state.value.province = creatorProfile.province ?? data.province ?? '';

      // Handle avatar from creator profile first, then user
      const avatarSource = creatorProfile.avatar || data.avatar;
      if (avatarSource) {
        const avatarId = typeof avatarSource === 'string' ? avatarSource : avatarSource.id;
        if (avatarId) {
          const avatarUrl = directusAssetsUrl(avatarId);
          if (avatarUrl) {
            avatarPreview.value = avatarUrl;
            state.value.avatar = avatarId;
          }
        }
      }
    } else {
      // Fallback to user data if no creator profile
      state.value.city = data.city ?? '';
      state.value.country = data.country ?? '';
      state.value.postalCode = data.postcode ?? '';
      state.value.province = data.province ?? '';

      if (data.avatar) {
        const avatarId = typeof data.avatar === 'string' ? data.avatar : data.avatar.id;
        if (avatarId) {
          const avatarUrl = directusAssetsUrl(avatarId);
          if (avatarUrl) {
            avatarPreview.value = avatarUrl;
            state.value.avatar = avatarId;
          }
        }
      }
    }
  } catch (error) {
    toast.add({
      title: t('global.error'),
      description: t('global.unexpected_error'),
    });
    console.error('Failed to load profile:', error);
  } finally {
    isLoading.value = false;
  }
}

// Form submission
async function onSubmit(_event: FormSubmitEvent<EditFormSchema>) {
  await updateProfile();
}

// Update profile function
async function updateProfile() {
  isLoading.value = true;

  try {
    let avatarId = state.value.avatar; // Keep existing avatar ID by default

    // If user selected a new avatar file, upload it first
    if (avatarFile.value && avatarPreview.value?.startsWith('data:')) {
      const uploadResponse = await $fetch<{ fileId: string; success: boolean }>('/api/upload-avatar', {
        method: 'POST',
        body: {
          avatar: avatarPreview.value, // base64 data
          filename: `${state.value.nickname}-avatar-updated`,
        },
      });
      avatarId = uploadResponse.fileId;
    }

    // Update creator profile with all data
    await $fetch('/api/creator/update-profile', {
      method: 'PATCH',
      body: {
        first_name: state.value.firstName,
        last_name: state.value.lastName,
        nickname: state.value.nickname,
        introduction: state.value.introduction,
        address: state.value.address,
        city: state.value.city,
        country: state.value.country,
        postal_code: state.value.postalCode,
        province: state.value.province,
        avatar: avatarId, // Use either existing ID or new uploaded ID
      },
    });

    // Show success notification
    toast.add({
      color: 'success',
      title: t('global.success'),
      description: t('global.profile_updated'),
    });

    emit('updated')
  } catch (error) {
    // Show error notification
    console.error('Failed to update profile', error);
    toast.add({
      color: 'error',
      title: t('global.error'),
      description: t('global.form_validation_error'),
    });
  } finally {
    isLoading.value = false;
  }
}
</script>
