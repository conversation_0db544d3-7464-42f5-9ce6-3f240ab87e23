<template>
  <div class="grid cursor-pointer grid-rows-2 rounded-md border border-[#3F3D47] transition-all hover:brightness-110">
    <NuxtLinkLocale :to="{ name: mode === 'fan' ? 'products-id' :  mode === 'owner' ? 'owner-products-id' : 'creator-my-products-id', params: { id: product.id } }">
      <div class="relative h-full w-full overflow-hidden">
        <NuxtImg
          :src="directusAssetsUrl(product.main_image as string, 500, 400) ?? '/images/missing-product.png'"
          class="h-full w-full rounded-t-md object-cover object-top blur-none transition-all duration-100"
          loading="lazy"
          placeholder
        />
        <UBadge class="absolute top-2 left-2" variant="soft" color="neutral" :ui="{ base: 'bg-zinc-400/90' }">{{
          useTranslatedName((product?.category as IpCategories).translations)
        }}</UBadge>
      </div>

      <div class="align-start flex h-full flex-col justify-between bg-[#22202CB2]">
        <div class="flex flex-col gap-3 p-4">
          <UBadge v-if="mode === 'creator'" variant="subtle" :color="statusColorMap" class="w-max">
            {{ $t(`global.${product.status }`) }}
          </UBadge>
          <h3 class="text-md line-clamp-2 font-semibold">{{ translatedProductName }}</h3>
          <div class="-my-2 flex items-baseline gap-1">
            <!-- Show discounted price and original price when discount exists -->
            <template v-if="hasDiscount">
              <p class="text-primary font-primary text-lg">
                {{ formattedDiscountedPrice }}
              </p>
              <p class="text-xs text-neutral-400 line-through">
                {{ formattedCurrentPrice }}
              </p>
            </template>
            <!-- Show only regular price when no discount -->
            <template v-else>
              <p class="text-primary font-primary text-lg">
                {{ formattedCurrentPrice }}
              </p>
            </template>
          </div>
          <p class="line-clamp-2 text-xs text-neutral-400">{{ translatedProductDescription }}</p>

          <!-- TAGS feature missing -->
          <!-- <div class="flex gap-2">
          <p v-for="tag in product.tags" :key="tag" class="text-xs text-neutral-500">#{{ tag }}</p>
        </div> -->
        </div>
        <div class="products-center flex items-center gap-2 p-4 pt-0">
          <UAvatar
            size="xs"
            :src="directusAssetsUrl((product?.creator as Creator).avatar as string) ?? '/images/missing-product.png'"
          />
          <p class="text-xs">
            {{ (product?.creator as Creator).nickname ?? (product?.creator as Creator)?.first_name ?? 'Creator' }}
          </p>
        </div>
      </div>
    </NuxtLinkLocale>
  </div>
</template>

<script lang="ts" setup>
import { getStatusColor } from '~~/shared/utils/ui';

const props = withDefaults(defineProps<{ product: Products, mode?: 'creator' | 'fan' | 'owner' }>(), {
  mode: 'fan',
});

const { directusAssetsUrl } = useDirectus();
const { formatProductPrice, formatProductDiscountPrice, productHasDiscount } = useCurrency();

// Get reactive translated product name and description
const translatedProductName = useTranslatedName(props.product.translations);
const translatedProductDescription = useTranslatedDescription(props.product.translations);

// Use currency composable for pricing logic with locale-based pricing
const hasDiscount = computed(() => productHasDiscount(props.product));
const formattedCurrentPrice = computed(() => formatProductPrice(props.product));
const formattedDiscountedPrice = computed(() => formatProductDiscountPrice(props.product));

// Use shared status color utility
const statusColorMap = computed(() => getStatusColor(props.product.status));
</script>
