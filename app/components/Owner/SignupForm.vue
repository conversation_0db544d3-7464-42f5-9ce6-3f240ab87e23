<template>
  <!-- Avatar Upload -->
  <UFormField name="avatar" class="mb-6 flex w-full flex-col px-1">
    <div class="mx-auto flex flex-row items-center space-x-4 sm:mx-0">
      <UAvatar
        :src="avatarPreview"
        :alt="state.firstName ?? ''"
        size="xl"
        class="h-20 w-20 sm:h-24 sm:w-24"
      />

      <UButton
        size="xl"
        color="secondary"
        class="rounded-full px-4 text-sm whitespace-nowrap"
        @click="triggerFileInput"
        leading-icon="heroicons:pencil-square"
      >
        {{ $t('global.change_avatar') }}
      </UButton>
    </div>
    
    <input ref="fileInput" type="file" accept="image/jpeg,image/jpg,image/png" class="hidden" @change="handleFileChange" />
    <div v-if="!avatarPreview && formErrors.avatar" class="text-warning-500 mt-2 text-sm">
      {{ formErrors.avatar }}
    </div>
  </UFormField>

  <UForm
    class="grid w-full gap-x-6 gap-y-4 lg:grid-cols-2"
    :schema="ownerSignupSchema"
    :state="state"
    @submit="onSubmit"
  >
    <!-- Company Name -->
    <UFormField name="companyName" :label="$t('global.company_name')" class="lg:col-span-2">
      <UInput v-model="state.companyName" type="text" id="companyName" size="lg" class="w-full" />
    </UFormField>

    <!-- Contact Person Name -->
    <UFormField name="firstName" :label="$t('global.ctc_person_name')"  class="lg:col-span-2">
      <UInput v-model="state.firstName" type="text" id="firstName" size="lg" class="w-full" />
    </UFormField>
    
    <UFormField name="email" :label="$t('global.email')"  class="lg:col-span-2">
      <UInput disabled v-model="state.email" type="text" id="email" size="lg" class="w-full" />
    </UFormField>

    <!-- Role in Company (Dropdown) -->
    <UFormField name="companyRole" :label="$t('global.company_role_signup')" class="lg:col-span-2">
      <USelect v-model="state.companyRole" :items="localizedBusinessRoles" id="companyRole" class="w-full" size="lg" />
    </UFormField>

    <!-- Company Homepage Link -->
    <UFormField name="homepageLink" class="lg:col-span-2">
      <template #label>{{ `${$t('global.company_homepage_link')}` }}</template>
      <UInput v-model="state.homepageLink" type="url" id="homepageLink" size="lg" class="w-full" />
    </UFormField>

    <!-- Business License Upload (Dropzone style) -->
    <UFormField name="businessLicense" class="lg:col-span-2" :label="$t('global.business_license')">
      <UIFileDropzone
        mode="file"
        :max-size="10 * 1024 * 1024"
        @file-selected="handleFileSelected"
        @file-removed="handleFileRemoved"
      />
    </UFormField>

    <!-- Country -->
    <UFormField name="country" :label="$t('global.country')" >
      <USelect v-model="state.country" :items="COUNTRIES[locale]" id="country" class="w-full" size="lg" />
    </UFormField>

    <!-- Company Phone Number -->
    <UFormField name="companyPhoneNumber" :label="$t('global.company_phone_signup')" class="lg:col-span-2">
      <UButtonGroup class="w-full">
        <USelect v-model="state.phonePrefix" :items="PHONE_PREFIXES" id="phonePrefix" size="lg" class="w-48" />
        <UInput v-model="state.companyPhoneNumber" type="tel" id="companyPhoneNumber" size="lg" class="w-full" />
      </UButtonGroup>
    </UFormField>

    <!-- Password -->
    <UFormField name="password" class="lg:col-span-2" :label="$t('global.create_your_password')">
      <UInput
        v-model="state.password"
        :type="showPassword ? 'text' : 'password'"
        id="password"
        class="w-full"
        size="lg"
        :placeholder="$t('global.input_password_placeholder')"
      >
        <template #trailing>
          <UButton
            variant="ghost"
            :icon="showPassword ? 'i-heroicons-eye-20-solid' : 'i-heroicons-eye-slash-20-solid'"
            size="xs"
            class="cursor-pointer rounded-full opacity-50"
            @click="showPassword = !showPassword"
          />
        </template>
      </UInput>
    </UFormField>

    <!-- Confirm Password -->
    <UFormField required name="confirmPassword" class="lg:col-span-2" :label="$t('global.re_enter_password')">
      <UInput
        v-model="state.confirmPassword"
        :type="showConfirmPassword ? 'text' : 'password'"
        id="confirmPassword"
        class="w-full"
        size="lg"
      >
        <template #trailing>
          <UButton
            variant="ghost"
            :icon="showConfirmPassword ? 'i-heroicons-eye-20-solid' : 'i-heroicons-eye-slash-20-solid'"
            size="xs"
            class="cursor-pointer rounded-full opacity-50"
            @click="showConfirmPassword = !showConfirmPassword"
          />
        </template>
      </UInput>
    </UFormField>
    
    <!-- Terms and Conditions -->
    <UFormField name="agreeTerms" class="lg:col-span-2">
      <div class="flex flex-row gap-2">
        <UCheckbox v-model="state.agreeTerms" />
        {{ $t('global.agree_with_terms') }}
        <ULink :to="`/pnc/privacy-policy-owner-pnc-${locale}.pdf`" target="_blank" class="cursor-pointer underline">
          {{ $t('global.privacy_policy') || 'Privacy Policy' }}
        </ULink>
      </div>
    </UFormField>

    <UButton
      color="primary"
      type="submit"
      size="lg"
      class="w-full justify-center rounded-full text-sm sm:text-base lg:col-span-2"
      :loading="isLoading"
    >
      {{ $t('global.sign_up') }}
    </UButton>
    
    <DevOnly>
      <UButton
        color="success"
        @click="fillUpMock"
        size="lg"
        class="w-full justify-center rounded-full text-sm sm:text-base lg:col-span-2"
      >
        {{ $t('dev_only.mock_fill_up') }}
      </UButton>
    </DevOnly>
  </UForm>
</template>

<script lang="ts" setup>
import * as z from 'zod/v4';
import type { FormSubmitEvent } from '@nuxt/ui';
import { COUNTRIES } from '~/utils/countries';

const { t, locale } = useI18n();
const { email } = useSignup();
const localePath = useLocalePath();

// Owner signup schema with password confirmation validation
const ownerSignupSchema = z.object({
  agreeTerms: z.boolean().refine((value) => value, {
    message: t('global.terms_agreement_required'),
  }),
  avatar: z.string(),
  email: z.string(),
  firstName: z.string().min(3, t('global.missing_name')),
  companyName: z.string().min(3, t('global.missing_company_name')),
  companyPhoneNumber: z.string().min(1, t('global.missing_phone')),
  companyRole: z.string().min(1, t('global.missing_role')),
  homepageLink: z.string().refine((val) => !val || z.string().url().safeParse(val).success, {
    message: t('global.invalid_url'),
  }),
  country: z.string().min(1, t('global.missing_country')),
  businessLicense: z.string(),
  businessLicenseFilename: z.string().optional(),
  password: z.string().min(5, t('global.password_requirements')),
  confirmPassword: z.string().min(5, t('global.passwords_must_match')),
  phonePrefix: z.string().optional(),
})

type OwnerSignupFormSchema = z.infer<typeof ownerSignupSchema>;

const toast = useToast();
const fileInput = ref<HTMLInputElement | null>(null);
const businessLicenseFile = ref<File | null>(null);

// Fix state initialization - use firstName instead of name
const state = useState<Partial<OwnerSignupFormSchema>>('ownerSignupState', () => ({
  agreeTerms: false,
  avatar: '',
  email: email.value!,
  firstName: '',
  companyName: '',
  companyPhoneNumber: '',
  companyRole: '',
  homepageLink: '',
  country: 'JP',
  businessLicense: '',
  businessLicenseFilename: '',
  password: '',
  confirmPassword: '',
  phonePrefix: '+81',
}));

// UI state
const showPassword = ref(false);
const showConfirmPassword = ref(false);
const isLoading = ref(false);
const avatarPreview = ref<string | undefined>(undefined);
const formErrors = ref<Record<string, string>>({});

const localizedBusinessRoles = computed(() => {
  return BUSINESS_ROLES.map((role: { value: string; label: string }) => ({
    value: role.value,
    label: t(role.label),
  }));
});

// Avatar upload functions
function triggerFileInput() {
  if (fileInput.value) {
    fileInput.value.click();
  }
}

function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    const file = target.files[0];

    // Validate file type - only allow JPEG and PNG
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type?.toLowerCase() || '')) {
      toast.add({
        title: t('global.error'),
        description: t('global.avatar_invalid_format') || 'Please upload only JPEG or PNG images',
        color: 'error',
      });
      // Clear the input
      target.value = '';
      return;
    }

    // Create a preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target && typeof e.target.result === 'string') {
        avatarPreview.value = e.target.result;
        state.value.avatar = e.target.result;
        // Clear any previous avatar error
        formErrors.value.avatar = '';
      }
    };
    reader.readAsDataURL(file as File);
  }
}

// Business license file handling functions for UIFileDropzone
function handleFileSelected(file: File) {
  businessLicenseFile.value = file;

  // Convert to base64 for upload
  const reader = new FileReader();
  reader.onload = (e) => {
    if (e.target && typeof e.target.result === 'string') {
      state.value.businessLicense = e.target.result;
      // Store the original filename to preserve it during upload
      state.value.businessLicenseFilename = file.name;
    }
  };
  reader.readAsDataURL(file);
}

function handleFileRemoved() {
  businessLicenseFile.value = null;
  state.value.businessLicense = '';
  state.value.businessLicenseFilename = '';
}

// Form submission
async function onSubmit(event: FormSubmitEvent<OwnerSignupFormSchema>) {
  // Validate avatar separately since it's not directly tied to the form input
  if (!avatarPreview.value) {
    formErrors.value.avatar = t('global.avatar_required');
    return;
  }

  await ownerSignup(event.data);
}

// Mock data for development
function fillUpMock() {
  state.value = {
    agreeTerms: true,
    avatar: '',
    email: email.value!,
    firstName: 'Jane Owner',
    companyName: 'ACME Corporation',
    companyPhoneNumber: '**********',
    companyRole: 'ceo_founder',
    homepageLink: 'https://acme.com',
    country: 'JP',
    businessLicense: '',
    businessLicenseFilename: '',
    password: 'DemoDemo1',
    confirmPassword: 'DemoDemo1',
    phonePrefix: '+81',
  };
}

// Owner Signup
async function ownerSignup(data: OwnerSignupFormSchema) {
  isLoading.value = true;

  try {
    await $fetch('/api/signup', {
      method: 'POST',
      body: {
        ...data,
        role: 'owner',
      },
    });

    // Show success notification
    toast.add({
      color: 'success',
      title: t('global.success'),
      description: t('global.signup_success'),
    });
    
    navigateTo(localePath('owner-signup-completed'), { replace: true });
  } catch (error) {
    // Show error notification
    console.error('Failed to complete owner signup', error);
    toast.add({
      title: t('global.error'),
      description: t('global.form_validation_error'),
    });
  } finally {
    isLoading.value = false;
  }
}
</script>
