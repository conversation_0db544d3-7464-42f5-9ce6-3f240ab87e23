<template>
  <!-- Add scrollable container for form overflow -->
  <div class="h-full overflow-y-auto">
    <UForm :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
      <!-- Status -->
       <div class="flex justify-between">
        <div>
          <p>{{ $t('global.status') }}</p>
          <p class="text-sm capitalize text-muted">{{ ip.status }}</p>
        </div>

        <UModal  v-model:open="isIpStatusModalOpen" :title="ip.status === 'published' ? $t('global.deactivate_ip') : $t('global.activate_ip')">
          <UButton variant="ghost" color="warning">
            {{ ip.status === 'published' ? $t('global.deactivate_ip') : $t('global.activate_ip') }}
          </UButton>
          <template #content>
            <div class="p-6 grid gap-4">
              <p class="text-lg font-semibold">{{ ip.status === 'published' ? $t('global.deactivate_ip') : $t('global.activate_ip') }}</p>
              <p class="text-muted whitespace-pre-line">{{ ip.status === 'published' ? $t('global.are_you_sure_deactivate') : $t('global.are_you_sure_activate') }}</p>
              <div class="grid grid-cols-2 gap-4">
                <UButton
                  color="secondary"
                  variant="soft" size="lg"
                  class="justify-center rounded-full"
                  @click="isIpStatusModalOpen = false"
                >
                  {{ $t('global.cancel') }}
                </UButton>
                <UButton color="warning" size="lg" class="justify-center rounded-full" @click="toggleIpStatus">
                 {{ ip.status === 'published' ? $t('global.deactivate_ip') : $t('global.activate_ip') }}
                </UButton>
              </div>
            </div>
          </template>
        </UModal>
      </div>

      <!-- IP Name -->
      <UFormField :label="$t('global.ip_name')" name="name">
        <UInput v-model="state.name" class="w-full" />
      </UFormField>

      <!-- IP Description -->
      <UFormField :label="$t('global.ip_description')" name="description">
        <UTextarea v-model="state.description" class="w-full" autoresize :rows="10" />
      </UFormField>
      
      <!-- IP Category -->
      <UFormField :label="$t('global.ip_category')" name="category">
        <USelect v-model="state.category" :items="localizedIpCategoryItems" class="w-full" />
      </UFormField>

      <!-- IP Images Dropzone -->
      <ProductImageDropzone
        v-model="state.images!"
        :product-name="state.name"
        :max-images="1"
        :existing-images="existingImages"
      />

      <UButton type="submit" :loading="isSubmitting">
        {{ $t('global.save_changes') }}
      </UButton>
    </UForm>
  </div>
</template>

<script lang="ts" setup>
import z from 'zod/v4';
import type { FormSubmitEvent } from '@nuxt/ui';
import { UTextarea } from '#components';

interface Props {
  ip: Ip;
}

const props = defineProps<Props>();
const emit = defineEmits(['updated', 'close', 'toggleStatus']);

const { t, locale } = useI18n();
const { useDirectusFetch, directusAssetsUrl } = useDirectus();

// Form validation schema
const schema = z.object({
  name: z.string().min(3, t('global.ip_name_required')),
  description: z.string().min(10, t('global.ip_description_required')),
  category: z.number().min(1, t('global.ip_category_required')),
  images: z.array(z.string()).min(1, t('global.at_least_one_image_required')),
  keywords: z.string().optional(),
});

type Schema = z.output<typeof schema>

const isIpStatusModalOpen = ref(false);

// Get current translation for the IP
function getCurrentTranslation() {
  if (!props.ip.translations) return { name: '', description: '' };

  const translatedName = useTranslatedName(props.ip.translations).value;
  const translatedDescription = useTranslatedDescription(props.ip.translations).value;

  return {
    name: translatedName || '',
    description: translatedDescription || ''
  };
}

// Helper function to extract category ID
function getCategoryId() {
  if (typeof props.ip.category === 'number') {
    return props.ip.category;
  }
  if (props.ip.category && typeof props.ip.category === 'object' && 'id' in props.ip.category) {
    return props.ip.category.id;
  }
  return 0;
}

async function toggleIpStatus() {
  isIpStatusModalOpen.value = false;
  emit('toggleStatus')
  emit('close')
}

// Helper function to extract image IDs
function getImageIds() {
  if (!props.ip.images || !Array.isArray(props.ip.images)) {
    return [];
  }

  return props.ip.images.map((img: any) => {
    // Handle different possible structures
    if (typeof img === 'string') {
      return img;
    }
    if (img && typeof img === 'object') {
      // Handle { directus_files_id: "uuid" } structure
      if (typeof img.directus_files_id === 'string') {
        return img.directus_files_id;
      }
      // Handle { directus_files_id: { id: "uuid" } } structure
      if (img.directus_files_id && typeof img.directus_files_id === 'object' && img.directus_files_id.id) {
        return img.directus_files_id.id;
      }
      // Handle { id: "uuid" } structure
      if (img.id) {
        return img.id;
      }
    }
    return null;
  }).filter(Boolean); // Remove any null values
}

// Extract existing images for ProductImageDropzone
const existingImages = computed(() => {
  if (!props.ip.images || props.ip.images.length === 0) return [];

  return props.ip.images.map((img: any) => {
    const fileId = typeof img.directus_files_id === 'string' ? img.directus_files_id : img.directus_files_id?.id;
    return {
      id: fileId,
      preview: directusAssetsUrl(fileId, 400, 400) || '/images/missing-product.png',
      uploaded: true,
    };
  });
});

// Initialize form state with existing IP data
const currentTranslation = getCurrentTranslation();
const state = reactive<Partial<Schema>>({
  name: currentTranslation.name || '',
  description: currentTranslation.description || '',
  category: getCategoryId(),
  images: getImageIds(),
  keywords: props.ip.keywords || '',
})

const isSubmitting = ref(false);
const toast = useToast();

// Fetch IP categories
const { data: ipCategories } = useDirectusFetch<IpCategories[]>('/items/ip_categories', {
  key: 'all-ip-categories',
  params: {
    fields: ['id', 'translations.*'],
  },
});

// Localized IP category items for dropdown
const localizedIpCategoryItems = computed(() => {
  if (!ipCategories.value) return [];
  return ipCategories.value.map((item) => ({
    label: item.translations[localeToIndex(locale.value)].name ?? '',
    value: item.id,
  }));
});

// Auto-generate keywords from name and description
watch([() => state.name, () => state.description], () => {
  if (state.name || state.description) {
    // Combine name and description, remove special characters, and create keywords
    const combined = `${state.name} ${state.description}`.toLowerCase();
    const keywords = combined
      .replace(/[^\w\s]/g, ' ') // Remove special characters
      .split(/\s+/) // Split by whitespace
      .filter(word => word.length > 2) // Filter out short words
      .slice(0, 10) // Limit to 10 keywords
      .join(', ');
    
    state.keywords = keywords;
  }
});

// Form submission handler
async function onSubmit(event: FormSubmitEvent<Schema>) {
  isSubmitting.value = true;

  try {
    // Use the update endpoint for IP updates
    const response = await $fetch(`/api/owner/ip/${props.ip.id}/update`, {
      method: 'PATCH',
      body: {
        ...event.data,
        locale: locale.value,
      }
    });

    if (response.success) {
      toast.add({
        title: t('global.success'),
        description: t('global.ip_updated_successfully'),
        color: 'success'
      });
      emit('updated');
    }
  } catch (error) {
    console.error('Failed to update IP:', error);
    toast.add({
      title: t('global.error'),
      description: t('global.unexpected_error'),
      color: 'error',
    });
  } finally {
    isSubmitting.value = false;
  }
}
</script>
