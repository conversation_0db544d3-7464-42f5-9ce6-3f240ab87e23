<template>
  <UModal
    v-if="product?.status === 'pending'"
    v-model:open="isApprovalModalOpen"
    :title="mode === 'approve' ? $t('global.approve') : $t('global.reject')"
    :ui="{ header: 'border-b-0' }"
  >
    <slot/>
    <template #body>
      <UForm :schema="schema" :state="state" class="-mt-6" @submit="onApprovalSubmit">
      <div class="p-4 grid gap-4">
        <p class="text-muted">{{ mode === 'approve' ? $t('global.confirm_approve') : $t('global.confirm_reject') }}</p>
          <UFormField :label="$t('global.comment')" name="comment">
          <UTextarea 
            v-model="state.comment" 
            class="w-full" 
            autoresize 
            :rows="3"
            :placeholder="$t('global.approval_comment_placeholder')" />
          </UFormField>
          <div class="grid grid-cols-2 gap-4">
            <UButton
              color="secondary"
              variant="soft" size="lg"
              class="justify-center rounded-full"
              @click="isApprovalModalOpen = false"
            >
              {{ $t('global.cancel') }}
            </UButton>
            <UButton 
              type="submit" 
              :color="mode === 'approve' ? 'success' : 'warning'" 
              size="lg" 
              class="justify-center rounded-full"
            >
              {{ $t('global.confirm') }}
            </UButton>
          </div>
          
        </div>
      </UForm>
    </template>
  </UModal>
</template>

<script setup lang="ts">
import { z } from 'zod/v4';

const props = defineProps<{
  product: Products;
  mode: 'approve' | 'reject';
}>();

const emit = defineEmits(['submit']);
const isApprovalModalOpen = ref(false);
const toast = useToast();
const { t } = useI18n();
const { useDirectusFetch } = useDirectus();

const schema = z.object({
  comment: z.string().min(8, t('global.required_fields'))
})

type Schema = z.output<typeof schema>

const state = reactive<Partial<Schema>>({
  comment: '',
})

const onApprovalSubmit = async () => {
  if (!props.mode) return;

  const body = {
    approval_comment: state.comment || null,
    keys: [props.product?.id]
  }
  try {
  //   // Update product status and approval comment using useDirectusFetch
    const { execute: executeApprove } = await useDirectusFetch(`/flows/trigger/3ff36cf2-7d3a-44bf-b20b-6efbbbaf5184`, {
      method: 'POST',
      body,
      immediate: false,
      server: false
    });

    const { execute: executeReject } = await useDirectusFetch(`/flows/trigger/f9935347-7156-4f32-9f02-e6e09f1b8fa2`, {
      method: 'POST',
      body,
      immediate: false,
      server: false
    });

    if (props.mode === 'approve') {
      await executeApprove();
    } else {
      await executeReject();
    }

    emit('submit');

    // Show success message
    toast.add({
      title: props.mode === 'approve'
        ? t('global.product_approved_successfully')
        : t('global.product_rejected_successfully'),
      color: 'success',
    });

    // Close modal
    isApprovalModalOpen.value = false;

    // Reset form state
    state.comment = '';

  } catch (error) {
    console.error('Failed to update product approval:', error);
    toast.add({
      title: t('global.unexpected_error'),
      color: 'error',
    });
  }
}
</script>