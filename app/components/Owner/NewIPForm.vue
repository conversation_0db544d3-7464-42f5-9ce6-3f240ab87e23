<template>
  <!-- Add scrollable container for form overflow -->
  <div class="h-full overflow-y-auto">
    <UForm :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
      <!-- IP Name -->
      <UFormField :label="$t('global.ip_name')" name="name">
        <UInput v-model="state.name" class="w-full" />
      </UFormField>

      <!-- IP Description -->
      <UFormField :label="$t('global.ip_description')" name="description">
        <UTextarea v-model="state.description" class="w-full"/>
      </UFormField>
      <!-- IP Category -->
      <UFormField :label="$t('global.ip_category')" name="category">
        <USelect v-model="state.category" :items="localizedIpCategoryItems" class="w-full" />
      </UFormField>

      <!-- IP Images Dropzone -->
      <ProductImageDropzone
        v-model="state.images!"
        :product-name="state.name"
        :max-images="5"
      />

      <UButton type="submit" :loading="isSubmitting">
        {{ $t('global.create_ip') }}
      </UButton>
    </UForm>
  </div>
</template>

<script lang="ts" setup>
import z from 'zod/v4';
import type { FormSubmitEvent } from '@nuxt/ui';
import { UTextarea } from '#components';

const { t, locale } = useI18n();
const { useDirectusFetch } = useDirectus();

const emit = defineEmits(['close']);

// Form validation schema
const schema = z.object({
  name: z.string().min(3, t('global.ip_name_required')),
  description: z.string().min(10, t('global.ip_description_required')),
  category: z.number().min(1, t('global.ip_category_required')),
  images: z.array(z.string()).min(1, t('global.at_least_one_image_required')),
  keywords: z.string().optional(),
});

type Schema = z.output<typeof schema>

// Form state
const state = reactive<Partial<Schema>>({
  name: '',
  description: '',
  category: 0,
  images: [],
  keywords: '',
})

const isSubmitting = ref(false);
const toast = useToast();

// Fetch IP categories
const { data: ipCategories } = useDirectusFetch<IpCategories[]>('/items/ip_categories', {
  key: 'all-ip-categories',
  params: {
    fields: ['id', 'translations.*'],
  },
});

// Localized IP category items for dropdown
const localizedIpCategoryItems = computed(() => {
  if (!ipCategories.value) return [];
  return ipCategories.value.map((item) => ({
    label: item.translations[localeToIndex(locale.value)].name ?? '',
    value: item.id,
  }));
});

// Auto-generate keywords from name and description
watch([() => state.name, () => state.description], () => {
  if (state.name || state.description) {
    // Combine name and description, remove special characters, and create keywords
    const combined = `${state.name} ${state.description}`.toLowerCase();
    const keywords = combined
      .replace(/[^\w\s]/g, ' ') // Remove special characters
      .split(/\s+/) // Split by whitespace
      .filter(word => word.length > 2) // Filter out short words
      .slice(0, 10) // Limit to 10 keywords
      .join(', ');
    
    state.keywords = keywords;
  }
});

// Form submission handler
async function onSubmit(event: FormSubmitEvent<Schema>) {
  isSubmitting.value = true;
  
  try {
    // Create IP using server endpoint
    const response = await $fetch('/api/owner/ip/create', {
      method: 'POST',
      body: {
        ...event.data,
        locale: locale.value,
      }
    });

    if (response.success) {
      toast.add({
        title: t('global.success'),
        description: t('global.ip_created_successfully'),
        color: 'success'
      });
      emit('close');
    }
  } catch (error) {
    console.error('Failed to create IP:', error);
    toast.add({
      title: t('global.error'),
      description: t('global.unexpected_error'),
      color: 'error',
    });
  } finally {
    isSubmitting.value = false;
  }
}
</script>
