<template>
  <!-- Avatar Upload -->
  <UFormField name="avatar" class="mb-6 flex w-full flex-col px-1">
    <div class="mx-auto flex flex-row items-center space-x-4 sm:mx-0">
      <UAvatar
        :src="avatarPreview"
        :alt="state.firstName ? state.firstName : ''"
        size="xl"
        class="h-20 w-20 sm:h-24 sm:w-24"
      />

      <UButton
        size="xl"
        color="secondary"
        class="rounded-full px-4 text-sm whitespace-nowrap"
        @click="triggerFileInput"
        leading-icon="heroicons:pencil-square"
      >
        {{ $t('global.change_avatar') }}
      </UButton>
    </div>

    <input ref="fileInput" type="file" accept="image/jpeg,image/jpg,image/png" class="hidden" @change="handleFileChange" />
  </UFormField>

  <UForm class="grid w-full gap-x-6 gap-y-4 lg:grid-cols-2" :schema="editSchema" :state="state" @submit="onSubmit">
    <UFormField name="email" :label="$t('global.email')">
      <UInput disabled :value="state.email" type="text" id="email" size="lg" class="w-full" />
    </UFormField>

    <!-- Company Name -->
    <UFormField name="companyName" :label="$t('global.company_name')">
      <UInput v-model="state.companyName" type="text" id="companyName" size="lg" class="w-full" />
    </UFormField>

    <!-- Contact Person Name -->
    <UFormField name="firstName" :label="$t('global.ctc_person_name')" class="lg:col-span-2">
      <UInput v-model="state.firstName" type="text" id="firstName" size="lg" class="w-full" />
    </UFormField>

    <!-- Role in Company (Dropdown) -->
    <UFormField name="companyRole" :label="$t('global.company_role_signup')" class="lg:col-span-2">
      <USelect v-model="state.companyRole" :items="localizedBusinessRoles" id="companyRole" class="w-full" size="lg" />
    </UFormField>

    <!-- Company Homepage Link -->
    <UFormField name="homepageLink" class="lg:col-span-2">
      <template #label>{{ `${$t('global.company_homepage_link')}` }}</template>
      <UInput v-model="state.homepageLink" type="url" id="homepageLink" size="lg" class="w-full" />
    </UFormField>

    <!-- Business License Upload (Dropzone style) -->
    <UFormField name="businessLicense" class="lg:col-span-2" :label="$t('global.business_license')">
      <!-- Show existing business license if present -->
      <div v-if="existingBusinessLicense && !businessLicenseFile" class="mb-3 p-3 bg-gray-50 rounded-lg border">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <UIcon name="i-heroicons-document-text" class="h-5 w-5 text-gray-500" />
            <span class="text-sm text-gray-700">{{ businessLicenseFilename }}</span>
          </div>
          <UButton
            size="sm"
            tag="a"
            :href="businessLicenseUrl || undefined"
            target="_blank"
            trailing-icon="i-heroicons-arrow-top-right-on-square"
          >
            {{ $t('global.view_file') }}
          </UButton>
        </div>
        <p class="text-xs text-gray-500 mt-1">{{ $t('global.upload_new_to_replace') }}</p>
      </div>

      <!-- Dropzone for new upload -->
      <UIFileDropzone
        mode="file"
        :max-size="10 * 1024 * 1024"
        @file-selected="handleFileSelected"
        @file-removed="handleFileRemoved"
      />
    </UFormField>

    <!-- Country -->
    <UFormField name="country" :label="$t('global.country')">
      <USelect v-model="state.country" :items="COUNTRIES[locale]" id="country" class="w-full" size="lg" />
    </UFormField>

    <!-- Company Phone Number -->
    <UFormField name="companyPhoneNumber" :label="$t('global.company_phone_signup')">
      <UButtonGroup class="w-full">
        <USelect v-model="state.phonePrefix" :items="PHONE_PREFIXES" id="phonePrefix" size="lg" class="w-48" />
        <UInput v-model="state.companyPhoneNumber" type="tel" id="companyPhoneNumber" size="lg" class="w-full" />
      </UButtonGroup>
    </UFormField>

    <UButton
      color="primary"
      type="submit"
      size="lg"
      class="w-full justify-center rounded-full text-sm sm:text-base lg:col-span-2"
      :loading="isLoading"
    >
      {{ $t('global.save_changes') }}
    </UButton>
  </UForm>
</template>

<script lang="ts" setup>
import * as z from 'zod/v4';
import type { FormSubmitEvent } from '@nuxt/ui';
import { COUNTRIES } from '~/utils/countries';

const emit = defineEmits(['updated']);

const { t, locale } = useI18n();
const { directusAssetsUrl } = useDirectus();

// Edit schema (no password or terms required)
const editSchema = z.object({
  avatar: z.string(),
  email: z.string(),
  firstName: z.string().min(3, t('global.missing_name')),
  companyName: z.string().min(3, t('global.missing_company_name')),
  companyPhoneNumber: z.string().min(1, t('global.missing_phone')),
  companyRole: z.string().min(1, t('global.missing_role')),
  homepageLink: z.string().refine((val) => !val || z.string().url().safeParse(val).success, {
    message: t('global.invalid_url'),
  }),
  country: z.string().min(1, t('global.missing_country')),
  businessLicense: z.string().optional(),
  phonePrefix: z.string().optional(),
});

type EditFormSchema = z.infer<typeof editSchema>;

const toast = useToast();
const fileInput = ref<HTMLInputElement | null>(null);

const state = ref<Partial<EditFormSchema>>({
  avatar: '',
  email: '',
  firstName: '',
  companyName: '',
  companyPhoneNumber: '',
  companyRole: '',
  homepageLink: '',
  country: '',
  businessLicense: '',
  phonePrefix: '',
});

// UI state
const isLoading = ref(false);
const avatarPreview = ref<string | undefined>(undefined);
const avatarFile = ref<File | null>(null);
const businessLicenseFile = ref<File | null>(null);
const existingBusinessLicense = ref<any | null>(null);

// Computed property for business license URL
const businessLicenseUrl = computed(() => {
  if (existingBusinessLicense.value) {
    const fileId = typeof existingBusinessLicense.value === 'string'
      ? existingBusinessLicense.value
      : existingBusinessLicense.value.id;
    return directusAssetsUrl(fileId);
  }
  return null;
});

// Computed property for business license filename
const businessLicenseFilename = computed(() => {
  if (existingBusinessLicense.value && typeof existingBusinessLicense.value === 'object') {
    return existingBusinessLicense.value.filename_download || existingBusinessLicense.value.id ;
  }
  return '{{ $t("global.business_license") }}';
});

// Business roles dropdown
const localizedBusinessRoles = computed(() => {
  return BUSINESS_ROLES.map((role: { value: string; label: string }) => ({
    value: role.value,
    label: t(role.label),
  }));
});

// Avatar upload functions
function triggerFileInput() {
  if (fileInput.value) {
    fileInput.value.click();
  }
}

function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (file) {
    // Validate file type - only allow JPEG and PNG
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type?.toLowerCase() || '')) {
      toast.add({
        title: t('global.error'),
        description: t('global.avatar_invalid_format') || 'Please upload only JPEG or PNG images',
        color: 'error',
      });
      // Clear the input
      target.value = '';
      return;
    }

    avatarFile.value = file;

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target && typeof e.target.result === 'string') {
        avatarPreview.value = e.target.result;
      }
    };
    reader.readAsDataURL(file);
  }
}

// Business license file handling functions for UIFileDropzone
function handleFileSelected(file: File) {
  businessLicenseFile.value = file;

  // Convert to base64 for upload
  const reader = new FileReader();
  reader.onload = (e) => {
    if (e.target && typeof e.target.result === 'string') {
      state.value.businessLicense = e.target.result;
    }
  };
  reader.readAsDataURL(file);
}

function handleFileRemoved() {
  businessLicenseFile.value = null;
  state.value.businessLicense = '';
}

// Load user profile data
async function loadUserProfile() {
  isLoading.value = true;
  try {
    // Fetch user data with owner profile
    const data = await $fetch('/api/owner/profile');
    const ownerProfile = data.ip_owner_profile?.[0];

    // Populate form with user data
    state.value.email = data.email ?? '';
    state.value.firstName = data.first_name ?? '';
    
    // Populate form with owner profile data
    if (ownerProfile) {
      console.log('🔍 DEBUG - Owner profile data:', ownerProfile);
      state.value.companyName = ownerProfile.company_name ?? '';
      state.value.companyRole = ownerProfile.company_role ?? '';
      state.value.homepageLink = ownerProfile.homepage_link ?? '';
      state.value.country = ownerProfile.country ?? '';

      // Set existing business license for display (don't put in form state)
      if (ownerProfile.business_license) {
        console.log('🔍 DEBUG - Business license found:', ownerProfile.business_license);
        existingBusinessLicense.value = ownerProfile.business_license;
        console.log('🔍 DEBUG - Business license URL:', directusAssetsUrl(ownerProfile.business_license));
      } else {
        console.log('🔍 DEBUG - No business license found in owner profile');
      }

      // Parse phone number (extract prefix and number)
      const fullPhone = ownerProfile.company_phone_number ?? '';
      if (fullPhone) {
        // Find matching prefix
        const matchingPrefix = PHONE_PREFIXES.find(prefix => fullPhone.startsWith(prefix.value));
        if (matchingPrefix) {
          state.value.phonePrefix = matchingPrefix.value;
          state.value.companyPhoneNumber = fullPhone.substring(matchingPrefix.value.length);
        } else {
          state.value.companyPhoneNumber = fullPhone;
        }
      }
    }

    // Set avatar preview if exists
    if (data.avatar) {
      state.value.avatar = data.avatar;
      avatarPreview.value = directusAssetsUrl(data.avatar);
    }
  } catch (error) {
    console.error('Failed to load user profile:', error);
    toast.add({
      color: 'error',
      title: t('global.error'),
      description: t('global.failed_to_load_profile'),
    });
  } finally {
    isLoading.value = false;
  }
}

// Form submission
async function onSubmit(_event: FormSubmitEvent<EditFormSchema>) {
  await updateProfile();
}

// Update profile function
async function updateProfile() {
  isLoading.value = true;

  try {
    let avatarId = state.value.avatar; // Keep existing avatar ID by default

    // If user selected a new avatar file, upload it first
    if (avatarFile.value && avatarPreview.value?.startsWith('data:')) {
      const uploadResponse = await $fetch<{ fileId: string; success: boolean }>('/api/upload-avatar', {
        method: 'POST',
        body: {
          avatar: avatarPreview.value, // base64 data
          filename: `${state.value.firstName}-avatar-updated`,
        },
      });
      avatarId = uploadResponse.fileId;
    }

    // Update owner profile with all data
    await $fetch('/api/owner/update-profile', {
      method: 'PATCH',
      body: {
        first_name: state.value.firstName,
        company_name: state.value.companyName,
        company_phone_number: (state.value.phonePrefix || '') + (state.value.companyPhoneNumber || ''),
        company_role: state.value.companyRole,
        homepage_link: state.value.homepageLink || null,
        country: state.value.country,
        business_license: businessLicenseFile.value && state.value.businessLicense?.startsWith('data:')
          ? state.value.businessLicense // Send base64 data for server to upload
          : undefined, // Don't update if no new file
        business_license_filename: businessLicenseFile.value?.name, // Send original filename
        avatar: avatarId, // Use either existing ID or new uploaded ID
      },
    });

    // Show success notification
    toast.add({
      color: 'success',
      title: t('global.success'),
      description: t('global.profile_updated'),
    });

    // Emit updated event to parent
    emit('updated');
  } catch (error) {
    console.error('Failed to update profile:', error);
    toast.add({
      color: 'error',
      title: t('global.error'),
      description: t('global.failed_to_update_profile'),
    });
  } finally {
    isLoading.value = false;
  }
}

// Load profile data when component mounts
onMounted(() => {
  loadUserProfile();
});
</script>
