<template>
  <div
    :class="[
      'flex items-center justify-center bg-gradient-to-br from-gray-500 to-gray-600 font-bold text-white',
      shapeClasses,
      sizeClasses,
    ]"
  >
    {{ initials }}
  </div>
</template>

<script lang="ts" setup>
interface Props {
  name: string;
  shape?: 'circle' | 'square' | 'squircle';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

const props = withDefaults(defineProps<Props>(), {
  shape: 'circle',
  size: 'md',
});

// Generate initials from the name
const initials = computed(() => {
  if (!props.name) return '?';

  const words = props.name
    .trim()
    .split(/\s+/)
    .filter((word) => word.length > 0);

  if (words.length === 0) return '?';

  if (words.length === 1) {
    // Single word: take first 2 characters
    const firstWord = words[0];
    return firstWord ? firstWord.substring(0, 2).toUpperCase() : '?';
  } else {
    // Multiple words: take first character of first 2 words
    return words
      .slice(0, 2)
      .map((word) => word.charAt(0))
      .join('')
      .toUpperCase();
  }
});

// Shape classes
const shapeClasses = computed(() => {
  switch (props.shape) {
    case 'circle':
      return 'rounded-full';
    case 'square':
      return 'rounded-none';
    case 'squircle':
      return 'rounded-2xl';
    default:
      return 'rounded-full';
  }
});

// Size classes
const sizeClasses = computed(() => {
  switch (props.size) {
    case 'xs':
      return 'w-6 h-6 text-xs';
    case 'sm':
      return 'w-8 h-8 text-sm';
    case 'md':
      return 'w-12 h-12 text-base';
    case 'lg':
      return 'w-16 h-16 text-lg';
    case 'xl':
      return 'w-20 h-20 text-xl';
    default:
      return 'w-12 h-12 text-base';
  }
});
</script>
