<template>
  <div>
    <!-- Hidden File Input -->
    <input
      ref="fileInputRef"
      type="file"
      :accept="defaultAccept"
      :multiple="maxFiles > 1"
      @change="handleFileSelect"
      class="hidden"
    />

    <!-- Dropzone Area -->
    <div
      ref="dropzoneRef"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @click="triggerFileInput"
      :class="[
        'relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
        isDragOver ? 'border-primary-500 bg-primary-50 dark:bg-primary-950/20' : 'border-gray-300 dark:border-gray-600',
        'hover:border-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800/50'
      ]"
    >
      <!-- Initial Upload Info (hidden when limit is reached) -->
      <div v-if="!isLimitReached" class="flex flex-col items-center space-y-2">
        <UIcon :name="defaultIcon" class="w-12 h-12 text-gray-400" />
        <div class="text-sm text-gray-600 dark:text-gray-400">
          <span class="font-medium text-primary-600 dark:text-primary-400">
            {{ defaultClickText }}
          </span>
          {{ dragText || $t('global.or_drag_and_drop') }}
        </div>
        <p class="text-xs text-gray-500">
          {{ defaultHelpText }}
        </p>
      </div>

      <!-- Selected File Display -->
      <div v-if="selectedFile" class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <UIcon name="i-heroicons-document" class="w-5 h-5 text-gray-500" />
            <span class="text-sm text-gray-700 dark:text-gray-300">{{ selectedFile.name }}</span>
            <span class="text-xs text-gray-500">({{ formatFileSize(selectedFile.size) }})</span>
          </div>
          <UButton
            icon="i-heroicons-x-mark"
            size="xs"
            color="neutral"
            variant="ghost"
            @click.stop="removeFile"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  accept?: string
  mode?: 'file' | 'image'
  icon?: string
  clickText?: string
  dragText?: string
  helpText?: string
  maxSize?: number // in bytes
  maxFiles?: number // maximum number of files
}

const { t } = useI18n()

const props = withDefaults(defineProps<Props>(), {
  accept: '*/*',
  mode: 'file',
  maxSize: 10 * 1024 * 1024, // 10MB default
  maxFiles: 1 // default to single file
})

// Computed properties for mode-specific defaults
const defaultIcon = computed(() => {
  if (props.icon) return props.icon
  return props.mode === 'image' ? 'i-heroicons-photo' : 'i-heroicons-document-arrow-up'
})

const defaultAccept = computed(() => {
  if (props.accept !== '*/*') return props.accept
  return props.mode === 'image' ? 'image/*' : '*/*'
})

const defaultClickText = computed(() => {
  if (props.clickText) return props.clickText
  return props.mode === 'image' ? t('global.click_to_upload_image') : t('global.click_to_upload_file')
})

const defaultHelpText = computed(() => {
  if (props.helpText) return props.helpText
  return `${t('global.up_to')} ${formatFileSize(props.maxSize)}`
})

// Check if file limit is reached
const isLimitReached = computed(() => {
  return selectedFile.value !== null && props.maxFiles === 1
})

const emit = defineEmits<{
  fileSelected: [file: File]
  fileRemoved: []
}>()

// Refs
const fileInputRef = ref<HTMLInputElement>()
const dropzoneRef = ref<HTMLDivElement>()
const isDragOver = ref(false)
const selectedFile = ref<File | null>(null)

// Methods
const triggerFileInput = () => {
  fileInputRef.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    processFile(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
  
  const file = event.dataTransfer?.files[0]
  if (file) {
    processFile(file)
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
}

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  // Only set to false if leaving the dropzone itself
  if (!dropzoneRef.value?.contains(event.relatedTarget as Node)) {
    isDragOver.value = false
  }
}

const processFile = (file: File) => {
  // Check file limit (for single file mode, replace existing file)
  if (props.maxFiles === 1 && selectedFile.value) {
    // Replace existing file for single file mode
    selectedFile.value = null
  }

  // Check file size
  if (file.size > props.maxSize) {
    // You can emit an error event or show a toast here
    console.error(`File size exceeds ${formatFileSize(props.maxSize)} limit`)
    return
  }

  selectedFile.value = file
  emit('fileSelected', file)
}

const removeFile = () => {
  selectedFile.value = null
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }
  emit('fileRemoved')
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Expose methods for parent component
defineExpose({
  removeFile,
  selectedFile: readonly(selectedFile)
})
</script>
