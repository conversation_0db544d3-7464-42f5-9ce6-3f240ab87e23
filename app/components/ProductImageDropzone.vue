<template>
  <UFormField :label="$t('global.product_images')" name="images">
    <div class="space-y-4">
      <!-- Image previews -->
      <div v-if="images.length > 0" class="space-y-4">
        <!-- Main image (first image) - full width -->
        <div
          v-if="images[0]"
          class="relative group w-full h-64 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700"
        >
          <!-- Main image badge -->
          <UBadge
            class="absolute top-2 left-2 z-10"
            color="primary"
            variant="solid"
            size="xs"
          >
            {{ $t('global.main') }}
          </UBadge>

          <!-- Main image preview -->
          <NuxtImg
            :src="images[0].preview"
            :alt="'Main product image'"
            class="w-full h-full object-cover"
          />

          <!-- Remove button for main image -->
          <UButton
            @click="removeImage(0)"
            icon="i-heroicons-x-mark"
            size="xs"
            color="error"
            variant="solid"
            class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
          />

          <!-- Reorder buttons for main image (only if there are other images) -->
          <!-- <div v-if="images.length > 1" class="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <UButton
              @click="moveImage(0, 1)"
              icon="i-heroicons-arrow-right"
              size="xs"
              color="neutral"
              variant="solid"
            />
          </div> -->
        </div>

        <!-- Additional images grid (excluding the first image) -->
        <div v-if="images.length > 1" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          <div
            v-for="(image, index) in images.slice(1)"
            :key="image.id || index"
            class="relative group aspect-square rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700"
          >
            <!-- Image preview -->
            <img
              :src="image.preview"
              :alt="`Product image ${index + 2}`"
              class="w-full h-full object-cover"
            />

            <!-- Remove button -->
            <UButton
              @click="removeImage(index + 1)"
              icon="i-heroicons-x-mark"
              size="xs"
              color="error"
              variant="solid"
              class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
            />

            <!-- Reorder buttons -->
            <!-- <div class="absolute bottom-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <UButton
                @click="moveImage(index + 1, index)"
                icon="i-heroicons-arrow-left"
                size="xs"
                color="neutral"
                variant="solid"
              />
              <UButton
                v-if="index + 1 < images.length - 1"
                @click="moveImage(index + 1, index + 2)"
                icon="i-heroicons-arrow-right"
                size="xs"
                color="neutral"
                variant="solid"
              />
            </div> -->
          </div>
        </div>
      </div>

      <!-- Dropzone Area -->
      <div
        ref="dropzoneRef"
        @drop="handleDrop"
        @dragover="handleDragOver"
        @dragenter="handleDragEnter"
        @dragleave="handleDragLeave"
        @click="triggerFileInput"
        :class="[
          'relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
          isDragOver ? 'border-primary-500 bg-primary-50 dark:bg-primary-950/20' : 'border-gray-300 dark:border-gray-600',
          'hover:border-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800/50'
        ]"
      >
        <div class="flex flex-col items-center space-y-2">
          <UIcon name="i-heroicons-photo" class="w-12 h-12 text-gray-400" />
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <span class="font-medium text-primary-600 dark:text-primary-400">
              {{ $t('global.click_to_upload') }}
            </span>
            {{ $t('global.or_drag_and_drop') }}
          </div>
          <p class="text-xs text-gray-500">
            {{ $t('global.png_jpg_up_to_5_images') }}
          </p>
        </div>
        
        <!-- Loading overlay -->
        <div v-if="isUploading" class="absolute inset-0 bg-white/80 dark:bg-gray-900/80 rounded-lg flex items-center justify-center">
          <div class="flex flex-col items-center space-y-2">
            <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-primary-500" />
            <span class="text-sm text-gray-600 dark:text-gray-400">{{ $t('global.uploading') }}...</span>
          </div>
        </div>
      </div>

      <!-- Hidden file input -->
      <input
        ref="fileInputRef"
        type="file"
        multiple
        accept="image/*"
        class="hidden"
        @change="handleFileSelect"
      />

      <!-- Error message -->
      <div v-if="errorMessage" class="text-red-500 text-sm">
        {{ errorMessage }}
      </div>
    </div>
  </UFormField>
</template>

<script setup lang="ts">
interface ProductImage {
  id?: string;
  file?: File;
  preview: string;
  uploaded?: boolean;
}

interface Props {
  modelValue: string[]; // Array of Directus file IDs
  maxImages?: number;
  productName?: string;
  existingImages?: ProductImage[]; // Pre-existing images for editing
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  maxImages: 5,
  productName: '',
  existingImages: () => [],
});

const emit = defineEmits<Emits>();

// Reactive state
const images = ref<ProductImage[]>([]);
const isDragOver = ref(false);
const isUploading = ref(false);
const errorMessage = ref('');

// Template refs
const dropzoneRef = ref<HTMLElement>();
const fileInputRef = ref<HTMLInputElement>();

// Composables
const { t } = useI18n();
const toast = useToast();

// Initialize images from existing images prop
const initializeExistingImages = () => {
  if (props.existingImages && props.existingImages.length > 0) {
    images.value = [...props.existingImages];
  }
};

// Watch for changes in existingImages prop
watch(() => props.existingImages, () => {
  initializeExistingImages();
}, { immediate: true });

// File input trigger
const triggerFileInput = () => {
  fileInputRef.value?.click();
};

// Handle file selection from input
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files) {
    handleFiles(Array.from(target.files));
  }
};

// Handle drag and drop events
const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  isDragOver.value = true;
};

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault();
  // Only set to false if leaving the dropzone entirely
  if (!dropzoneRef.value?.contains(event.relatedTarget as Node)) {
    isDragOver.value = false;
  }
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  isDragOver.value = false;
  
  const files = Array.from(event.dataTransfer?.files || []);
  handleFiles(files);
};

// Process selected files
const handleFiles = async (files: File[]) => {
  errorMessage.value = '';
  
  // Validate file count
  const totalImages = images.value.length + files.length;
  if (totalImages > props.maxImages) {
    errorMessage.value = t('global.max_images_exceeded', { max: props.maxImages });
    return;
  }

  // Validate file types
  const validFiles = files.filter(file => file.type.startsWith('image/'));
  if (validFiles.length !== files.length) {
    errorMessage.value = t('global.only_image_files_allowed');
    return;
  }

  // Create preview images
  const newImages: ProductImage[] = [];
  for (const file of validFiles) {
    const preview = URL.createObjectURL(file);
    newImages.push({
      file,
      preview,
      uploaded: false,
    });
  }

  images.value.push(...newImages);
  
  // Upload images
  await uploadImages(newImages);
};

// Upload images to server
const uploadImages = async (imagesToUpload: ProductImage[]) => {
  isUploading.value = true;
  
  try {
    // Convert files to base64
    const base64Images = await Promise.all(
      imagesToUpload.map(async (img) => {
        if (!img.file) return '';
        return new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = (e) => resolve(e.target?.result as string);
          reader.readAsDataURL(img.file!);
        });
      })
    );

    // Upload to server
    const response = await $fetch('/api/upload-product-images', {
      method: 'POST',
      body: {
        type: 'product',
        images: base64Images,
        productName: props.productName,
      },
    });

    if (response.success) {
      // Update images with uploaded IDs
      imagesToUpload.forEach((img, index) => {
        img.id = response.fileIds[index];
        img.uploaded = true;
      });

      // Emit updated file IDs
      const allFileIds = images.value
        .filter(img => img.uploaded && img.id)
        .map(img => img.id!);
      
      emit('update:modelValue', allFileIds);
      
      // toast.add({
      //   title: t('global.success'),
      //   description: t('global.images_uploaded_successfully'),
      //   color: 'success',
      // });
    }
  } catch (error) {
    console.error('Upload failed:', error);
    errorMessage.value = t('global.upload_failed');
    
    // Remove failed uploads
    images.value = images.value.filter(img => !imagesToUpload.includes(img));
    
    toast.add({
      title: t('global.error'),
      description: t('global.upload_failed'),
      color: 'error',
    });
  } finally {
    isUploading.value = false;
  }
};

// Remove image
const removeImage = (index: number) => {
  const removedImage = images.value[index];

  // Revoke object URL to prevent memory leaks
  if (removedImage && removedImage.preview.startsWith('blob:')) {
    URL.revokeObjectURL(removedImage.preview);
  }

  images.value.splice(index, 1);
  
  // Update model value
  const allFileIds = images.value
    .filter(img => img.uploaded && img.id)
    .map(img => img.id!);
  
  emit('update:modelValue', allFileIds);
};

// Move image position
const moveImage = (fromIndex: number, toIndex: number) => {
  const imageToMove = images.value.splice(fromIndex, 1)[0];
  if (imageToMove) {
    images.value.splice(toIndex, 0, imageToMove);
  }
  
  // Update model value to reflect new order
  const allFileIds = images.value
    .filter(img => img.uploaded && img.id)
    .map(img => img.id!);
  
  emit('update:modelValue', allFileIds);
};

// Cleanup on unmount
onUnmounted(() => {
  images.value.forEach(img => {
    if (img.preview.startsWith('blob:')) {
      URL.revokeObjectURL(img.preview);
    }
  });
});
</script>
