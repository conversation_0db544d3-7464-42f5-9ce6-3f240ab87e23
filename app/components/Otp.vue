<template>
  <div
    class="flex w-md max-w-full flex-col items-center rounded-lg border border-[#3F3D47] bg-[#1B1A2199] p-6 backdrop-blur-xl md:p-8"
  >
    <ULink to="/" class="-mt-14 md:-mt-16" >
      <NuxtPicture src="/images/ipgo-cherry.svg" alt="IPGO" class="-mt-14 h-8 w-8 md:-mt-16" loading="eager" />
    </ULink>
    <h2 class="my-4 text-center text-xl font-bold">
      {{ mode === 'signup' ? $t('global.sign_up') : $t('global.recover_password') }}
    </h2>
    <form @submit.prevent="" v-auto-animate class="w-full">
      <!-- Email (First Step) -->
      <label for="email" class="font-bold">{{ $t('global.email') }}</label>
      <UInput
        v-model="email"
        type="email"
        id="email"
        class="mt-2 mb-4 w-full"
        size="xl"
        :placeholder="$t('global.input_email_placeholder')"
        :disabled="codeSent"
        :ui="{
          base: 'dark:!bg-neutral-900 dark:!text-white',
        }"
        @keyup.enter="sendVerificationCode"
      >
        <template #trailing v-if="codeSent">
          <UButton v-if="remaining !== 0" variant="outline" size="xs" class="text-white" :disabled="remaining !== 0">
            <!-- Resend in x seconds -->
            {{ $t('global.resend_in') }} {{ remaining }}s
          </UButton>
          <UButton v-else size="xs" @click="sendVerificationCode">
            {{ $t('global.resend') }}
          </UButton>
        </template>
      </UInput>

      <!-- Verification Code (Second Step) -->
      <template v-if="codeSent">
        <label for="verificationCode" class="font-bold">{{ $t('global.verify_code') }}</label>
        <UInput
          v-model="verificationCode"
          type="text"
          id="verificationCode"
          class="mt-2 mb-4 w-full"
          size="xl"
          @keyup.enter="verifyCode"
        />
      </template>

      <!-- Send Code Button-->
      <UButton
        v-if="!codeSent"
        color="primary"
        size="lg"
        class="mb-4 w-full justify-center rounded-full"
        :disabled="!isEmailValid || isLoading"
        @click.prevent="sendVerificationCode"
      >
        {{ $t('global.send') }}
      </UButton>

      <!-- Continue Button (After Code Sent) -->
      <UButton
        v-if="codeSent"
        color="primary"
        size="lg"
        class="w-full justify-center rounded-full"
        @click.prevent="verifyCode"
      >
        {{ $t('global.continue') }}
      </UButton>

      <p class="mt-4 text-center text-sm text-zinc-400">
        {{ $t('global.already_have_an_account') }}
        <ULink :to="getLoginRoute()" class="text-white">
          {{ $t('global.log_in') }}
        </ULink>
      </p>
    </form>
  </div>
</template>

<script lang="ts" setup>
const localePath = useLocalePath();
const email = useState('email', () => '');
const verificationCode = useState('verificationCode', () => '');
const codeSent = ref(false);
const countdown = shallowRef(60);
const isLoading = ref(false);
const { start, remaining } = useCountdown(countdown, {});
const toast = useToast();
const { t } = useI18n();
const route = useRoute();
const { email: signupEmail, role: signupRole } = useSignup();
const props = defineProps<{
  mode: 'signup' | 'recover';
  role?: 'fan' | 'creator' | 'owner';
}>();

// Set default role to 'fan' if not provided
const currentRole = computed(() => props.role || 'fan');

// Load email and verification code from query params
onMounted(async () => {
  if (route.query.email && route.query.code) {
    email.value = route.query.email as string;
    verificationCode.value = route.query.code as string;
  }
  if (email.value && verificationCode.value) {
    codeSent.value = true;
    start(60);
  }
});

const isEmailValid = computed(() => validateEmail(email.value));

// Get login route based on current role
function getLoginRoute() {
  switch (currentRole.value) {
    case 'creator':
      return localePath('creator-login');
    case 'owner':
      return localePath('owner-login');
    case 'fan':
    default:
      return localePath('login');
  }
}

async function sendVerificationCode() {
  if (!isEmailValid.value) return;
  isLoading.value = true;

  // recover password
  if (props.mode === 'recover') {
    await $fetch('/api/emails/send-verification-code', {
      method: 'POST',
      body: {
        email: email.value,
        mode: 'recover',
      },
    });
    codeSent.value = true;
    start(60);
    return;
  }

  // signup
  try {
    await $fetch(`/api/auth/signup-verification`, {
      method: 'POST',
      body: {
        email: email.value,
        roleName: currentRole.value,
      },
    });
    codeSent.value = true;
    start(60);
    // Store role in signup composable for use in subsequent steps
    signupRole.value = currentRole.value;
  } catch (error: any) {
    if (error.message.includes('already exist')) {
      toast.add({
        title: t('global.error'),
        description: t('global.email_already_registered'),
      });
    }
  } finally {
    isLoading.value = false;
  }
}

async function verifyCode() {
  try {
    await $fetch('/api/auth/verify-code', {
      method: 'POST',
      body: {
        email: email.value,
        verificationCode: verificationCode.value,
      },
    });
    toast.add({
      title: t('global.success'),
    });
    signupEmail.value = email.value;

    // recover password
    if (props.mode === 'recover') {
      navigateTo(localePath('reset-password'));
      return;
    }

    //signup
    if (props.mode === 'signup') {
      // Navigate to role-specific signup page
      switch (currentRole.value) {
        case 'creator': 
          navigateTo(localePath('creator-signup-profile'));
          break;
        case 'owner':
          navigateTo(localePath('owner-signup-profile'));
          break;
        case 'fan':
        default:
          navigateTo(localePath('signup-fan'));
          break;
      }
      return;
    }
    // Default fallback for signup
    navigateTo(localePath('signup-fan'));
  } catch (error) {
    console.error('Failed to verify code', error);
    toast.add({
      title: t('global.error'),
      description: t('global.otp_verification_failed'),
    });
  }
}
</script>
