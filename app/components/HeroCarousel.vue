<template>
  <UCarousel
    loop
    autoplay
    wheel-gestures
    v-slot="{ item }"
    dots
    :items="ips"
    class="mx-auto w-full"
    :ui="{
      dots: 'scale-50 -translate-y-1',
    }"
  >
    <NuxtLinkLocale :to="{ name: mode === 'creator' ? 'creator-ip-id' : 'ip-id', params: { id: item.id } }">
      <div class="relative w-full overflow-hidden">
        <NuxtImg
          @click="refresh"
          :src="directusAssetsUrl(item.main_image as string)"
          class="h-full max-h-96 w-full rounded-md border border-[#3F3D47] object-cover blur-none transition-all duration-100"
          loading="eager"
          placeholder
          placeholder-class="blur-xs"
        />
        <div
          class="md:mx-none md:mb-none absolute bottom-0 left-0 mx-2 mb-2 flex max-w-xs cursor-pointer flex-col items-start gap-1 rounded-md border border-[#3F3D47] bg-[#22202CB2] p-3 backdrop-blur-lg md:bottom-2 md:left-2 md:gap-4 md:p-4 lg:bottom-4 lg:left-4"
        >
          <h3 class="text-sm md:text-md font-bold">
            {{ useTranslatedName(item.translations) }}
          </h3>
          <p class="line-clamp-2 md:line-clamp-3 text-xs">
            {{ useTranslatedDescription(item.translations) }}
          </p>
          <UButton v-if="mode === 'creator'" color="primary" size="sm" class="rounded-full"> {{ $t('global.learn_more') }}</UButton>
          <UButton v-else color="primary" size="sm" class="rounded-full"> {{ $t('global.shop_now') }}</UButton>
        </div>
      </div>
    </NuxtLinkLocale>
  </UCarousel>
</template>

<script lang="ts" setup>
withDefaults(
  defineProps<{
    mode?: 'creator' | 'ip' | 'fan';
  }>(),
  {
    mode: 'fan',
  },
);
const { locale } = useI18n();
const { useDirectusFetch, directusAssetsUrl } = useDirectus();
const { data: ips, refresh } = useDirectusFetch<Ip[]>('/items/ip', {
  params: {
    fields: ['id', 'translations.*, main_image', 'category.translations.*'],
    sort: ['-date_created'],
    limit: 5,
  },
});
</script>
