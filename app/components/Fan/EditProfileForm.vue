<template>
  <!-- Avatar Upload -->
  <UFormField name="avatar" class="mb-6 flex w-full flex-col px-1">
    <div class="mx-auto flex flex-row items-center space-x-4 sm:mx-0">
      <UAvatar
        :src="avatarPreview"
        :alt="state.firstName ? `${state.firstName} ${state.lastName}` : ''"
        size="xl"
        class="h-20 w-20 sm:h-24 sm:w-24"
      />

      <UButton
        size="xl"
        color="secondary"
        class="rounded-full px-4 text-sm whitespace-nowrap"
        @click="triggerFileInput"
        leading-icon="heroicons:pencil-square"
      >
        {{ $t('global.change_avatar') }}
      </UButton>
    </div>

    <input ref="fileInput" type="file" accept="image/jpeg,image/jpg,image/png" class="hidden" @change="handleFileChange" />
  </UFormField>

  <UForm class="grid w-full gap-x-6 gap-y-4 lg:grid-cols-2" :schema="editSchema" :state="state" @submit="onSubmit">
    <UFormField name="nickname" :label="$t('global.nickname')">
      <UInput v-model="state.nickname" type="text" id="nickname" size="lg" class="w-full" />
    </UFormField>

    <UFormField name="email" :label="$t('global.email')">
      <UInput disabled :value="state.email" type="text" id="email" size="lg" class="w-full" />
    </UFormField>

    <!-- First Name | Last Name -->
    <UFormField name="firstName" :label="$t('global.first_name')">
      <UInput v-model="state.firstName" type="text" id="firstName" size="lg" class="w-full" />
    </UFormField>
    <UFormField name="lastName" :label="$t('global.last_name')">
      <UInput v-model="state.lastName" type="text" id="lastName" class="w-full" size="lg" />
    </UFormField>

    <!-- Birthday -->
    <UFormField name="birthday" :label="$t('global.birthday')">
      <UInput v-model="state.birthday" type="date" id="birthday" class="w-full" size="lg" />
    </UFormField>

    <!-- Default Address Display -->
    <UFormField :label="$t('global.default_address')" class="lg:col-span-2">
      <div class="flex items-center justify-between rounded-lg border border-neutral-700 bg-neutral-800 p-4">
        <div v-if="defaultAddress" class="flex-1">
          <p class="font-medium">{{ defaultAddress.name }}</p>
          <p class="text-sm text-neutral-400">
            {{ defaultAddress.address_line_1 }}
            <span v-if="defaultAddress.address_line_2">, {{ defaultAddress.address_line_2 }}</span>
          </p>
          <p class="text-sm text-neutral-400">
            {{ defaultAddress.city }}<span v-if="defaultAddress.province">, {{ defaultAddress.province }}</span> {{ defaultAddress.postcode }}
          </p>
          <p class="text-sm text-neutral-400">{{ defaultAddress.country }}</p>
        </div>
        <div v-else class="flex-1">
          <p class="text-neutral-400">{{ $t('global.no_default_address') }}</p>
        </div>
        <UButton
          variant="outline"
          size="sm"
          @click="openAddressModal"
          class="ml-4"
        >
          {{ $t('global.manage_addresses') }}
        </UButton>
      </div>
    </UFormField>

    <UButton
      color="primary"
      type="submit"
      size="lg"
      class="w-full justify-center rounded-full text-sm sm:text-base lg:col-span-2"
      :loading="isLoading"
    >
      {{ $t('global.save_changes') }}
    </UButton>
  </UForm>

  <!-- Address Management Modal -->
  <UModal v-model:open="isAddressModalOpen" :title="$t('global.manage_addresses')">
    <template #body>
      <FanAddressList @selected="handleAddressModalClose" />
    </template>
  </UModal>
</template>

<script lang="ts" setup>
import * as z from 'zod/v4';
import type { FormSubmitEvent } from '@nuxt/ui';

// Define emits
const emit = defineEmits(['updated']);

const { t } = useI18n();
const { directusAssetsUrl, useDirectusFetch } = useDirectus();
const localePath = useLocalePath();
const { profile } = useUser();

// Edit schema (no password, terms, or address fields required - addresses handled separately)
const editSchema = z.object({
  avatar: z.string(),
  email: z.string(),
  firstName: z.string().min(3, t('global.missing_first_name')),
  lastName: z.string().min(3, t('global.missing_last_name')),
  nickname: z.string().min(3, t('global.missing_nickname')),
  birthday: z.string().date(t('global.missing_birthdate')),
});

type EditFormSchema = z.infer<typeof editSchema>;

const toast = useToast();
const fileInput = ref<HTMLInputElement | null>(null);

const state = ref<Partial<EditFormSchema>>({
  avatar: '',
  email: '',
  firstName: '',
  lastName: '',
  nickname: '',
  birthday: '',
});

// UI state
const isLoading = ref(false);
const avatarPreview = ref<string | undefined>(undefined);
const avatarFile = ref<File | null>(null);

// Address state
const defaultAddress = ref<Addresses | null>(null);
const isAddressModalOpen = ref(false);

// Avatar upload functions
function triggerFileInput() {
  if (fileInput.value) {
    fileInput.value.click();
  }
}

function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    const file = target.files[0];

    // Validate file type - only allow JPEG and PNG
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type?.toLowerCase() || '')) {
      toast.add({
        title: t('global.error'),
        description: t('global.avatar_invalid_format') || 'Please upload only JPEG or PNG images',
        color: 'error',
      });
      // Clear the input
      target.value = '';
      return;
    }

    avatarFile.value = file || null;

    // Create a preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target && typeof e.target.result === 'string') {
        avatarPreview.value = e.target.result;
        // Keep existing avatar ID until we upload the new one
      }
    };
    reader.readAsDataURL(file as File);
  }
}

// Load user profile data on mount
onMounted(async () => {
  await loadUserProfile();
});

// Load user profile data
async function loadUserProfile() {
  isLoading.value = true;
  try {
    const data = await $fetch('/api/me');

    state.value.email = data.email ?? '';
    state.value.firstName = data.first_name ?? '';
    state.value.lastName = data.last_name ?? '';
    state.value.nickname = data.nickname ?? '';
    state.value.birthday = data.birthday ?? '';

    // Handle existing avatar (Directus file ID)
    if (data.avatar) {
      const avatarId = typeof data.avatar === 'string' ? data.avatar : data.avatar.id;
      if (avatarId) {
        const avatarUrl = directusAssetsUrl(avatarId);
        if (avatarUrl) {
          avatarPreview.value = avatarUrl;
          state.value.avatar = avatarId; // Store the file ID, not the URL
        }
      }
    }

    // Load default address from address book
    await loadDefaultAddress(data.default_address);
  } catch (error) {
    toast.add({
      title: t('global.error'),
      description: t('global.unexpected_error'),
    });
    console.error('Failed to load profile:', error);
  } finally {
    isLoading.value = false;
  }
}

// Load default address from address book
async function loadDefaultAddress(defaultAddressId?: number | Addresses | null) {
  if (!defaultAddressId) {
    defaultAddress.value = null;
    return;
  }

  try {
    const addressId = typeof defaultAddressId === 'number' ? defaultAddressId : defaultAddressId.id;
    const { data } = await useDirectusFetch<Addresses>(`/items/addresses/${addressId}`);
    defaultAddress.value = data.value || null;
  } catch (error) {
    console.error('Failed to load default address:', error);
    defaultAddress.value = null;
  }
}

// Open address management modal
function openAddressModal() {
  isAddressModalOpen.value = true;
}

// Handle address modal close and refresh default address
async function handleAddressModalClose() {
  isAddressModalOpen.value = false;
  // Refresh profile data to get updated default address
  const data = await $fetch('/api/me');
  await loadDefaultAddress(data.default_address);
}

// Form submission
async function onSubmit(event: FormSubmitEvent<EditFormSchema>) {
  await updateProfile();
}

// Update profile function
async function updateProfile() {
  isLoading.value = true;

  try {
    let avatarId = state.value.avatar; // Keep existing avatar ID by default

    // If user selected a new avatar file, upload it first
    if (avatarFile.value && avatarPreview.value?.startsWith('data:')) {
      const uploadResponse = await $fetch<{ fileId: string; success: boolean }>('/api/upload-avatar', {
        method: 'POST',
        body: {
          avatar: avatarPreview.value, // base64 data
          filename: `${state.value.email}-avatar-updated`,
        },
      });
      avatarId = uploadResponse.fileId;
    }

    // Update user profile with all data
    await $fetch('/api/update-profile', {
      method: 'PATCH',
      body: {
        first_name: state.value.firstName,
        last_name: state.value.lastName,
        nickname: state.value.nickname,
        birthday: state.value.birthday,
        avatar: avatarId, // Use either existing ID or new uploaded ID
        // Address fields would be handled separately if needed
      },
    });

    // Show success notification
    toast.add({
      color: 'neutral',
      title: t('global.success'),
      description: t('global.profile_updated'),
    });

    // Emit updated event to close modal
    emit('updated');

    // refetch user data
    const data = await $fetch('/api/me');
    profile.value = data;
    

    navigateTo(localePath('fan-cart'), { external: true });
  } catch (error) {
    // Show error notification
    console.error('Failed to update profile', error);
    toast.add({
      title: t('global.error'),
      description: t('global.form_validation_error'),
    });
  } finally {
    isLoading.value = false;
  }
}
</script>
