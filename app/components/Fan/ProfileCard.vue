<template>
  <!-- Wide Profile Card -->
  <div class="flex gap-4 rounded-xl border border-neutral-700 bg-neutral-900 p-4">
    <!-- Avatar -->
    <div v-if="profile?.avatar">
      <UAvatar class="h-28 w-28" :src="directusAssetsUrl(profile?.avatar as string) ?? ''" />
    </div>
    <div v-else>
      <UIInitialAvatar :name shape="circle" class="h-28 w-28" />
    </div>
    <!-- Creator Details -->
    <div class="flex flex-grow flex-col justify-center gap-2">
      <h2 class="text-2xl font-bold">{{ name || 'Loading...' }}</h2>
      <!-- Buttons, edit profile, payment method , shipping address -->
      <div id="profile-buttons" class="md: grid w-full grid-cols-2 gap-4 sm:grid-cols-4 md:flex-row lg:grid-cols-6">
        <UModal v-model:open="isProfileEditModalOpen" :title="$t('global.edit_profile')" :close="true"
          ><UButton class="justify-center rounded-full" variant="soft" color="neutral">{{
            $t('global.edit_profile')
          }}</UButton>
          <template #body>
            <FanProfileDetails mode="edit" @updated="isProfileEditModalOpen = false"/>
          </template>
        </UModal>
        <UModal v-model:open="isPaymentMethodModalOpen" :title="$t('global.payment_method')" :close="true">
          <UButton class="justify-center rounded-full" variant="soft" color="neutral">{{
            $t('global.payment_method')
          }}</UButton>
          <template #body>
            <FanPaymentMethodList @selected="handleSelectedPaymentMethod" />
          </template>
        </UModal>
        <UModal v-model:open="isShippingAddressModalOpen" :title="$t('global.shipping_address')" :close="true">
          <UButton class="justify-center rounded-full" variant="soft" color="neutral">{{
            $t('global.shipping_address')
          }}</UButton>
          <template #body>
            <FanAddressList @selected="handleSelectedAddress" />
          </template>
        </UModal>
        <UButton
          class="justify-center rounded-full px-4 sm:ml-auto lg:col-span-3"
          variant="outline"
          color="primary"
          @click="handleLogout"
        >
          {{ $t('global.log_out') }}
        </UButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const { directusAssetsUrl } = useDirectus();
const localePath = useLocalePath();
const { logout } = useUser();
const props = defineProps<{
  profile: DirectusUsers;
}>();
const isProfileEditModalOpen = ref(false);
const isPaymentMethodModalOpen = ref(false);
const isShippingAddressModalOpen = ref(false);
const name = computed(() => {
  return [props.profile?.first_name, props.profile?.last_name].filter(Boolean).join(' ') || '';
});

const handleSelectedAddress = (address: Addresses) => {
  isShippingAddressModalOpen.value = false;
};

// Handle selected payment method
const handleSelectedPaymentMethod = (paymentMethod: any) => {
  isPaymentMethodModalOpen.value = false;
};

const handleLogout = async () => {
  await logout();
  navigateTo(localePath('index'));
};
</script>
