<script setup lang="ts">
import type { NavigationMenuItem } from '@nuxt/ui';

// Props
const props = withDefaults(
  defineProps<{
    translucent?: boolean;
  }>(),
  {
    translucent: false,
  },
);

const route = useRoute();
const { t } = useI18n();
const localePath = useLocalePath();
const { accessToken, profile, checkAndRedirectRoles } = useUser();
const { directusAssetsUrl } = useDirectus();
const { cartItemCount } = useCart();

if (accessToken.value) {
  const { data: profileData } = await useFetch('/api/me', {
    key: 'profile',
  });
  profile.value = profileData.value || null;
  checkAndRedirectRoles();
}

const items = computed<NavigationMenuItem[]>(() => [
  {
    label: t('global.store'),
    to: localePath('index'),
  },
  {
    label: t('global.about_ipgo'),
    to: 'https://www.figma.com/community/file/1288455405058138934',
    target: '_blank',
  },
]);

const avatarUrl = computed(() => {
  if (!profile.value?.avatar) return undefined;
  return directusAssetsUrl(profile.value?.avatar as string) ?? undefined;
});

// UI configuration - conditionally apply translucent background
const headerUI = computed(() => ({
  left: '!flex-none',
  right: 'flex items-center justify-end flex-1 gap-3 lg:gap-1.5',
  ...(props.translucent && {
    root: 'backdrop-blur-none !bg-gradient-to-b from-[#1b1a21]/60 via-[#1b1a21]/20 to-transparent',
  }),
}));
</script>

<template>
  <UHeader :ui="headerUI" :to="localePath('index')">
    <template #title>
      <LogoWithText class="w-auto" />
    </template>
    <!-- <UNavigationMenu :items="items" class="justify-start" variant="link" color="neutral" /> -->
    <div class="ml-4 flex flex-row gap-4 text-sm">
      <ULink :to="localePath('index')" :class="{ 'text-white hover:text-neutral-700': props.translucent }">
        {{ $t('global.store') }}
      </ULink>
      <ULink :to="localePath('about')" :class="{ 'text-white hover:text-neutral-700': props.translucent }">
        {{ $t('global.about_ipgo') }}
      </ULink>
    </div>
    <template #right>
      <UTooltip text="Cart">
        <UChip :show="cartItemCount > 0" position="bottom-right" inset :text="cartItemCount" size="2xl">
          <UButton
            color="neutral"
            variant="ghost"
            :to="localePath({ name: 'fan', query: { tab: 'cart' } })"
            icon="i-heroicons-shopping-cart"
            aria-label="cart"
          />
        </UChip>
      </UTooltip>

      <LangSwitcher />

      <UTooltip text="Profile">
        <ULink
          :to="profile ? localePath('fan-cart') : localePath('login')"
          class="flex items-center gap-2 text-sm text-white"
        >
          <ClientOnly>
            <UAvatar id="no-avatar" v-if="!profile?.avatar" src="/images/missing-product.png" />
            <UAvatar id="avatar" v-else="profile.avatar && refreshToken" :src="avatarUrl" aria-label="profile" />
            <template #fallback>
              <UAvatar id="no-avatar" src="/images/missing-product.png" />
            </template>
          </ClientOnly>
          <p class="hidden lg:block" v-if="profile">{{ t('global.account') }}</p>
          <p class="hidden lg:block" v-else>{{ t('global.log_in') }}</p>
        </ULink>
      </UTooltip>
    </template>

    <template #body>
      <UNavigationMenu :items="items" orientation="vertical" class="-mx-2.5" />
    </template>
  </UHeader>
</template>
