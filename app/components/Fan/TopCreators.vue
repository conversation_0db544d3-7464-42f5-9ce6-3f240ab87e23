<template>
  <div class="no-scrollbar flex gap-4 overflow-x-auto">
    <FanCreatorCard :creator="creator" v-for="creator in creators" class="h-52 w-36 flex-shrink-0" />
  </div>
</template>

<script lang="ts" setup>
const { useDirectusFetch } = useDirectus();
const { data: creators } = useDirectusFetch<Creator[]>('/items/creator?filter[count(products)][_neq]=0', {
  key: 'top-creators',
  params: {
    fields: ['id', 'nickname', 'introduction', 'products', 'user.avatar'],
    limit: 10,
  },
});
</script>
