<template>
  <UModal v-model:open="isOpen" :title="$t('global.delete_payment_method')">
    <slot />
    <template #body>
      <div class="p-4">
        <p class="-mt-6">{{ $t('global.are_you_sure_to_delete_this_payment_method') }}</p>
        <p class="mb-4">{{ $t('global.this_action_cannot_be_undone') }}</p>

        <div class="grid grid-cols-2 gap-4">
          <UButton
            color="secondary"
            variant="soft" size="lg"
            class="justify-center rounded-full"
            @click="isOpen = false"
          >
            {{ $t('global.cancel') }}
          </UButton>
          <UButton
            color="warning"
            size="lg"
            class="justify-center rounded-full"
            @click="confirmDelete"
            :loading="isDeleting"
          >
            {{ $t('global.delete') }}
          </UButton>
        </div>
      </div>
    </template>
  </UModal>
</template> 

<script lang="ts" setup>
interface Props {
  paymentMethodId: string;
}

const props = defineProps<Props>();
const isOpen = ref(false);
const emit = defineEmits(['deleted']);
const isDeleting = ref(false);

const confirmDelete = async () => {
  if (!props.paymentMethodId) return;

  isDeleting.value = true;
  try {
    // Call API to delete payment method
    await $fetch(`/api/payment/method/${props.paymentMethodId}`, {
      method: 'DELETE'
    });

  } catch (error) {
    console.error('Error deleting payment method:', error);
  } finally {
    isDeleting.value = false;
    isOpen.value = false;
    emit('deleted');
  }
};
</script>
