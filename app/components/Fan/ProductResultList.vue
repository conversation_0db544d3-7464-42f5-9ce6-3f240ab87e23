<template>
  <!--  should tile like a grid but fully responsive -->
  <!-- <div :class="`flex flex-wrap gap-2 ${status === 'pending' ? 'opacity-50' : ''}`" v-auto-animate> -->
  <div :class="`grid grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-2`" v-auto-animate ref="el">
    <LazyVerticalProductCard
      v-if="products && products?.length > 0"
      v-for="product in products"
      :key="product.id"
      :product="product"
      class="w-full max-h-96"
    />
    <div v-else class="mt-8 w-full col-span-2 md:col-span-3 xl:col-span-5">
      <p class="text-center text-xl font-bold">{{ $t('global.no_products_available') }}</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getPriceFieldByLocale } from '~~/shared/utils/consts';

const { filter } = useFanFilter();
const { useDirectusFetch } = useDirectus();
const { locale } = useI18n();

const filterParams = computed(() => {
  const filters: any[] = [];

  // Product name search (search in translations)
  if (filter.value.productName) {
    filters.push({
      translations: {
        name: {
          _icontains: filter.value.productName,
        },
      },
    });
  }

  // Search value (additional search functionality)
  if (filter.value.searchValue) {
    filters.push({
      _or: [
        {
          translations: {
            name: {
              _icontains: filter.value.searchValue,
            },
          },
        },
        {
          translations: {
            description: {
              _icontains: filter.value.searchValue,
            },
          },
        },
      ],
    });
  }

  // Product status filter
  // switch (filter.value.status) {
  //   case 'new':
  //     // date_created within past month
  //     filters.push({
  //       date_created: {
  //         _gte: new Date(new Date().setMonth(new Date().getDay() - 7)).toISOString(),
  //       },
  //     });
  //     break;
  //   default:
  //     break;
  // }

  // Availability filter (maps to stock availability)
  if (filter.value.availability) {
    if (filter.value.availability === 'instock') {
      filters.push({
        stock_remaining: {
          _gt: 0,
        },
      });
    } else if (filter.value.availability === 'outofstock') {
      filters.push({
        stock_remaining: {
          _lte: 0,
        },
      });
    }
  }

  // Product category filter (category is an ID that references ProductCategories)
  if (filter.value.productCategory) {
    filters.push({
      category: {
        code: {
          _eq: filter.value.productCategory,
        },
      },
    });
  }

  // Price range filters - use locale-specific price field
  if (filter.value.minPrice && filter.value.minPrice !== '') {
    const minPrice = parseFloat(filter.value.minPrice);
    if (!isNaN(minPrice)) {
      const priceField = getPriceFieldByLocale(locale.value);
      filters.push({
        [priceField]: {
          _gte: minPrice,
        },
      });
    }
  }

  if (filter.value.maxPrice && filter.value.maxPrice !== '') {
    const maxPrice = parseFloat(filter.value.maxPrice);
    if (!isNaN(maxPrice)) {
      const priceField = getPriceFieldByLocale(locale.value);
      filters.push({
        [priceField]: {
          _lte: maxPrice,
        },
      });
    }
  }

  // IP category filter
  if (filter.value.ipCategory) {
    filters.push({
      ip: {
        category: {
          code: {
            _eq: filter.value.ipCategory,
          },
        },
      },
    });
  }

  // Creator filter
  if (filter.value.creator?.value) {
    filters.push({
      creator: {
        _eq: filter.value.creator.value,
      },
    });
  }

  return {
    fields: [
      '*',
      'translations.*',
      'category.translations.*',
      'ip.translations.*',
      'ip.category.code',
      'ip.category.translations.*',
      'creator.*',
    ],
    limit: -1,
    filter: filters.length > 0 ? { _and: filters } : {},
    sort:
      filter.value.sortBy === 'latest'
        ? ['-date_created']
        : filter.value.sortBy === 'oldest'
          ? ['date_created']
          : filter.value.sortBy === 'price_low'
            ? ['price']
            : filter.value.sortBy === 'price_high'
              ? ['-price']
              : filter.value.sortBy === 'best_seller'
                ? ['-units_sold']
                : ['-date_created'],
  };
});

const { data: products } = await useDirectusFetch<Products[]>('/items/products', {
  watch: [filter],
  params: filterParams,
  key: 'filtered-products',
});
</script>
