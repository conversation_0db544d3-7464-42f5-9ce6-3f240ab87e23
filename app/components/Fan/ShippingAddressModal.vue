<template>
  <UModal v-model:open="isOpen">
    <slot/>
    <template #body>
      <div class="grid grid-cols-2 gap-4">
        <p class="-mt-4 text-sm text-muted col-span-2">{{ $t('global.all_fields_are_required') }}</p>
        <UForm :schema="schema" :state="state" @submit="onSubmit" class="grid grid-cols-2 gap-4 col-span-2">
          <!-- Recipient Name -->
          <UFormField name="name" :label="$t('global.recipient_name')">
            <UInput v-model="state.name" type="text" id="name" class="w-full" size="lg" />
          </UFormField>

          <!-- Phone Number -->
          <UFormField name="phone_number" :label="$t('global.phone_number')">
            <UInput v-model="state.phone_number" type="text" id="phone_number" class="w-full" size="lg" />
          </UFormField>

          <!-- Country -->
          <UFormField name="country" :label="$t('global.country')">
            <USelect v-model="state.country" :items="COUNTRIES[locale]" id="country" class="w-full" size="lg" />
          </UFormField>

          <!-- Empty space to maintain grid layout -->
          <div></div>

          <!-- Postcode -->
          <UFormField name="postcode" :label="$t('global.postal_code')" class="col-span-2">
            <div class="grid grid-cols-2 gap-4">
              <UInput v-model="state.postcode" type="text" id="postcode" size="lg" class="w-full" />
              
              <!-- Auto-fill button for Japanese addresses -->
              <UButton
                v-if="state.country === 'JP'"
                @click="lookupJapaneseAddress"
                :loading="isLookingUp"
                icon="i-heroicons-magnifying-glass"
                variant="soft"
                color="neutral"
                class="w-max rounded-xl"
                :disabled="!state.postcode || state.postcode.length < 7"
              >{{  $t('global.get_address') }}</UButton>
            </div>
          </UFormField>

          <!-- Address Line 1 -->
          <UFormField name="address_line_1" :label="$t('global.address_line_1')" class="col-span-2">
            <UInput v-model="state.address_line_1" type="text" id="address_line_1" class="w-full" size="lg" />
          </UFormField>

          <!-- Address Line 2 -->
          <UFormField name="address_line_2" class="col-span-2">
            <template #label>{{ `${$t('global.address_line_2')} (${$t('global.optional')})` }}</template>
            <UInput v-model="state.address_line_2" type="text" id="address_line_2" class="w-full" size="lg" />
          </UFormField>

          <!-- City -->
          <UFormField name="city" :label="$t('global.city')">
            <UInput v-model="state.city" type="text" id="city" class="w-full" size="lg" />
          </UFormField>

          <!-- Province (optional) -->
          <UFormField name="province">
            <template #label>{{ `${$t('global.state_slash_province')} (${$t('global.optional')})` }}</template>
            <UInput v-model="state.province" type="text" id="province" class="w-full" size="lg" />
          </UFormField>

          <!-- Set as Default Checkbox -->
          <UFormField name="is_default" class="col-span-2">
            <div class="flex flex-row gap-2">
              <UCheckbox v-model="state.is_default" :disabled="isCurrentlyDefault" />
              <span :class="{ 'text-muted': isCurrentlyDefault }">
                {{ $t('global.set_as_default_address') }}
                <span v-if="isCurrentlyDefault" class="text-xs text-muted ml-1">
                  ({{ $t('global.currently_default') }})
                </span>
              </span>
            </div>
          </UFormField>

          <!-- Fill Mock Data -->
          <DevOnly>
            <UButton
              color="success"
              @click="fillUpMock"
              size="lg"
              class="w-full justify-center rounded-full text-sm sm:text-base col-span-2"
            >
              {{ $t('dev_only.mock_fill_up') }}
            </UButton>
          </DevOnly>

          <!-- Submit Button -->
          <UButton
            color="primary"
            type="submit"
            size="lg"
            class="w-full justify-center rounded-full text-sm sm:text-base col-span-2"
            :loading="isLoading"
          >
            {{ mode === 'add' ? $t('global.add_address') : $t('global.save_changes') }}
          </UButton>
        </UForm>
      </div>
    </template>
  </UModal>
</template>

<script lang="ts" setup>
import z from 'zod/v4';
import type { FormSubmitEvent } from '@nuxt/ui';

// This shipping address form needs to have two modes. One is add and the other is edit.
const props = defineProps<{
  mode: 'add' | 'edit';
  address?: Addresses;
}>();
const emit = defineEmits(['submit']);

const { t, locale } = useI18n();
const { useDirectusFetch } = useDirectus();
const { profile } = useUser();
const isOpen = ref(false);

// Get profile
const { data: profileData } = await useFetch('/api/me');
profile.value = profileData.value || null;

// Schema with is_default field added
const schema = z.object({
  name: z.string().min(1, t('global.missing_name')),
  address_line_1: z.string().min(1, t('global.missing_address')),
  address_line_2: z.string().optional(),
  city: z.string().min(1, t('global.missing_city')),
  country: z.string().min(1, t('global.missing_country')),
  postcode: z.string().min(1, t('global.missing_postal_code')),
  phone_number: z.string().min(1, t('global.missing_phone')),
  province: z.string().optional(),
  is_default: z.boolean().optional(),
});

type ShippingAddressFormSchema = z.infer<typeof schema>;

// Check if this address is currently the default
const isCurrentlyDefault = computed(() => {
  return props.mode === 'edit' && props.address?.id === profile.value?.default_address;
});

// State with proper typing and is_default field
const state = reactive<ShippingAddressFormSchema>({
  name: props.address?.name || '',
  address_line_1: props.address?.address_line_1 || '',
  address_line_2: props.address?.address_line_2 || '',
  city: props.address?.city || '',
  country: props.address?.country || '',
  postcode: props.address?.postcode || '',
  phone_number: props.address?.phone_number || '',
  province: props.address?.province || '',
  is_default: props.mode === 'add' ? true : isCurrentlyDefault.value
});

const isLoading = ref(false);
const isLookingUp = ref(false);

const fillUpMock = () => {
  state.name = 'John Doe';
  state.address_line_1 = '123 Main St';
  state.address_line_2 = 'Apt 4B';
  state.city = 'Anytown';
  state.country = 'JP';
  state.postcode = '1231234';
  state.phone_number = '1234567890';
  state.province = 'Tokyo';
  state.is_default = true;
};

// Type for Japanese postcode API response
interface JapaneseAddressResult {
  address1: string;
  address2: string;
  address3: string;
  kana1: string;
  kana2: string;
  kana3: string;
  prefcode: string;
  zipcode: string;
}

interface JapaneseAddressResponse {
  message: string | null;
  results: JapaneseAddressResult[] | null;
  status: number;
}

// Japanese postcode lookup function
const lookupJapaneseAddress = async () => {
  if (!state.postcode || state.country !== 'JP') return;

  isLookingUp.value = true;
  const toast = useToast();

  try {
    // Remove any hyphens from postcode for API call
    const cleanPostcode = state.postcode.replace(/-/g, '');

    const response = await $fetch<JapaneseAddressResponse>(`/api/postcode/jp/${cleanPostcode}`);

    if (response.status === 200 && response.results && response.results.length > 0) {
      const result = response.results[0];

      if (result) {
        // Auto-fill address fields with Japanese address data
        state.address_line_1 = `${result.address1}${result.address2}${result.address3}`;
        state.city = result.address2;
        state.province = result.address1;

        // Show success message
        toast.add({
          title: t('global.address_lookup_success'),
          icon: 'i-heroicons-check-circle',
          color: 'success'
        });
      }
    } else {
      // Show error message if no results found
      toast.add({
        title: t('global.address_lookup_error'),
        icon: 'i-heroicons-exclamation-triangle',
        color: 'error'
      });
    }
  } catch (error) {
    console.error('Error looking up Japanese address:', error);
    // Show error message for network/API errors
    toast.add({
      title: t('global.address_lookup_error'),
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error'
    });
  } finally {
    isLookingUp.value = false;
  }
};

const onSubmit = async (event: FormSubmitEvent<ShippingAddressFormSchema>) => {
  let newAddressId: number | undefined;

  try {
    console.log('Submitting address:', event.data);

    // Exclude is_default from the request body as it's only for client-side logic
    const { is_default, ...addressData } = event.data;

    if (props.mode === 'add') {
      // Create new address
      const { data: newAddress } = await useDirectusFetch<Addresses>('/items/addresses', {
        method: 'POST',
        server: false,
        body: {
          ...addressData,
          user: profile.value?.id,
        },
      });
      newAddressId = newAddress.value?.id;

    } else {
      // Update existing address
      await useDirectusFetch(`/items/addresses/${props.address?.id}`, {
        method: 'PATCH',
        server: false,
        body: {
          ...addressData,
        },
      });
    }

    // Update user profile default_address if is_default is true
    if (is_default) {
      const addressIdToSet = props.mode === 'add' ? newAddressId : props.address?.id;
      await useDirectusFetch(`/users/${profile.value?.id}`, {
        method: 'PATCH',
        server: false,
        body: {
          default_address: addressIdToSet
        },
      });
    }

    // Emit the returned address ID
    const emitAddressId = props.mode === 'add' ? newAddressId : props.address?.id;
    emit('submit', emitAddressId);
    
  } catch (error) {
    console.error('Error submitting address:', error);
  } finally {
    isLoading.value = false;
    isOpen.value = false;
  }
};
</script>