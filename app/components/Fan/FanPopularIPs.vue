<template>
  <div class="flex w-full flex-col gap-4">
    <div class="no-scrollbar flex gap-2 overflow-x-auto" v-auto-animate>
      <IpCategoriesFilter v-model="selectedIpCategory" />
    </div>
    <div class="no-scrollbar flex h-56 flex-shrink-0 gap-4 overflow-x-auto" v-auto-animate>
      <!-- IP Item Cards -->
      <IPCard
        mode="fan"
        v-if="ips && ips?.length > 0"
        :ip="ip"
        v-for="ip in ips"
        :key="ip.id"
        :locale="locale"
        class="max-w-80 min-w-xs"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const { locale } = useI18n();
const { useDirectusFetch } = useDirectus();

const selectedIpCategory = useState<string | undefined>('ipFilter', () => undefined);

const filterParams = computed(() => ({
  fields: ['id, translations.*, images.directus_files_id, category.translations.*', 'main_image'],
  limit: 15,
  filter: selectedIpCategory.value
    ? [
        {
          category: {
            code: {
              _eq: selectedIpCategory.value,
            },
          },
        },
      ]
    : [],
}));
const config = useRuntimeConfig();

const { data: ips } = useDirectusFetch<Ip[]>('/items/ip', {
  watch: [selectedIpCategory],
  params: filterParams,
  key: 'popular-ips',
});

// const { data: allIps, pending: pendingIps } = useFetch('', {});
// const { data: ipCategories } = useFetch('/api/ips/category-list');
</script>
