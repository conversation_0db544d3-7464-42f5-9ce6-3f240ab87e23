<template>
  <NuxtLinkLocale :to="{ name: 'creators-id', params: { id: creator.id } }" :class="$attrs.class">
    <div
      class="flex h-full cursor-pointer flex-col items-center justify-center gap-2 overflow-hidden rounded-lg border border-neutral-800 p-4 text-center transition-all hover:brightness-110"
    >
      <!-- Show avatar image if available, otherwise show initials -->
      <NuxtImg
        v-if="avatarUrl"
        :src="avatarUrl"
        alt="creator-avatar"
        class="aspect-square h-20 w-20 rounded-full object-cover blur-none transition-all duration-100"
        loading="lazy"
        placeholder
        placeholder-class="blur-xs"
      />
      <UIInitialAvatar v-else :name="creator.nickname || ''" shape="circle" class="h-20 w-20" />
      <h4 class="line-clamp-2 text-sm leading-tight font-semibold">{{ creator.nickname }}</h4>
      <p class="line-clamp-2 h-8 text-xs text-neutral-500">{{ creator.introduction }}</p>
    </div>
  </NuxtLinkLocale>
</template>

<script lang="ts" setup>
// Disable automatic attribute inheritance since we manually handle class
defineOptions({
  inheritAttrs: false,
});

const { creator } = defineProps<{ creator: Creator }>();
const { directusAssetsUrl } = useDirectus();

// Get avatar URL if it exists
const avatarUrl = computed(() => {
  const avatar = creator.avatar;
  if (!avatar) return null;

  const avatarId = typeof avatar === 'string' ? avatar : avatar.id;
  return avatarId ? directusAssetsUrl(avatarId) : null;
});
</script>
