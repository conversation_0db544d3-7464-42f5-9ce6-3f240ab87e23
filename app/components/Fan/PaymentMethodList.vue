<template>
  <div class="grid gap-4 -mt-3 md:-mt-6">
    <!-- scrollable list -->
    <div class="overflow-y-scroll max-h-[60vh] flex flex-col gap-2">
      <div
        v-for="paymentMethod in paymentMethods"
        :key="paymentMethod.id"
        @click="setSelectedPaymentMethod(paymentMethod)"
        :class="[
          'flex gap-4 p-4 rounded-xl border cursor-pointer bg-[#22202C] hover:border-primary hover:bg-primary/10',
          selectedPaymentMethod?.id === paymentMethod.id ? 'border-primary bg-primary/10' : 'border-muted'
        ]">
        <!-- Card brand logo -->
        <div class="flex items-center justify-center">
          <img
            :src="getCardBrandLogo(paymentMethod.card?.brand)"
            :alt="getCardBrandDisplay(paymentMethod.card?.brand)"
            class="w-8 h-8"
          />
        </div>

        <div class="flex flex-col justify-center gap-0.5 grow" v-auto-animate>
          <!-- Card brand and last 4 digits -->
          <p class="text-sm font-bold">
            {{ getCardBrandDisplay(paymentMethod.card?.brand) }} •••• {{ paymentMethod.card?.last4 }}
          </p>
          <!-- Expiry date and cardholder name -->
          <p class="text-xs text-muted">
            {{ paymentMethod.billing_details?.name }} {{ paymentMethod.card?.exp_month }}/{{ paymentMethod.card?.exp_year }}
          </p>
        </div>
        
        <!-- default badge and action buttons -->
        <div class="flex items-center justify-end gap-2" v-auto-animate>
          <!-- default badge on the left -->
          <UBadge v-if="isDefaultPaymentMethod(paymentMethod)" variant="soft" color="secondary" class="text-white capitalize">
            {{ $t('global.default') }}
          </UBadge>

          <!-- action buttons (delete, edit) on the right -->
          <!-- edit payment method modal -->
          <FanPaymentMethodModal mode="edit" :payment-method="paymentMethod" :key="paymentMethod.id" :title="$t('global.edit_payment_method')"
          @submit="handlePaymentMethodSubmit">
            <UButton icon="i-heroicons-pencil" variant="ghost" color="neutral" size="sm"></UButton>
          </FanPaymentMethodModal>

          <!-- delete payment method modal -->
          <FanDeletePaymentMethodModal
            :payment-method-id="paymentMethod.id"
            @deleted="() => deletePaymentMethod(paymentMethod.id)"
          >
            <UButton
              icon="i-heroicons-trash"
              variant="ghost"
              color="error"
              size="sm"
            />
          </FanDeletePaymentMethodModal>
        </div>
      </div>
    </div>

    <!-- add new payment method -->
    <FanPaymentMethodModal mode="add" :title="$t('global.add_new_payment_method')" @submit="handlePaymentMethodSubmit">
       <div class="p-4 rounded-xl border border-muted flex justify-center items-center gap-2 cursor-pointer border-dashed bg-[#22202C] hover:brightness-105">
        <UIcon name="i-heroicons-plus"/>
        <span class="text-sm">
          {{ $t('global.add_new_payment_method') }}
        </span>
      </div>
    </FanPaymentMethodModal>

    <!-- OK Button. emit close -->
     <UButton 
      color="primary" 
      size="lg" 
      class="justify-center rounded-full"
      @click="emit('selected', selectedPaymentMethod)"
    >
      {{ $t('global.ok') }}
    </UButton>
  </div>
</template>

<script lang="ts" setup>

const props = defineProps<{
  defaultPaymentMethod?: any;
}>();

const emit = defineEmits(['selected']);

// Use payment methods composable
const { paymentMethods, selectedPaymentMethod, defaultPaymentMethodId, fetchPaymentMethods, deletePaymentMethod, setSelectedPaymentMethod } = usePaymentMethods();

// Initialize payment methods and set default
console.log('🚀 PaymentMethodList: Initializing payment methods...');
await fetchPaymentMethods();
console.log('🚀 PaymentMethodList: Initial payment methods loaded:', paymentMethods.value.length);

const handlePaymentMethodSubmit = async (paymentMethodId: string) => {
  console.log('🎉 Payment method submitted with ID:', paymentMethodId);
  console.log('📋 Current payment methods before refresh:', paymentMethods.value.length);

  // Refresh payment methods from API after adding/editing
  await fetchPaymentMethods();

  console.log('📋 Payment methods after refresh:', paymentMethods.value.length);

  const newPaymentMethod = paymentMethods.value.find((pm) => pm.id === paymentMethodId);
  console.log('🔍 Found new payment method:', newPaymentMethod ? 'Yes' : 'No', newPaymentMethod?.id);

  setSelectedPaymentMethod(newPaymentMethod || null);
};

// Check if payment method is the default payment method
const isDefaultPaymentMethod = (paymentMethod: any) => {
  return defaultPaymentMethodId.value === paymentMethod.id;
};

// Import utility functions from payments utility
const { getCardBrandDisplay, getCardBrandLogo } = await import('../../../utils/payments');

</script>
