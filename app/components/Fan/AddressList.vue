<template>
  <div class="grid gap-4 -mt-6">
    <!-- scrollable list -->
    <div class="overflow-y-scroll max-h-[60vh] flex flex-col gap-2"  v-auto-animate>
      <div
        v-for="address in addresses"
        :key="address.id"
        @click="setSelectedAddress(address)"
        :class="[
          'flex gap-4 p-4 rounded-xl border cursor-pointer bg-[#22202C] hover:border-primary hover:bg-primary/10',
          selectedAddress?.id === address.id ? 'border-primary bg-primary/10' : 'border-muted'
        ]">
        <div class="flex flex-col gap-0.5 grow"  v-auto-animate>
          <p class="text-sm font-bold">{{ address.name }}</p>
          <p class="text-xs">{{ address.address_line_1 }}</p>
          <p v-if="address.address_line_2" class="text-xs">{{ address.address_line_2 }}</p>
          <p class="text-xs">{{ address.city }}</p>
          <p class="text-xs">{{ address.postcode }}</p>
          <p class="text-xs">{{ address.province }} {{ COUNTRIES[locale].find(country => country.value === address.country)?.label }}</p>

        </div>

        <!-- default ubadge -->
         <div class="flex flex-col justify-center gap-1"  v-auto-animate>
           <UBadge v-if="isDefaultAddress(address)" variant="soft" color="secondary" class="text-white self-center capitalize">
             {{ $t('global.default') }}
            </UBadge>

            <!-- action buttons (delete, edit) -->
            <div class="flex gap-2 items-center justify-end">
              <!-- edit address modal -->
              <FanShippingAddressModal mode="edit" :address="address" :key="address.id" :title="$t('global.edit_address')"
              @submit="handleAddressSubmit">
                <UButton icon="i-heroicons-pencil" variant="ghost" color="neutral" size="sm"></UButton>
              </FanShippingAddressModal>

              <!-- delete address modal -->
              <FanDeleteAddressModal
                :addressId="address.id"
                @deleted="() => deleteAddress(address.id)"
              >
                <UButton
                  icon="i-heroicons-trash"
                  variant="ghost"
                  color="error"
                  size="sm"
                />
              </FanDeleteAddressModal>
            </div>
          </div>
      </div>
    </div>

    <!-- add new shipping address -->
    <FanShippingAddressModal  mode="add" :title="$t('global.add_new_shipping_address')" @submit="handleAddressSubmit">
       <div class="p-4 rounded-xl border border-muted flex justify-center items-center gap-2 cursor-pointer border-dashed bg-[#22202C] hover:brightness-105">
        <UIcon name="i-heroicons-plus"/>
        <span class="text-sm">
          {{ $t('global.add_new_shipping_address') }}
        </span>
      </div>
    </FanShippingAddressModal>

    <!-- OK Button. emit close -->
     <UButton 
      color="primary" 
      size="lg" 
      class="justify-center rounded-full"
      @click="emit('selected',selectedAddress)"
    >
      {{ $t('global.ok') }}
    </UButton>
  </div>
</template>

<script lang="ts" setup>
const { profile } = useUser();
const props = defineProps<{
  defaultAddress?: Addresses;
}>();

const emit = defineEmits(['selected']);
const { locale } = useI18n();

// Use addresses composable
const { addresses, selectedAddress, fetchAddresses, deleteAddress, setSelectedAddress } = useAddresses();

// Initialize addresses and set default
await fetchAddresses()

// Get user data
const { data: profileData, refresh: refreshProfile } = useFetch('/api/me');
profile.value = profileData.value || null;

const handleAddressSubmit = async (addressId: number) => {
  await refreshProfile();
  profile.value = profileData.value || null;
  await fetchAddresses();
  const newAddress = addresses.value.find((address) => address.id === addressId);
  setSelectedAddress(newAddress || null);
};

// Check if address is the default address
const isDefaultAddress = (address: Addresses) => {
  return address.id === profile.value?.default_address;
};


</script>