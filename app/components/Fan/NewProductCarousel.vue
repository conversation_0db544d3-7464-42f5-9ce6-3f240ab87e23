<template>
  <div class="no-scrollbar flex gap-4 overflow-x-auto">
    <!-- {{ products }} -->
    <VerticalProductCard v-for="item in products" :product="item" class="max-h-96 min-w-2xs w-2xs" mode="fan" />
  </div>
</template>

<script lang="ts" setup>
const { useDirectusFetch } = useDirectus();
const { data: products } = useDirectusFetch<Products[]>('/items/products', {
  params: {
    fields: [
      'id, translations.*, price, price_krw, price_jpy,  base_currency, main_image, category.code, discount, category.translations.*, user_created.avatar',
      'creator.nickname',
    ],
    limit: 15,
  },
});
</script>
