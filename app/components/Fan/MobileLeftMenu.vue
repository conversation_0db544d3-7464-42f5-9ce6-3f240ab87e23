<template>
  <div>
    <div class="flex flex-col gap-4 rounded-lg border border-zinc-800 bg-zinc-900 p-4 backdrop-blur-xl" v-auto-animate>
      <!-- Clear All Filters Button -->
      <UButton
        v-if="filterApplied"
        @click="resetFilter"
        size="sm"
        variant="outline"
        color="primary"
        icon="i-heroicons-x-mark"
        class="w-full text-left"
      >
        {{ $t('global.clear_all_filters') }}
      </UButton>
      <div class="flex flex-col">
        <p class="font-bold">{{ $t('global.status') }}</p>
        <UButton
          size="xs"
          variant="ghost"
          color="neutral"
          :active-class="filter.status !== '' ? '!text-primary-500' : ''"
          :active="filter.status !== ''"
          @click="filter.status = filter.status !== '' ? '' : 'P00'"
          :trailing-icon="filter.status !== '' ? 'i-heroicons-x-mark' : ''"
          >{{ $t('global.new') }}</UButton
        >
        <!-- <UButton size="xs" variant="ghost" color="neutral">{{ $t("global.sale") }}</UButton>
        <UButton size="xs" variant="ghost" color="neutral">{{ $t("global.reserve") }}</UButton>
        <UButton size="xs" variant="ghost" color="neutral">{{ $t("global.restock") }}</UButton> -->
      </div>

      <div class="flex flex-col">
        <p class="font-bold">{{ $t('global.availability') }}</p>
        <UButton
          @click="filter.availability = filter.availability == 'instock' ? '' : 'instock'"
          size="xs"
          variant="ghost"
          color="neutral"
          :active-class="filter.availability == 'instock' ? '!text-primary-500' : ''"
          :active="filter.availability == 'instock'"
          :trailing-icon="filter.availability == 'instock' ? 'i-heroicons-x-mark' : ''"
          class="w-full text-left"
          >{{ $t('global.in_stock') }}
        </UButton>
        <UButton
          @click="filter.availability = filter.availability == 'outofstock' ? '' : 'outofstock'"
          size="xs"
          variant="ghost"
          color="neutral"
          :active-class="filter.availability == 'outofstock' ? '!text-primary-500' : ''"
          :active="filter.availability == 'outofstock'"
          :trailing-icon="filter.availability == 'outofstock' ? 'i-heroicons-x-mark' : ''"
          class="w-full text-left"
          >{{ $t('global.out_of_stock') }}
        </UButton>
      </div>

      <div class="flex flex-col">
        <p class="font-bold">{{ $t('global.product_categories') }}</p>
        <!-- All button -->
        <UButton
          @click="filter.productCategory = ''"
          size="xs"
          variant="ghost"
          color="neutral"
          class="w-full text-left"
        >
          {{ $t('global.all') }}
        </UButton>

        <UButton
          v-for="cat in PRODUCT_CATEGORIES"
          size="xs"
          :key="cat.value"
          active-class="!text-primary-500"
          :active="filter.productCategory === cat.value"
          @click="filter.productCategory = filter.productCategory === cat.value ? '' : cat.value"
          variant="ghost"
          color="neutral"
          class="w-full text-left"
          :trailing-icon="filter.productCategory === cat.value ? 'i-heroicons-x-mark' : ''"
        >
          {{ $t(cat.label) }}
        </UButton>
      </div>

      <!-- Price Range -->
      <div class="flex flex-col">
        <p class="font-bold">{{ $t('global.price_range') }}</p>
        <UButton
          size="xs"
          variant="ghost"
          color="neutral"
          @click="clearPriceFilter"
          class="w-full text-left !text-white"
        >
          {{ $t('global.all') }}
        </UButton>
        <UButton
          v-for="range in localizedPriceRanges"
          :key="`${range.min}-${range.max}`"
          size="xs"
          variant="ghost"
          color="neutral"
          :active-class="isPriceRangeActive(range.min, range.max) ? '!text-primary-500' : ''"
          :active="isPriceRangeActive(range.min, range.max)"
          @click="setPriceRange(range.min, range.max)"
          :trailing-icon="isPriceRangeActive(range.min, range.max) ? 'i-heroicons-x-mark' : ''"
          class="w-full text-left"
        >
          {{ range.label }}
        </UButton>
      </div>

      <!-- IP Categories -->
      <div class="flex flex-col">
        <p class="font-bold">{{ $t('global.ip_category') }}</p>
        <UButton size="xs" variant="ghost" color="neutral" @click="filter.ipCategory = ''" class="w-full text-left">
          {{ $t('global.all') }}
        </UButton>
        <UButton
          v-for="cat in ipCategories"
          size="xs"
          :key="cat.code"
          variant="ghost"
          color="neutral"
          :active-class="'!text-primary-500'"
          :active="filter.ipCategory === cat.code"
          @click="filter.ipCategory = filter.ipCategory === cat.code ? '' : cat.code"
          :trailing-icon="filter.ipCategory === cat.code ? 'i-heroicons-x-mark' : ''"
          class="w-full text-left"
        >
          {{ useTranslatedName(cat.translations) }}
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PRICE_RANGES } from '~~/shared/utils/consts';

const { filter, filterApplied, resetFilter } = useFanFilter();
const { useDirectusFetch } = useDirectus();
const { locale } = useI18n();

// Get localized price ranges based on current locale
const localizedPriceRanges = computed(() => {
  return PRICE_RANGES[locale.value as keyof typeof PRICE_RANGES] || PRICE_RANGES.en;
});

// Fetch IP categories from Directus (same approach as IpCategoriesFilter.vue)
const { data: populatedIpCategories } = await useFetch('/api/ips/categories', { key: 'populated-ip-categories' });

const { data: ipCategories } = await useDirectusFetch<IpCategories[]>('/items/ip_categories', {
  key: 'ip-categories',
  watch: [locale], // Make reactive to locale changes
  params: {
    fields: ['code, translations.name, translations.languages_id'],
    filter: {
      id: {
        _in: populatedIpCategories,
      },
    },
    sort: ['translations.name'],
  },
});

// Price range functions
const clearPriceFilter = () => {
  filter.value.minPrice = '';
  filter.value.maxPrice = '';
};

const setPriceRange = (min: string, max: string) => {
  // If clicking on an already active range, clear it
  if (isPriceRangeActive(min, max)) {
    clearPriceFilter();
  } else {
    filter.value.minPrice = min;
    filter.value.maxPrice = max;
  }
};

const isPriceRangeActive = (min: string, max: string) => {
  return filter.value.minPrice === min && filter.value.maxPrice === max;
};
</script>
