<template>
  <div class="grid grid-cols-2 lg:flex gap-4">
    <!-- keyword filter -->
    <UInput
      :model-value="filter.productName"
      @input="debouncedSearchInput"
      :placeholder="$t('global.search_product')"
      icon="i-lucide-search"
    />
    <!-- creator filter -->
    <USelectMenu
      v-model="filter.creator"
      :items="creatorFilterItems"
      icon="i-lucide-user"
      class="w-full lg:w-48 cursor-pointer"
      :placeholder="$t('global.filter_by_creator')"
    />
    <UModal fullscreen :title="$t('global.product_filters')" class="lg:hidden">
      <UButton variant="outline" color="secondary">
        {{ $t('global.product_filters') }}
      </UButton>
      <template #close>
        <UButton class="ml-auto" icon="i-heroicons-check" color="primary" >
        </UButton>
      </template>
      <template #body>
        <FanMobileLeftMenu class="overflow-y-scroll h-full" />
      </template>
    </UModal>
    
    <!-- sort filter -->
    <USelect
      v-model="filter.sortBy"
      :items="sortItems"
      class="w-full lg:w-48 cursor-pointer"
      icon="i-lucide-sort-desc"
      :placeholder="$t('global.sort_by')"
    />
  </div>
</template>

<script lang="ts" setup>
const { t } = useI18n();
const { filter } = useFanFilter();
const { data: creators } = await useFetch('/api/creators');

// Add a watcher for searchKeyword with debounce
const debouncedSearchInput = useDebounceFn(
  (e: any) => {
    filter.value.productName = e.target.value;
  },
  500,
  { maxWait: 3000 },
);

const creatorFilterItems = computed(() => {
  if (!creators.value) return [];
  return [
    {
      label: t('global.all'),
      value: '',
    },
    ...creators.value.map((creator: any) => ({
      label: creator.nickname,
      value: creator.id,
    })),
  ];
});

const sortItems = ref([
  {
    label: t('global.newest'),
    value: 'latest',
  },
  {
    label: t('global.oldest'),
    value: 'oldest',
  },
  {
    label: t('global.price_low_to_high'),
    value: 'price_low',
  },
  {
    label: t('global.price_high_to_low'),
    value: 'price_high',
  },
  {
    label: t('global.best_seller'),
    value: 'best_seller',
  },
]);
</script>
