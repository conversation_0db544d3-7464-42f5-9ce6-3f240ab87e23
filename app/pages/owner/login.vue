<template>
  <div class="grid lg:grid-cols-2">
    <div id="intro" class="hidden lg:grid justify-center items-center">
      <div>
        <img src="/images/logo-w-text.svg">
        <h1 class="uppercase text-7xl font-semibold drop-shadow-2xl">Where<br/>Creativity<br/>Meets<br/>Oppertinity</h1>
      </div>
    </div>
    <div id="login" class="bg-[#1B1A21B2]/70 backdrop-blur-lg">
      <Login mode="owner" />
    </div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: "fullscreen-video",
  title: 'pages.owner.login.title'
});

const { accessToken, refreshToken } = useUser();
const localePath = useLocalePath()

if (refreshToken.value && !accessToken.value) {
  try {
    await useFetch('/api/auth/refresh-token', {
      method: 'POST',
    });
    navigateTo(localePath('owner-ip'))
  } catch (error) {
    console.error(error)
  }
}
</script>
