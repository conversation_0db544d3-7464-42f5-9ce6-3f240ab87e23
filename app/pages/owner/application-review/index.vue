<template>
  <UPage>
    <UPageBody v-auto-animate>
      <h2 class="text-3xl font-bold my-4">{{ $t('global.product_application_review') }}</h2>
      <div class="flex gap-4 my-4">
      </div>
      <!-- Mobile-friendly filter section -->
      <div class="flex flex-col gap-4 md:flex-row">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 flex-1">
          <!-- IP filter -->
          <USelectMenu
            v-model="selectedIpId"
            value-key="id"
            :items="localizedIpFilterItems"
            icon="i-lucide-layers"
            class="cursor-pointer"
            :placeholder="$t('global.filter_by_ip')"
          />
          <!-- select product category filter -->
          <USelectMenu
            v-model="selectedProductCategoryCode"
            value-key="code"
            :items="localzedProductCategoryItems"
            icon="i-lucide-tag"
            class="cursor-pointer"
            :placeholder="$t('global.filter_by_product_category')"
          />
          <!-- select status filter -->
          <USelect
            v-model="selectedStatus"
            :items="statusItems"
            class="cursor-pointer"
            icon="i-lucide-filter"
            :placeholder="$t('global.filter_by_status')"
          />
        </div>
        <!-- Product Keyword Search Input -->
        <UInput
          :model-value="selectedProductName"
          @input="debouncedSearchInput"
          :placeholder="$t('global.search_product')"
          icon="i-lucide-search"
          class="md:w-64"
        />
      </div>
      <div class="-mt-10">
        <div id="product-application-list" class="" v-auto-animate>
          <!-- Desktop table headers - hidden on mobile -->
          <div id="orders-headers" class="hidden md:grid grid-cols-7 p-4 gap-4 text-sm text-neutral-500">
            <p>{{ $t('global.preview_image') }}</p>
            <p>{{ $t('global.product_name') }}</p>
            <p>{{ $t('global.ip') }}</p>
            <p>{{ $t('global.date_applied') }}</p>
            <p>{{ $t('global.product_category') }}</p>
            <p>{{ $t('global.status') }}</p>
            <p>{{ $t('global.operation') }}</p>
          </div>

          <!-- Product items -->
          <div
            :id="`item-${product.id}`"
            v-for="product in products"
            :key="product.id"
            class="rounded-lg border border-neutral-700 p-4 mb-4 md:grid md:grid-cols-7 md:items-center md:gap-4 md:p-2"
          >
            <!-- Mobile card layout -->
            <div class="md:hidden space-y-4">
              <!-- Product image and basic info -->
              <div class="flex gap-4">
                <div class="flex-shrink-0 w-20 h-20">
                  <NuxtLinkLocale :to="{ name: 'owner-products-id', params: { id: product.id } }">
                    <NuxtImg
                      :src="directusAssetsUrl(product?.main_image as string, 200, 200) ?? '/images/missing-product.png'"
                      class="h-20 w-20 rounded-lg object-cover"
                      placeholder
                      placeholder-class="blur-xs"
                    />
                  </NuxtLinkLocale>
                </div>
                <div class="flex-1 min-w-0">
                  <h3 class="font-medium text-sm mb-1 truncate">{{ useTranslatedName(product.translations) }}</h3>
                  <p class="text-xs text-neutral-400 mb-1">{{ useTranslatedName((product.ip as Ip).translations) }}</p>
                  <p class="text-xs text-neutral-500">{{ format(new Date(product.date_updated!), 'yyyy-MM-dd') }}</p>
                </div>
              </div>

              <!-- Category and status row -->
              <div class="flex items-center justify-between">
                <div class="text-xs text-neutral-400">
                  {{ useTranslatedName((product.category as ProductCategories).translations) }}
                </div>
                <UBadge
                  class="justify-center px-3 py-1 capitalize text-xs"
                  variant="subtle"
                  :color="getStatusColor(product.status)"
                >
                  {{ $t(`global.${product.status}`) }}
                </UBadge>
              </div>

              <!-- Action buttons or comment -->
              <div v-if="product.status === 'pending'" class="flex gap-2">
                <OwnerApprovalModal :product="product" mode="reject" @submit="refresh">
                  <UButton color="secondary" size="sm" class="flex-1 justify-center rounded-full">
                    {{ $t('global.reject') }}
                  </UButton>
                </OwnerApprovalModal>
                <OwnerApprovalModal :product="product" mode="approve" @submit="refresh">
                  <UButton color="success" size="sm" class="flex-1 justify-center rounded-full">
                    {{ $t('global.approve') }}
                  </UButton>
                </OwnerApprovalModal>
              </div>
              <NuxtLinkLocale
                v-else
                :to="{ name: 'owner-products-id', params: { id: product.id } }"
                class="block text-xs text-neutral-400 line-clamp-3 hover:text-neutral-300 transition-colors">
                {{ product.approval_comment }}
              </NuxtLinkLocale>
            </div>

            <!-- Desktop table layout -->
            <div class="hidden md:contents">
              <div>
                <NuxtLinkLocale :to="{ name: 'owner-products-id', params: { id: product.id } }">
                  <NuxtImg
                    :src="directusAssetsUrl(product?.main_image as string, 200, 200) ?? '/images/missing-product.png'"
                    class="h-32 w-full rounded-lg object-cover"
                    placeholder
                    placeholder-class="blur-xs"
                  />
                </NuxtLinkLocale>
              </div>
              <div>
                <p>{{ useTranslatedName(product.translations) }}</p>
              </div>
              <div class="text-sm">
                <p>{{ useTranslatedName((product.ip as Ip).translations) }}</p>
              </div>
              <div class="text-sm">
                <p>{{ format(new Date(product.date_updated!), 'yyyy-MM-dd') }}</p>
              </div>
              <div class="text-sm">
                <p>{{ useTranslatedName((product.category as ProductCategories).translations) }}</p>
              </div>
              <div class="text-sm">
                <UBadge
                  class="justify-center px-4 capitalize"
                  variant="subtle"
                  :color="getStatusColor(product.status)"
                >
                  {{ $t(`global.${product.status}`) }}
                </UBadge>
              </div>
              <div class="grid gap-4" v-if="product.status === 'pending'">
                <OwnerApprovalModal :product="product" mode="reject" @submit="refresh">
                  <UButton color="secondary" size="sm" class="w-max md:min-w-20 lg:min-w-32 xl:min-w-40 lg:px-8 justify-center rounded-full">
                    {{ $t('global.reject') }}
                  </UButton>
                </OwnerApprovalModal>
                <OwnerApprovalModal :product="product" mode="approve" @submit="refresh">
                  <UButton color="success" size="sm" class="w-max md:min-w-20 lg:min-w-32 xl:min-w-40 lg:px-8 justify-center rounded-full">
                    {{ $t('global.approve') }}
                  </UButton>
                </OwnerApprovalModal>
              </div>
              <NuxtLinkLocale
                v-else
                :to="{ name: 'owner-products-id', params: { id: product.id } }"
                class="break-word text-xs line-clamp-4 max-w-40">
                {{ product.approval_comment }}
              </NuxtLinkLocale>
            </div>
          </div>
          <div v-if="products?.length === 0" class="mt-8 w-full">
            <p class="text-center text-xl font-bold">{{ $t('global.no_products_available') }}</p>
          </div>
        </div>
      </div>
    </UPageBody>
  </UPage>
</template>

<script setup lang="ts">
import { format } from 'date-fns';
import { getStatusColor } from '~~/shared/utils/ui';

definePageMeta({
  title: 'pages.owner.applicationReview.title',
  layout: 'owner',
  middleware: 'owner-auth'
});

const { useDirectusFetch } = useDirectus();
const { profile } = useUser();
const { t } = useI18n();
const { directusAssetsUrl } = useDirectus();

const selectedProductName = ref('')
const selectedIpId = ref('')
const selectedStatus = ref('')
const selectedProductCategoryCode = ref('')

const debouncedSearchInput = useDebounceFn(
  (e: any) => {
    selectedProductName.value = e.target.value;
  },
  500,
  { maxWait: 3000 },
);

const { data: profileData } = await useFetch('/api/me', {
  key: 'profile',
  params: {
    fields: ['*','ip_owner_profile.*','role.name'],
  },
});
profile.value = profileData.value || null;


const filterParams = computed(()=> {
  const filters: any[] = [];

  // Product name search filter
  if (selectedProductName.value !== '' && selectedProductName.value !== undefined) {
    filters.push({
      translations: {
        name: {
          _icontains: selectedProductName.value,
        },
      },
    });
  }

  // IP filter - skip if 'all' is selected
  if (selectedIpId.value !== '' && selectedIpId.value !== undefined && selectedIpId.value !== 'all') {
    filters.push({
      ip: {
        _eq: parseInt(selectedIpId.value), // Convert string back to number for API
      },
    });
  }

  // Product category filter - skip if 'all' is selected
  if (selectedProductCategoryCode.value !== '' && selectedProductCategoryCode.value !== undefined && selectedProductCategoryCode.value !== 'all') {
    filters.push({
      category: {
        code: {
          _eq: selectedProductCategoryCode.value,
        },
      },
    });
  }

  // Status filter
  if (selectedStatus.value) {
    filters.push({
      status: {
        _eq: selectedStatus.value,
      },
    });
  }

  return {
    fields: [
      '*',
      'translations.*',
      'category.code',
      'category.translations.*',
      'ip.translations.ip_id',
      'ip.translations.name',
      'ip.translations.languages_id',
    ],
    sort: ['-date_updated'],
    filter: filters.length > 0 ? { _and: filters } : {},
  };
})

// Create a serializable key from filter values instead of the computed ref
const filterKey = computed(() => {
  return `owner-products-${selectedProductName.value}-${selectedIpId.value}-${selectedProductCategoryCode.value}-${selectedStatus.value}`;
});

const { data: products, refresh } = await useDirectusFetch<Products[]>(`/items/products`, {
  params: filterParams,
  key: 'owner-creator-products',
  server: false
});

// Get all IPs for this owner
const { data: ownerIps } = await useDirectusFetch(`/items/ip`, {
  key: `owner-ips`,
  params: {
    fields: ['id', 'translations.*'],
  },
});

// Status filter options
const statusItems = computed(() => [
  {
    label: t('global.all'),
    value: undefined,
  },
  {
    label: t('global.published'),
    value: 'published',
  },
  {
    label: t('global.pending'),
    value: 'pending',
  },
  {
    label: t('global.unpublished'),
    value: 'unpublished',
  },
  {
    label: t('global.rejected'),
    value: 'rejected',
  }
]);

// Create IP filter options from fetched data
const localizedIpFilterItems = computed(() => {
  const items = [
    {
      label: t('global.all'),
      id: 'all',
    }
  ];

  if (ownerIps.value && Array.isArray(ownerIps.value)) {
    ownerIps.value.forEach((ip: Ip) => {
      const ipName = useTranslatedName(ip.translations).value || `IP ${ip.id}`;
      items.push({
        label: ipName,
        id: String(ip.id), // Convert to string for consistency
      });
    });
  }

  return items;
});

// Create product category filter options from fetched data
const localzedProductCategoryItems = [
  {
    label: t('global.all'),
    code: 'all',
  },
  ...PRODUCT_CATEGORIES.map((product) => ({
    label: t(product.label),
    code: product.value,
  })),
];
</script>
