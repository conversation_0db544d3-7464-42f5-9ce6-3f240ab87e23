<template>
  <div class="flex min-h-screen flex-col items-center justify-center px-2">
    <div
    class="flex w-md max-w-full flex-col items-center rounded-lg border border-[#3F3D47] bg-[#1B1A2199] p-6 backdrop-blur-xl md:p-8"
      >
        <ULink to="/">
          <NuxtPicture src="/images/ipgo-cherry.svg" alt="IPGO" class="-mt-14 h-12 w-12 md:-mt-16" loading="eager" />
        </ULink>
        <h2 class="my-2 text-center text-xl font-bold">
          {{  'Sign up request sent!' }}
        </h2>
        <p class="text-center">
          {{ 'Your sign up request has been submitted. You will receive an email when your request is approved by IPGO admin.' }}
        </p>

        <UButton color="primary" size="lg" class="mt-4 w-full justify-center rounded-full" :to="localePath('index')">
          {{ 'Back to Home' }}
        </UButton>
      </div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: "fullscreen",
  title: "pages.owner.signup.title"
});

const localePath = useLocalePath();

// After 5 seconds, redirect to homepage
setTimeout(() => {
  navigateTo(localePath('index'));
}, 10000);
</script>
