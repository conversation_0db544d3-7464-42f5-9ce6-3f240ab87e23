<template>
  <UPage>
    <UPageBody class="min-h-[70vh]">
      <!-- Main top section -->
      <div class="grid grid-cols-5 gap-8">
        <!-- Left: Product Gallery Section -->
        <div id="product-gallery" class="col-span-5 flex flex-col-reverse gap-4 lg:col-span-3 md:flex-row">
          <div class="grid grid-cols-5 lg:grid-rows-5 md:grid-cols-1 gap-4 xl:w-32 gap 2xl:w-42">
          <!-- <div class="flex lg:w-20 xl:w-32 flex-shrink-0 flex-row gap-4 lg:flex-col"> -->
            <NuxtImg
              v-for="(image, index) in product?.images as ProductsFiles[]"
              :key="image.id"
              @click="selectedImageIndex = index"
              :src="directusAssetsUrl(image.directus_files_id as string, 200, 200) || '/images/missing-product.png'"
              :alt="`Product image ${index + 1}`"
              class="aspect-square w-full cursor-pointer rounded-lg object-cover"
              :class="selectedImageIndex === index ? 'ring-primary ring-2' : ''"
              placeholder
              placeholder-class="blur-xs"
            />
          </div>

          <!-- Main Image Section - Fixed container -->
          <div class="relative w-full overflow-hidden">
            <NuxtImg
              :src="selectedImage"
              :alt="translatedProductName || $t('global.preview_image')"
              class="w-full cursor-pointer rounded-lg object-cover aspect-square h-full md:h-[32rem] lg:h-full"
              @click="
                lightboxVisible = true;
                imageIndexRef = selectedImageIndex;
              "
            />
            <VueEasyLightbox
              :visible="lightboxVisible"
              :imgs="allImages"
              :index="imageIndexRef"
              @hide="lightboxVisible = false"
            />
          </div>
        </div>

        <!-- Right:Product Details Section (placeholder for future content) -->
        <div class="col-span-5 flex w-full flex-col gap-4 lg:col-span-2">
          <!-- Application Status and Message-->
           <div v-if="product?.status !== 'pending'" id="application-status" class="border rounded-lg border-neutral-700 p-4 bg-neutral-900">
            <p class="font-semibold text-sm" :class="`text-${isApproved? 'success' : 'error'}`">
              {{ isApproved ? $t('global.application_approved_by_ip') : $t('global.application_rejected_by_ip') }}
            </p>
            <p v-if="product?.approval_comment" class="text-muted text-sm mt-2 whitespace-pre-wrap max-h-32 overflow-y-scroll">{{ product?.approval_comment }}</p>
           </div>

          <div class="flex gap-4">
            <!-- Product Status -->
            <UBadge id="product-category" variant="outline" :color="getStatusColor(product?.status || '')" class="w-max">{{
               $t(`global.${product?.status}`)
            }}</UBadge>
            <!-- Product Category and Name -->
            <UBadge id="product-category" variant="outline" color="neutral" class="w-max">{{
              useTranslatedName((product?.category as ProductCategories).translations)
            }}</UBadge>
          </div>
          <!-- Product Name -->
          <h1 id="product-name" class="text-3xl font-bold">{{ translatedProductName }}</h1>

          <!-- Pricing with optional discount -->
          <div class="flex items-baseline gap-1">
            <template v-if="hasDiscount">
              <p class="text-primary font-primary text-xl">
                {{ formattedDiscountedPrice }}
              </p>
              <p class="text-xs text-neutral-400 line-through">
                {{ formattedCurrentPrice }}
              </p>
            </template>
            <template v-else>
              <p class="text-primary font-primary text-xl font-bold">
                {{ formattedCurrentPrice }}
              </p>
            </template>
          </div>

          <!-- Star Rating Component -->
          <div class="flex flex-row items-center gap-2 -mt-4">
            <vue3-star-ratings
              v-model="productRating"
              starColor="white"
              class="h-6"
              disable-click
            />
            <p class="text-sm text-neutral-500">({{ averageRating }})</p>
            <p id="review-count" v-if="(product as Products)?.reviews.length > 0">
              {{ product?.reviews.length }} {{ $t('global.reviews') }}
            </p>
          </div>

          <!-- Creator Avatar - Name -->
          <div id="creator-link" class="text-sm">
            <p class="font-semibold">{{ $t('global.creator') }}</p>
            <NuxtLinkLocale :to="{ name: 'owner-ip-id-creator-creatorId', params: { id: (product?.creator as Creator)?.id } }">
              <div class="flex flex-row items-center gap-2">
                <UAvatar
                  size="xs"
                  :src="
                    directusAssetsUrl((product?.creator as Creator)?.avatar as string) ?? '/images/missing-product.png'
                  "
                />
                <p class="text-sm text-muted">{{ (product?.creator as Creator).nickname ?? 'Creator' }}</p>
              </div>
            </NuxtLinkLocale>
          </div>

          <!-- Product Description -->
          <div id="product-description" class="text-sm">
            <p class="font-semibold">{{ $t('global.product_description') }}</p>
            <p class="line-clamp-5 text-neutral-500">{{ translatedProductDescription }}</p>
          </div>

          <!-- Total Stock -->
          <div id="total-stock" class="text-sm">
            <p class="font-semibold">{{ $t('global.total_stock') }}</p>
            <p class="text-neutral-500">{{ product?.stock_total ?? 0 }}</p>
          </div>

          <!-- Units Sold -->
          <div id="units-sold" class="text-sm">
            <p class="font-semibold">{{ $t('global.units_sold_preordered') }}</p>
            <p class="text-neutral-500">{{ product?.units_sold ?? 0 }}</p>
          </div>
          
          <!-- Remaining (Total - Sold) -->
          <div id="remaining-stock" class="text-sm">
            <p class="font-semibold">{{ $t('global.remaining_stock') }}</p>
            <p class="text-neutral-500">{{ product?.stock_remaining ?? 0 }}</p>
          </div>

          <!-- Total Revenue Generated -->
          <div id="total-revenue" class="text-sm">
            <p class="font-semibold">{{ $t('global.total_revenue_generated') }}</p>
            <p class="text-neutral-500">{{ product?.revenue_total ?? 0 }}</p>
          </div>

          <!-- Date Applied -->
          <div id="date-applied" class="text-sm">
            <p class="font-semibold">{{ $t('global.date_applied') }}</p>
            <p class="text-neutral-500">{{ format(new Date(product?.date_created!), 'yyyy-MM-dd') }}</p>
          </div>

          <!-- CTA Buttons -->
          <div class="grid grid-cols-2 gap-4">
            <!-- Add modal here later-->
            <OwnerApprovalModal :product="product!" mode="reject" @submit="refresh">
               <UButton
                color="secondary"
                size="lg"
                class="w-full justify-center rounded-full"
                >
                {{  $t('global.reject') }}
              </UButton>
            </OwnerApprovalModal>
            <OwnerApprovalModal :product="product!" mode="approve" @submit="refresh">
              <UButton
                color="success"
                size="lg"
                class="w-full justify-center rounded-full"
                >
                {{  $t('global.approve') }}
              </UButton>
            </OwnerApprovalModal>
          </div>
        </div>
      </div>
    </UPageBody>
  </UPage>
</template>

<script lang="ts" setup>
import { format } from 'date-fns';
import vue3StarRatings from 'vue3-star-ratings';
import { z } from 'zod/v4';

definePageMeta({
  layout: 'owner',
  middleware: 'owner-auth'
});

const route = useRoute('creator-my-products-id___en');
const { useDirectusFetch, directusAssetsUrl } = useDirectus();
const toast = useToast();

// Use creator product composable
const {
  isProductApproved,
  calculateAverageRating,
  toggleProductPublish,
  deleteProduct: deleteProductAction,
  useProductGallery
} = useCreatorProduct();

// Check if product is approved using composable
const isApproved = computed(() => isProductApproved(product.value?.status));

// Fetch product data
const { data: product, refresh } = await useDirectusFetch<Products>(`/items/products/${route.params.id}`, {
  key: `product-${route.params.id}`,
  params: {
    fields: ['*.*', 'status', 'translations.*', 'category.translations.*', 'images.*', 'reviews', 'reviews.user_created.avatar', 'reviews.user_created.nickname','reviews.user_created.first_name','reviews.user_created.last_name'],
  },
});

if (!product.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Product not found',
  });
}

const lightboxVisible = ref(false);

// Reactive product name for SEO and display
const translatedProductName = useTranslatedName(product.value!.translations);
const translatedProductDescription = useTranslatedDescription(product.value!.translations);

// Calculate average rating using composable
const averageRating = computed(() => calculateAverageRating(product.value?.reviews || []));

// Rating state management
const productRating = ref(averageRating.value);

// Gallery management using composable
const {
  selectedImageIndex,
  imageIndexRef,
  selectedImage,
  allImages
} = useProductGallery(product);

// Use utility functions for pricing logic
const hasDiscount = computed(() => (product.value ? hasProductDiscount(product.value) : false));
const formattedCurrentPrice = computed(() => (product.value ? formatProductCurrentPrice(product.value) : ''));
const formattedDiscountedPrice = computed(() => (product.value ? formatProductDiscountedPrice(product.value) : ''));

// SEO meta
useSeoMeta({
  title: () => translatedProductName.value,
  description: () => translatedProductDescription.value,
  ogTitle: () => translatedProductName.value,
  ogDescription: () => translatedProductDescription.value,
  ogImage: () => selectedImage.value,
});
</script>
