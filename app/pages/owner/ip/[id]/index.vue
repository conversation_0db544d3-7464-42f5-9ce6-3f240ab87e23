<template>
  <UPage>
    <!-- Full width banner with IP image and details overlay -->
    <div class="relative h-[55vh] w-full overflow-hidden lg:h-[65vh]" transition-style="in:circle:top-right">
      <NuxtImg
        v-if="ip?.main_image"
        :src="directusAssetsUrl(ip.main_image as string) ?? undefined"
        class="h-full w-full object-cover object-top blur-none transition-all duration-100"
        :alt="useTranslatedName(ip.translations).value"
        loading="eager"
        placeholder
        placeholder-class="blur-xs"
      />
      <div v-else class="h-full w-full bg-gradient-to-br from-neutral-800 to-neutral-900"></div>

      <!-- Gradient overlay that fades to background color -->
      <div class="absolute inset-0 bg-gradient-to-t from-[#1B1A21] via-transparent to-transparent"></div>

      <!-- Details overlay at bottom -->
      <div class="absolute bottom-0 left-0 w-full p-4 md:p-10">
        <div class="mx-auto flex max-w-[92rem] flex-col gap-2">
          <!-- IP Status -->
          <UBadge v-if="ip?.status" variant="outline" :color="getStatusColor(ip?.status || '')" class="w-max">{{
            $t(`global.${ip?.status}`)
          }}</UBadge>

          <!-- IP Category Badge -->
          <UBadge v-if="ip?.category" variant="outline" color="secondary" :ui="{ base: 'text-neutral' }" class="w-max">
            {{ useTranslatedName((ip.category as IpCategories).translations) }}
          </UBadge>

          <!-- IP Name -->
          <h1 class="text-2xl font-bold text-white md:text-3xl lg:text-4xl">
            {{ useTranslatedName(ip!.translations) }}
          </h1>

          <!-- IP Description -->
          <p class="md:text-md line-clamp-3 max-w-3xl text-sm text-neutral-200" transition-style="in:wipe:right">
            {{ useTranslatedDescription(ip!.translations) }}
          </p>

          <!-- Edit button -->
            <USlideover v-model:open="isEditIPOpen" >
              <UButton
                color="secondary"
                size="lg"
                class="justify-center rounded-full w-max px-12"
              >
                {{ $t('global.edit_ip_detail') }}
              </UButton>

              <template #content>
                <OwnerEditIpForm
                  :ip="ip!"
                  class="w-full p-8"
                  @updated="handleEditComplete()"
                  @close="isEditIPOpen = false"
                  @toggleStatus="handleToggleStatus"
                />
              </template>
            </USlideover>
        </div>
      </div>
    </div>

    <UContainer>
      <UPageBody>
        <section id="top-creators" class="grid gap-6">
           <div class="flex">
            <div class="flex w-full items-baseline gap-2">
              <h2 class="text-2xl font-bold">{{ $t('global.top_creators') }}</h2>
            </div>
          </div>

          <div class="no-scrollbar flex gap-4 overflow-x-auto">
            <OwnerCreatorCard
              v-for="creator in topCreators"
              :key="creator.id"
              :creator="creator"
              :ipId="route.params.id"
              class="h-64 w-48 flex-shrink-0"
            />
          </div>

          <!-- No results message -->
          <div v-if="topCreators?.length === 0" class="py-8 text-center">
            <p class="text-neutral-500">{{ $t('global.no_creator_found_ip') }}</p>
          </div>
        </section>


        <!-- Licensed Products section -->
        <section id="products" class="grid gap-6">
          <div class="flex">
            <div class="flex w-full items-baseline gap-2">
              <h2 class="text-2xl font-bold">{{ $t('global.licensed_products') }}</h2>

              <!-- Keyword Search for products -->
              <div class="flex flex-col-reverse md:flex-row gap-2 ml-auto">
                <UInput
                  :model-value="productSearchQuery"
                  @input="debouncedSearchInput"
                  :placeholder="$t('global.search_product')"
                  icon="i-lucide-search"
                  class="w-48"
                />
              
              </div>
            </div>
          </div>

          <!-- Product category UBadges -->
          <div class="no-scrollbar flex gap-2 overflow-x-auto -mt-4" v-auto-animate>
            <!-- All categories button -->
            <UBadge
              :variant="selectedProductCategory === null ? 'subtle' : 'soft'"
              :color="selectedProductCategory === null ? 'primary' : 'neutral'"
              class="whitespace-nowrap cursor-pointer"
              @click="selectedProductCategory = null"
            >
              {{ $t('global.all') }}
            </UBadge>

            <!-- Individual category badges -->
            <UBadge
              v-for="category in productCategories"
              :key="category.id"
              :variant="selectedProductCategory === category.id ? 'subtle' : 'soft'"
              :color="selectedProductCategory === category.id ? 'primary' : 'neutral'"
              class="whitespace-nowrap cursor-pointer"
              @click="selectedProductCategory = selectedProductCategory === category.id ? null : category.id"
            >
              {{ useTranslatedName(category.translations) }}
            </UBadge>
          </div>

          <div class="grid grid-cols-2 gap-4 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5" v-auto-animate>
            <LazyVerticalProductCard
              v-for="product in products"
              :key="product.id"
              mode="owner"
              :product="product"
              class="max-h-[460px]"
            />
          </div>

          <!-- No results message -->
          <div v-if="products?.length === 0" class="py-8 text-center">
            <p class="text-neutral-500">{{ $t('global.no_products_found') }}</p>
          </div>
        </section>
      </UPageBody>
    </UContainer>
  </UPage>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'owner-fullwidth-header',
  title: 'pages.ip.details.title',
  middleware: 'owner-auth',
});

const toast = useToast();
const route = useRoute('creator-ip-id___en');
const { t } = useI18n();
const { useDirectusFetch, directusAssetsUrl } = useDirectus();
const { profile } = useUser();
const isEditIPOpen = ref(false);
const localePath = useLocalePath();

// Search functionality for products
const productSearchQuery = ref('');
// Selected product category for filtering
const selectedProductCategory = ref<number | null>(null);

// Debounced search input handler
const debouncedSearchInput = useDebounceFn(
  (e: any) => {
    productSearchQuery.value = e.target.value;
  },
  500,
  { maxWait: 3000 },
);

// Fetch IP data with all related information
const { data: ip, refresh } = await useDirectusFetch<Ip>(`/items/ip/${route.params.id}`, {
  params: {
    fields: [
      'id',
      'translations.*',
      'main_image',
      'images.directus_files_id',
      'category.id',
      'category.translations.*',
      'keywords',
      'owner.*',
      "status"
    ],
  },
  onResponseError(error) {
    navigateTo(localePath('owner-ip'))
  },
});

// Fetch creators for this IP
const { data: topCreators } = await useDirectusFetch<Creator[]>('/items/creator', {
  key: `ip-creators-${route.params.id}`,
  params: {
    fields: ['*.*'],
    filter: {
      products: {
        ip: {
          _eq: route.params.id,
        },
      },
    },
  },
});


// Create computed filter query
const filterProductsParams = computed(() => {
  const filters : Record<string, any>[] = [
    {
      ip: {
        _eq: route.params.id,
      },
    },
  ];

  // Search by name filter
  if (productSearchQuery.value) {
    filters.push({
      _or: [
        {
          translations: {
            name: {
              _icontains: productSearchQuery.value,
            },
          },
        },
        {
          translations: {
            description: {
              _icontains: productSearchQuery.value,
            },
          },
        },
      ],
      translations: {
        name: {
          _icontains: productSearchQuery.value,
        },
      },
    });
  }

  // Product category filter
  if (selectedProductCategory.value) {
    filters.push({
      category: {
        _eq: selectedProductCategory.value,
      },
    });
  }

  return filters
});

// Fetch product categories for this IP
const { data: productCategoryIds } = await useFetch(`/api/products/categories?ipId=${route.params.id}`, {
  key: `ip-product-categories-${route.params.id}`,
});

// Fetch full product category data with translations
const { data: productCategories } = await useDirectusFetch<ProductCategories[]>('/items/product_categories', {
  key: `ip-product-categories-full-${route.params.id}`,
  params: {
    fields: ['id', 'code', 'translations.*'],
    filter: {
      id: {
        _in: productCategoryIds.value || [],
      },
    },
  },
});

// Fetch products for this IP
const { data: products} = await useDirectusFetch<Products[]>('/items/products', {
  key: `ip-products-${route.params.id}`,
  watch: [filterProductsParams], // Make reactive to filter changes
  params: {
    fields: [
      'id',
      'status',
      'translations.*',
      'price',
      'price_jpy',
      'price_krw',
      'base_currency',
      'discount',
      'main_image',
      'category.translations.*',
      'creator.nickname',
      'creator.*',
      'user_created.avatar',
    ],
    filter: {
      _and: filterProductsParams,
    },
    sort: ['-date_created'],
  },
});

const handleEditComplete = () => {
  // Refresh the entire page to ensure all data and images are updated
  window.location.reload();
}

const { execute } = useDirectusFetch(`/items/ip/${route.params.id}`,{
  key: `ip-${route.params.id}`,
  method: 'PATCH',
  body: {
    status: ip.value?.status === 'published' ? 'unpublished' : 'published',
  },
  server: false,
  immediate: false
});

const handleToggleStatus = async () => {
  try {
    await execute();
    toast.add({
      title: ip.value?.status === 'published' ? t('global.deactivated') : t('global.deactivated'),
      color: 'success',
    });

    // refresh page
    window.location.reload();
    
  } catch (error) {
    toast.add({
      title: t('global.error_changing_status'),
      color: 'error',
    });
    console.error('Failed to toggle IP status:', error);
  }
}

// SEO and page meta
useSeoMeta({
  title: () => ip.value?.translations ? useTranslatedName(ip.value.translations).value : '',
  description: () => ip.value?.translations ? useTranslatedDescription(ip.value.translations).value : '',
  ogTitle: () => ip.value?.translations ? useTranslatedName(ip.value.translations).value : '',
  ogDescription: () => ip.value?.translations ? useTranslatedDescription(ip.value.translations).value : '',
  ogImage: () => directusAssetsUrl(ip.value?.main_image as string) ?? undefined,
});
</script>