<template>
  <UPage>
    <UPageBody>
      <div class="border border-muted p-6 flex flex-row gap-4 rounded-lg bg-[#22202C]">
        <NuxtImg class="rounded-full w-36 h-36 aspect-square border border-muted" width="140" height="140"  :src="directusAssetsUrl(creator?.avatar as string) ?? '/images/missing-product.png'" />
        <div id="creator-info" class="flex flex-col gap-2 justify-center">
          <p class="text-3xl font-bold">{{ creator?.nickname }}</p>
          <p class="text-sm">{{ creator?.introduction }}</p>
        </div>
      </div>

      <section id="licensed-products" class="grid gap-4">
        <!-- Header with search and category filters -->
        <div class="flex">
          <div class="flex w-full items-baseline gap-2">
            <h2 class="text-2xl font-bold">{{ $t('global.licensed_products_under') }} {{ useTranslatedName(ip?.translations ?? []) }}</h2>

            <!-- Keyword Search for products -->
            <div class="flex flex-col-reverse md:flex-row gap-2 ml-auto">
              <UInput
                :model-value="productSearchQuery"
                @input="debouncedSearchInput"
                :placeholder="$t('global.search_product')"
                icon="i-lucide-search"
                class="w-48"
              />
            </div>
          </div>
        </div>
        
        <!-- Product category UBadges -->
        <div class="no-scrollbar flex gap-2 overflow-x-auto" v-auto-animate>
          <!-- All categories button -->
          <UBadge
            :variant="selectedProductCategory === null ? 'subtle' : 'soft'"
            :color="selectedProductCategory === null ? 'primary' : 'neutral'"
            class="whitespace-nowrap cursor-pointer"
            @click="selectedProductCategory = null"
          >
            {{ $t('global.all') }}
          </UBadge>

          <!-- Individual category badges -->
          <UBadge
            v-for="category in productCategories"
            :key="category.id"
            :variant="selectedProductCategory === category.id ? 'subtle' : 'soft'"
            :color="selectedProductCategory === category.id ? 'primary' : 'neutral'"
            class="whitespace-nowrap cursor-pointer"
            @click="selectedProductCategory = selectedProductCategory === category.id ? null : category.id"
          >
            {{ useTranslatedName(category.translations) }}
          </UBadge>
        </div>

        <div class="grid grid-cols-2 gap-4 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5" v-auto-animate>
          <LazyVerticalProductCard
            v-for="product in products"
            :key="product.id"
            mode="owner"
            :product="product"
            class="max-h-[460px]"
          />
        </div>

        <!-- No results message -->
        <div v-if="!products || products.length === 0" class="py-8 text-center">
          <p class="text-neutral-500">{{ $t('global.no_products_found') }}</p>
        </div>
      </section>
    </UPageBody>
  </UPage>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'owner',
  title: 'pages.owner.creator.title',
  middleware: 'owner-auth',
});

const route = useRoute('owner-ip-id-creator-creatorId___en')
const { useDirectusFetch , directusAssetsUrl} = useDirectus();
const productSearchQuery = ref('');
const selectedProductCategory = ref<number | null>(null);

const debouncedSearchInput = useDebounceFn(
  (e: any) => {
    console.log('Search input changed:', e.target.value);
    productSearchQuery.value = e.target.value;
  },
  500,
  { maxWait: 3000 },
);


// Build dynamic filter based on search query and category using standard pattern
const creatorFilter = computed(() => {
  const filters: any[] = [
    {
      ip: {
        _eq: route.params.id,
      },
      creator: {
        _eq: route.params.creatorId,
      },
    },
  ];

  // Add search query filter if it exists
  if (productSearchQuery.value && productSearchQuery.value.trim()) {
    filters.push({
      translations: {
        name: {
          _icontains: productSearchQuery.value.trim(),
        },
      },
    });
  }

  // Add category filter if selected
  if (selectedProductCategory.value !== null) {
    filters.push({
      category: {
        _eq: selectedProductCategory.value,
      },
    });
  }

  const result = filters.length > 1 ? { _and: filters } : filters[0];
  return result;
});

const { data: creator } = await useDirectusFetch<Creator>(`/items/creator/${route.params.creatorId}`, {
  key: `creator-${route.params.creatorId}`,
  params: {
    fields: ['id', 'nickname', 'introduction', 'avatar'],
  },
});

const { data: products, status } = await useDirectusFetch<Products[]>('/items/products', {
  key: `creator-products-${route.params.creatorId}`,
  watch: [creatorFilter],
  params: {
    fields: [
      'id',
      'status',
      'translations.*',
      'price',
      'price_jpy',
      'price_krw',
      'base_currency',
      'discount',
      'main_image',
      'category.translations.*',
      'category.id',
      'creator.nickname',
      'creator.*',
      'user_created.avatar',
    ],
    filter: creatorFilter,
    sort: ['-date_updated'],
  },
});

const { data: ip } = useDirectusFetch<Ip>(`/items/ip/${route.params.id}`, {
  params: {
    fields: ['id, translations.*'],
  },
});

// Set once on load to get all available categories
const productCategories = products.value?.reduce((acc: ProductCategories[], product) => {
  // Check if category exists and is an object (not just a number ID)
  if (product.category && typeof product.category === 'object' && 'id' in product.category) {
    const categoryObj = product.category as ProductCategories;
    if (!acc.find((category) => category.id === categoryObj.id)) {
      acc.push(categoryObj);
    }
  }
  return acc;
}, [] as ProductCategories[]);

</script>
