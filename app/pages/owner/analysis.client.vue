<script setup lang="ts">
import {today, getLocalTimeZone, toCalendarDate} from '@internationalized/date';

definePageMeta({
  title: 'pages.owner.analysis.title',
  middleware: 'owner-auth',
  layout: 'owner',
})

const { profile } = useUser()
const { t } = useI18n()

const startDate = ref(today(getLocalTimeZone()).subtract({ months: 3 })) // 3 months ago
const endDate = ref(today(getLocalTimeZone()).add({ days: 1 })) // Tomorrow to avoid timezone issues
const selectedIps = ref<Ip[]>([])
const selectedCreators = ref<Creator[]>([])
const { useDirectusFetch } = useDirectus()

// Type the API response
interface ChartDataPoint {
  date: string
  [ipKey: string]: number | string
}

interface IpInfo {
  ip: {
    id: number
    translations: Array<{
      name: string
      languages_id: number
    }>
  }
  color: string
}

interface ApiResponse {
  revenueChartData: ChartDataPoint[]
  quantityChartData: ChartDataPoint[]
  ipData: Record<string, IpInfo>
}

// Get profile
const { data: profileData } = await useFetch('/api/me', {
  key: 'profile',
  params: {
    fields: ['*','ip_owner_profile.*','role.name'],
  },
});
profile.value = profileData.value || null;

// Get all IPs that this owner has products for.
const {data: ownerIps } = await useDirectusFetch<Ip[]>('/items/ip',{
  params:{
    fields: ['translations.*','id'],
  }
})

const { data: ownerCreatorObjects } = await useDirectusFetch<{creator: number}[]>('/items/products',{
  key: 'owner-product-creator-groupby',
  params:{
    fields: ['creator.id', 'creator.nickname'],
    groupBy: ['creator']
  }
})

const ownerCreatorIds = computed(() => {
  return ownerCreatorObjects.value?.map((c: any) => c.creator) || []
})

const { data: ownerCreators } = await useDirectusFetch<Creator[]>('/items/creator',{
  key: 'owner-creators',
  params:{
    fields: ['id', 'nickname'],
    filter: {
      id: {
        _in: ownerCreatorIds.value,
      },
    },
  }
})


// Initialize with all available IPs
selectedIps.value = ownerIps.value || []
selectedCreators.value = ownerCreators.value || []


const query = computed(() => ({
  startDate: toCalendarDate(startDate.value).toDate(getLocalTimeZone()),
  endDate: toCalendarDate(endDate.value).toDate(getLocalTimeZone()),
  ips: selectedIps.value.map(ip => ip.id),
}))

const { data: performanceData } = await useFetch<ApiResponse>('/api/owner/performance/charts',{
  query,
  key: JSON.stringify(query.value),
  watch: [startDate, endDate, selectedIps],
})

// X-axis formatters for both charts
const revenueXFormatter = (i: number): string => performanceData.value?.revenueChartData[i]?.date || ''
const quantityXFormatter = (i: number): string => performanceData.value?.quantityChartData[i]?.date || ''

// Create categories using useTranslatedName for locale reactivity
const categories = computed(() => {
  const cats: Record<string, { name: string; color: string }> = {}

  Object.keys(performanceData.value?.ipData || {}).forEach(ipKey => {
    const ipInfo = performanceData.value?.ipData[ipKey]
    if (ipInfo?.ip?.translations) {
      cats[ipKey] = {
        name: useTranslatedName(ipInfo.ip.translations).value,
        color: ipInfo.color
      }
    }
  })

  return cats
})

// Modal state for adding IPs
const isAddIpModalOpen = ref(false)
const isAddCreatorModalOpen = ref(false)

// Get available IPs that aren't already selected
const availableIps = computed(() => {
  if (!ownerIps.value) return []
  const selectedIpIds = selectedIps.value.map(ip => ip.id)
  return ownerIps.value.filter(ip => !selectedIpIds.includes(ip.id))
})

// Function to add IP to selected list
const addIpToSelection = (ip: Ip) => {
  selectedIps.value.push(ip)
  isAddIpModalOpen.value = false
}

// Get available IPs that aren't already selected
const availableCreators = computed(() => {
  if (!ownerCreators.value) return []
  const selectedIpIds = selectedCreators.value.map(creator => creator.id)
  return ownerCreators.value.filter(creator => !selectedIpIds.includes(creator.id))
})

// Function to add Creator to selected list
const addCreatorToSelection = (creator: Creator) => {
  selectedCreators.value.push(creator)
  isAddCreatorModalOpen.value = false
}

// Products Section
const creatorsStartDate = ref(today(getLocalTimeZone()).subtract({ months: 3 }))
const creatorsEndDate = ref(today(getLocalTimeZone()).add({ days: 1 })) // Tomorrow to avoid timezone issues

const queryProducts = computed(() => ({
  startDate: toCalendarDate(creatorsStartDate.value).toDate(getLocalTimeZone()),
  endDate: toCalendarDate(creatorsEndDate.value).toDate(getLocalTimeZone()),
  creators: selectedCreators.value.map(creator => creator.id),
}))

const { data: creatorSalesRevenueAndCount } = useFetch('/api/owner/performance/creators', {
  query: queryProducts,
  watch: [creatorsStartDate, creatorsEndDate, selectedCreators],
})

// Computed formatters for creator charts
const creatorXFormatter = computed(() => (i: number): string =>
  creatorSalesRevenueAndCount.value?.creatorsRevenue[i]?.creatorNickname || ''
)

// Categories for creator charts. creator nicknames are the keys. color should be randomized
const creatorRevenueDataCategories = computed(() => ({
  total: {
    name: t('global.total'),
    color: '#3b82f6',
  },
}))
const creatorCountDataCategories = computed(() => ({
  count: {
    name: t('global.quantity'),
    color: '#FF928A',
  },
}))

const xFormatter = (i: number): string => `${creatorSalesRevenueAndCount.value?.creatorsRevenue[i]?.creatorNickname ?? ''}`

</script>

<template>
<UPage>
  <UPageBody>
      <h1 class="text-2xl font-bold">{{ $t('global.performance_report') }}</h1>
      <section id="ip-charts" class="-mt-8 flex flex-col gap-4">
        <h2 class="text-xl font-bold">{{ $t('global.ip') }}</h2>

        <!-- Period Selector -->
        <div id="date-range" class="flex gap-4 items-center">
          <p class="text-xs w-10">{{ $t('global.period') }}</p>
          <UIDateRangePicker v-model:start-date="startDate" v-model:end-date="endDate" />
        </div>

        <!-- IP selector -->
        <div id="ip-selection" class="flex gap-4 items-center">
          <p class="text-xs w-24">{{ $t('global.ip_s') }}</p>
          <div class="flex gap-2 flex-wrap" v-auto-animate>
            <UBadge 
              @click="() => selectedIps = selectedIps.filter(i => i.id !== ip.id)" 
              v-for="ip in selectedIps" 
              :key="ip.id" 
              class="cursor-pointer"
              color="neutral" 
              trailing-icon="i-heroicons-x-mark" 
              variant="outline">{{ useTranslatedName(ip.translations) }}</UBadge>
            <!-- Add IP Modal -->
            <UModal v-model:open="isAddIpModalOpen" title="Add IP" :close="true">
              <UButton
                v-if="availableIps.length > 0"
                size="sm"
                variant="outline"
                color="neutral"
                class="rounded-full"
                @click="isAddIpModalOpen = true"
              >
                +
              </UButton>
              <template #content>
                <div class="flex flex-col gap-4 p-4 max-h-96">
                  <p class="text-sm text-neutral-400">{{ $t('global.please_select_an_ip') }}</p>
                  <div class="grid gap-2 overflow-y-auto">
                    <UButton
                      v-for="ip in availableIps"
                      :key="ip.id"
                      variant="ghost"
                      color="neutral"
                      class="justify-start p-3 text-left"
                      @click="addIpToSelection(ip)"
                    >
                      {{ useTranslatedName(ip.translations) }}
                    </UButton>
                  </div>
                </div>
              </template>
            </UModal>
          </div>
        </div>

        <!-- Two charts side by side -->
        <div class="grid grid-cols-2 gap-8">
          <!-- Revenue Chart -->
          <div class=" border border-neutral-500 bg-[#22202C] rounded-xl grid gap-2 text-center p-4 w-full">
            <h3 class="text-lg font-semibold">{{ $t('global.ip_monthly_revenue_summary') }}</h3>
            <LineChart :data="performanceData?.revenueChartData || []" :curve-type="CurveType.Linear" :categories="categories"
              :x-formatter="revenueXFormatter" :y-grid-line="true" :y-num-ticks="6" :x-num-ticks="12"
              :legend-position="LegendPosition.Bottom" :height="250" />
          </div>

          <!-- Quantity Chart -->
          <div class=" border border-neutral-500 bg-[#22202C] rounded-xl grid gap-2 text-center p-4 w-full">
            <h3 class="text-lg font-semibold">{{ $t('global.ip_monthly_sale_count_summary') }}</h3>
            <LineChart :data="performanceData?.quantityChartData || []" :curve-type="CurveType.Linear" :categories="categories"
              :x-formatter="quantityXFormatter" :y-grid-line="true" :y-num-ticks="6" :x-num-ticks="12"
              :height="250" />
          </div>
        </div>
      </section>

      <section  class="flex flex-col gap-4">
        <!-- Period Selector -->
        <h2 class="text-xl font-bold">{{ $t('global.creator') }}</h2>
        <div id="date-range" class="flex gap-4 items-center">
          <p class="text-xs w-20">{{ $t('global.period') }}</p>
          <UIDateRangePicker v-model:start-date="creatorsStartDate" v-model:end-date="creatorsEndDate" />
        </div>

        <!-- Creator selector -->
        <div id="ip-selection" class="flex gap-4 items-center">
          <p class="text-xs w-24">{{ $t('global.creators') }}</p>
          <div class="flex gap-2 flex-wrap" v-auto-animate>
            <UBadge 
              v-for="creator in selectedCreators" 
              @click="() => selectedCreators = selectedCreators.filter(i => i.id !== creator.id)" 
              :key="creator.id" 
              class="cursor-pointer"
              color="neutral" 
              trailing-icon="i-heroicons-x-mark" 
              variant="outline">{{ creator.nickname }}</UBadge>
            <!-- Add Creaetor Modal -->
            <UModal v-model:open="isAddCreatorModalOpen" title="Add Creator" :close="true">
              <UButton
                v-if="availableCreators.length > 0"
                size="sm"
                variant="outline"
                color="neutral"
                class="rounded-full"
                @click="isAddCreatorModalOpen = true"
              >
                +
              </UButton>
              <template #content>
                <div class="flex flex-col gap-4 p-4 max-h-96">
                  <p class="text-sm text-neutral-400">{{ $t('global.please_select_an_ip') }}</p>
                  <div class="grid gap-2 overflow-y-auto">
                    <UButton
                      v-for="creator in availableCreators"
                      :key="creator.id"
                      variant="ghost"
                      color="neutral"
                      class="justify-start p-3 text-left"
                      @click="addCreatorToSelection(creator)"
                    >
                      #{{creator.id }} : {{ creator.nickname }}
                    </UButton>
                  </div>
                </div>
              </template>
            </UModal>
          </div>
        </div>


        <div class="grid grid-cols-2 gap-8">
          <!-- Revenue Chart -->
          <div class=" border border-neutral-500 bg-[#22202C] rounded-xl grid gap-2 text-center p-4 w-full">
            <h3 class="text-lg font-semibold">{{ $t('global.creator_revenue_comparison') }}</h3>
             <BarChart
              :data="creatorSalesRevenueAndCount?.creatorsRevenue ?? [{}]"
              :height="300"
              :categories="creatorRevenueDataCategories"
              :y-axis="['total']"
              :y-grid-line="true"
              :x-formatter="xFormatter"
              :legend-position="LegendPosition.Bottom"
              :hide-legend="false"
            />
          </div>

          <!-- Quantity Chart -->
          <div class=" border border-neutral-500 bg-[#22202C] rounded-xl grid gap-2 text-center p-4 w-full">
            <h3 class="text-lg font-semibold">{{ $t('global.creator_sales_count_comparison') }}</h3>
              <BarChart
                :data="creatorSalesRevenueAndCount?.creatorsRevenue ?? [{}]"
                :height="300"
                :categories="creatorCountDataCategories"
                :y-axis="['count']"
                :y-grid-line="true"
                :x-formatter="xFormatter"
                :legend-position="LegendPosition.Bottom"
                :hide-legend="false"
              />
          </div>
        </div>
      </section>
  </UPageBody>
</UPage>
</template>