<template>
  <UPage>
    <template #left>
      <FanLeftMenu class="w-full min-w-max" />
    </template>
    <UPageBody>
      <FanTopBarFilterMenu class="mb-4 lg:mb-6"/>
      <FanProductResultList />
    </UPageBody>
  </UPage>
</template>

<script setup lang="ts">
const { filter, filterApplied } = useFanFilter();
const router = useRoute();

definePageMeta({
  title: 'pages.fan.products.title',
});

// Initialize filter from URL query parameters on page load
const query = router.query;
if (Object.keys(query).length > 0) {
  filter.value = {
    productName: (query.keyword as string) || '',
    status: (query.status as any) || '',
    availability: (query.availability as any) || '',
    productCategory: (query.productCategory as any) || '',
    minPrice: (query.minPrice as string) || '',
    maxPrice: (query.maxPrice as string) || '',
    ipCategory: (query.ipCategory as string) || '',
    searchValue: (query.searchValue as string) || '',
    page: parseInt((query.page as string) || '1'),
    sortBy: (query.sortBy as string) || 'latest',
    creator: {
      label: '',
      value: (query.creatorId as string) || '',
    },
  };
}

// apply the filter as query param to the url
watch(
  filter,
  () => {
    if (!filterApplied) {
      navigateTo({ query: {} });
    } else {
      // Build query object with only non-empty values
      const query: Record<string, string | number> = {};

      if (filter.value.productName) query.keyword = filter.value.productName;
      if (filter.value.status) query.status = filter.value.status;
      if (filter.value.availability) query.availability = filter.value.availability;
      if (filter.value.productCategory) query.productCategory = filter.value.productCategory;
      if (filter.value.minPrice) query.minPrice = filter.value.minPrice;
      if (filter.value.maxPrice) query.maxPrice = filter.value.maxPrice;
      if (filter.value.ipCategory) query.ipCategory = filter.value.ipCategory;
      if (filter.value.creator.value) query.creatorId = filter.value.creator.value;
      if (filter.value.searchValue) query.searchValue = filter.value.searchValue;
      if (filter.value.page !== 1) query.page = filter.value.page;
      if (filter.value.sortBy !== 'latest') query.sortBy = filter.value.sortBy;

      navigateTo({ query });
    }
  },
  { deep: true },
);
</script>
