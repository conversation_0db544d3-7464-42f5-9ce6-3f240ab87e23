<template>
  <UPage>
    <UPageBody>
      <!-- Main top section -->
      <div class="grid grid-cols-5 gap-8">
        <!-- Left: Product Gallery Section -->
        <div id="product-gallery" class="col-span-5 flex flex-col-reverse gap-4 lg:col-span-3 md:flex-row">
          <div class="grid grid-cols-5 md:grid-rows-5 md:grid-cols-1 gap-4 xl:w-32 gap 2xl:w-42">
          <!-- <div class="flex lg:w-20 xl:w-32 flex-shrink-0 flex-row gap-4 lg:flex-col"> -->
            <NuxtImg
              v-for="(image, index) in product?.images as ProductsFiles[]"
              :key="image.id"
              @click="selectedImageIndex = index"
              :src="directusAssetsUrl(image.directus_files_id as string, 200, 200) || '/images/missing-product.png'"
              :alt="`Product image ${index + 1}`"
              class="aspect-square w-full cursor-pointer rounded-lg object-cover"
              :class="selectedImageIndex === index ? 'ring-primary ring-2' : ''"
              placeholder
              placeholder-class="blur-xs"
            />
          </div>

          <!-- Main Image Section - Fixed container -->
          <div class="relative w-full overflow-hidden">
            <NuxtImg
              :src="selectedImage"
              :alt="translatedProductName || $t('global.preview_image')"
              class="w-full cursor-pointer rounded-lg object-cover aspect-square h-full md:h-[32rem] lg:h-full"
              @click="
                lightboxVisible = true;
                imageIndexRef = selectedImageIndex;
              "
            />
            <VueEasyLightbox
              :visible="lightboxVisible"
              :imgs="allImages"
              :index="imageIndexRef"
              @hide="lightboxVisible = false"
            />
          </div>
        </div>

        <!-- Right:Product Details Section (placeholder for future content) -->
        <div class="col-span-5 flex w-full flex-col gap-4 lg:col-span-2">
          <!-- Product Category and Name -->
          <UBadge id="product-category" variant="outline" color="neutral" class="w-max">{{
            useTranslatedName((product?.category as ProductCategories).translations)
          }}</UBadge>

          <!-- Product Name -->
          <h1 id="product-name" class="text-3xl font-bold">{{ translatedProductName }}</h1>

          <!-- Pricing with optional discount -->
          <div class="flex items-baseline gap-1">
            <template v-if="hasDiscount">
              <p class="text-primary font-primary text-xl">
                {{ formattedDiscountedPrice }}
              </p>
              <p class="text-xs text-neutral-400 line-through">
                {{ formattedCurrentPrice }}
              </p>
            </template>
            <template v-else>
              <p class="text-primary font-primary text-xl font-bold">
                {{ formattedCurrentPrice }}
              </p>
            </template>
          </div>

          <!-- Star Rating Component -->
          <div class="flex flex-row items-center gap-2">
            <vue3-star-ratings
              v-model="productRating"
              :starColor="isRatingSubmitted ? 'var(--ui-primary)' : 'white'"
              class="h-6"
              disable-click
            />
            <p class="text-sm text-neutral-500">({{ averageRating }})</p>
            <p id="review-count" v-if="(product as Products)?.reviews.length > 0">
              {{ product?.reviews.length }} {{ $t('global.reviews') }}
            </p>
          </div>

          <!-- Creator Avatar - Name -->
          <NuxtLinkLocale :to="{ name: 'creators-id', params: { id: (product?.creator as Creator)?.id } }">
            <div class="flex flex-row items-center gap-2">
              <UAvatar
                size="xs"
                :src="
                  directusAssetsUrl((product?.creator as Creator)?.avatar as string) ?? '/images/missing-product.png'
                "
              />
              <p class="text-sm">{{ (product?.creator as Creator).nickname ?? 'Creator' }}</p>
            </div>
          </NuxtLinkLocale>

          <!-- Product Description -->
          <p id="product-description" class="line-clamp-5 h-36 text-sm text-neutral-400">
            {{ translatedProductDescription }}
          </p>

          <!-- CTA Buttons -->
          <div class="grid w-full grid-cols-2 gap-4">
            <!-- Add to Cart Button -->
            <UButton
              :disabled="isOutOfStock"
              :color="isOutOfStock ? 'secondary' : 'primary'"
              size="lg"
              variant="outline"
              class="text w-full justify-center rounded-full"
              @click="addToCart(product!)"
            >
              {{ $t('global.add_to_cart') }}
            </UButton>
            <CheckoutModal
              v-if="profile"
              ref="checkoutModal"
              :items="checkoutItems"
              :title="$t('global.order_details')"
              @success="handleOrderSuccess"
              @error="handleOrderError"
            >
              <UButton
                :disabled="isOutOfStock"
                :color="isOutOfStock ? 'secondary' : 'primary'"
                size="lg"
                class="w-full justify-center rounded-full"
                @click="checkoutModal?.open()"
              >
                {{ isOutOfStock ? $t('global.out_of_stock') : $t('global.order_now') }}
              </UButton>
            </CheckoutModal>
            <UButton
              v-else
              :disabled="isOutOfStock"
              :color="isOutOfStock ? 'secondary' : 'primary'"
              size="lg"
              class="w-full justify-center rounded-full"
              @click="loginToCheckout()"
            >
              {{ isOutOfStock ? $t('global.out_of_stock') : $t('global.order_now') }}
            </UButton>  
          </div>

          <!-- Others sections -->
          <div id="others" class="flex flex-row items-center">
            <!-- Favorite button -->
            <ClientOnly>
              <UButton
                v-if="!isLikedProduct"
                icon="i-heroicons-heart"
                size="lg"
                variant="ghost"
                @click="toggleFavorite()"
              />
              <UButton v-else icon="i-heroicons-heart-solid" size="lg" variant="ghost" @click="toggleFavorite()" />
              <template #fallback>
                <UButton icon="i-heroicons-heart" size="lg" variant="ghost" />
              </template>
            </ClientOnly>
            <!-- Share button -->
            <ShareButton />

            <!-- Remaining stock -->
            <p class="ml-auto text-sm text-neutral-500">
              {{ $t('global.remaining_stock') }}: {{ product?.stock_remaining ?? 0 }}
            </p>
          </div>
        </div>
      </div>

      <!-- Bottom Tabs : Details, Reviews, related products ect2 -->
      <section id="product-tabs" class="grid gap-2">
        <div id="product-tabs" class="flex flex-row gap-8">
          <button
            class="cursor-pointer text-2xl font-bold"
            :class="activeTab === 'details' ? 'text-primary' : ''"
            @click="activeTab = 'details'"
          >
            {{ $t('global.details') }}
          </button>
          <button
            class="cursor-pointer text-2xl font-bold"
            :class="activeTab === 'reviews' ? 'text-primary' : ''"
            @click="activeTab = 'reviews'"
          >
            {{ $t('global.reviews') }}
          </button>
          <!-- <UButton>{{ $t('global.discussions') }}</UButton> -->
        </div>

        <!-- Bottom Display Area -->
        <div id="product-tab-display-area" class="min-h-28 grid grid-cols-12 gap-4">
          <!-- Details tab -->
          <div v-if="activeTab === 'details'" id="product-details" class="col-span-8">
            <p class="prose whitespace-pre-line text-neutral-400">
              {{ translatedProductDescription }}
            </p>
          </div>
          <!-- Reviews tab -->
          <div v-if="activeTab === 'reviews'" id="product-reviews" class="col-span-8  max-h-96 overflow-x-scroll">
            <ReviewCard v-if="(product as Products)?.reviews?.length > 0" v-for="review in product?.reviews" :review />
            <p v-else>{{ $t('global.no_reviews_available') }}</p>
          </div>
          <!-- Review Bars -->
          <div id="review-bars" class="col-span-4 flex flex-col gap-1 text-right">
            <div class="flex flex-row items-center gap-2 mb-2 ">
              <vue3-star-ratings
                v-model="productRating"
                :starColor="isRatingSubmitted ? 'var(--ui-primary)' : 'white'"
                class="h-6"
                disable-click
              />
              <p v-if="averageRating != 0" class="text-2xl font-bold ml-auto">{{ averageRating }}</p>
            </div>
          
            <div v-for="rating in [5, 4, 3, 2, 1]" :key="rating" class="flex gap-2">
              <div class="text-muted">{{ rating }}</div>
              <USlider
                size="lg"
                class="col-span-7"
                :default-value="aggregatedRatings[rating].percentage"
                disabled
                :ui="{'thumb':'hidden'}"
              />
            <div class="font-semibold w-12">{{ aggregatedRatings[rating].count }}</div>
          </div>

          </div>
        </div>

      </section>

      <!-- Similar Products Section -->
      <section id="similar-products" class="grid gap-4">
        <h3 class="text-2xl font-bold">{{ $t('global.similar_products') }}</h3>

        <!-- Products from same category and creator -->
        <div class="no-scrollbar flex gap-4 overflow-x-auto">
          <LazyVerticalProductCard v-for="product in categoryProducts" :product class="max-h-96 min-w-2xs" />
        </div>
      </section>
    </UPageBody>
  </UPage>
</template>

<script lang="ts" setup>
import vue3StarRatings from 'vue3-star-ratings';

const toast = useToast();
const route = useRoute('products-id___en');
const { useDirectusFetch, directusAssetsUrl } = useDirectus();
const { t, locale } = useI18n();
const { addItem, cart, removeItems } = useCart();
const { profile, leftOffUrl } = useUser();
const { addToFavorites, removeFromFavorites } = useFavorites();
const localePath = useLocalePath();
const { formatProductPrice, formatProductDiscountPrice, productHasDiscount, getProductPrice } = useCurrency();

// Liked product
const { data: favCheck, execute } = useFetch('/api/favourites/check', {
  method: 'POST',
  body: { productId: route.params.id },
  immediate: false,
});
const isLikedProduct = computed(() => favCheck.value?.id);

if (import.meta.client && profile.value) {
  execute();
}

const toggleFavorite = async () => {
  if (!profile.value) {
    leftOffUrl.value = route.fullPath;
    navigateTo(localePath('login'));
    return;
  }
  if (isLikedProduct.value) {
    await removeFromFavorites(Number(route.params.id));
    execute();
  } else {
    await addToFavorites(Number(route.params.id));
    execute();
  }
};

const loginToCheckout = () => {
  leftOffUrl.value = route.fullPath;
  navigateTo(localePath('login'));
}

// Fetch product data
const { data: product } = await useDirectusFetch<Products>(`/items/products/${route.params.id}`, {
  key: `product-${route.params.id}`,
  params: {
    fields: ['*.*', 'translations.*', 'category.translations.*', 'images.*', 'reviews', 'main_image', 'reviews.user_created.avatar', 'reviews.user_created.nickname','reviews.user_created.first_name','reviews.user_created.last_name'],
  },
});

if (!product.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Product not found',
  });
}

const isOutOfStock = computed(() => (product.value ? (product.value.stock_remaining ?? 0) <= 0 : true));

const lightboxVisible = ref(false);

// Reactive product name for SEO and display
const translatedProductName = useTranslatedName(product.value!.translations);
const translatedProductDescription = useTranslatedDescription(product.value!.translations);

// Computed Aggregated Ratings
// Parse ratings to numbers since they come as strings from the database
const aggregatedRatings = calculateAggregatedRatings(
  product?.value.reviews
    .map((review: any) => parseFloat(review.rating))
    .filter((rating: number) => !isNaN(rating)) || []
);

// Computed Average Ratings
const averageRating = computed(() => {
  if (!product.value?.reviews || product.value.reviews.length === 0) return 0;
  // Parse ratings to numbers to ensure proper numeric addition (ratings come as strings from DB)
  const totalRating = product.value.reviews.reduce((acc, review: any) => {
    const rating = parseFloat(review.rating) || 0;
    return acc + rating;
  }, 0);
  return Math.round((totalRating / product.value.reviews.length) * 10) / 10; // Round to 1 decimal place
});

// Rating state management
const productRating = ref(averageRating.value);
const isRatingSubmitted = ref(false);

// Gallery state management
const selectedImageIndex = ref(0);
const imageIndexRef = ref(0);

// Computed property for the currently selected image
const selectedImage = computed(() => {
  if (!product.value?.images || product.value.images.length === 0) {
    return '/images/missing-product.png';
  }

  const selectedImg = product.value.images[selectedImageIndex.value];
  return directusAssetsUrl(selectedImg.directus_files_id as string, 900, 900) ?? '/images/missing-product.png';
});

const allImages = computed(() => {
  return product.value?.images.map((img: any) => directusAssetsUrl(img.directus_files_id as string)) ?? [];
});

// Use currency composable for pricing logic with locale-based pricing
const hasDiscount = computed(() => (product.value ? productHasDiscount(product.value) : false));
const formattedCurrentPrice = computed(() => (product.value ? formatProductPrice(product.value) : ''));
const formattedDiscountedPrice = computed(() => (product.value ? formatProductDiscountPrice(product.value) : ''));

// Tabs
const activeTab = ref('details');

// Similar products
const { data: categoryProducts } = useDirectusFetch<Products[]>('/items/products', {
  key: `similar-products-${product.value?.category}`,
  params: {
    fields: [
      'id, translations.*, price, main_image, category.code, price_jpy, discount, category.translations.*, creator.*',
      'creator.nickname, creator.id',
    ],
    filter: {
      category: {
        _eq: (product.value?.category as ProductCategories)?.id,
      },
      id: {
        _neq: product.value?.id,
      },
    },
    limit: 15,
  },
});

// CTAs - keeping minimal variables for compatibility

// CheckoutModal ref and data
const checkoutModal = ref();

// Computed property for checkout items
const checkoutItems = computed(() => {
  if (!product.value) return [];

  return [{
    id: product.value.id,
    name: translatedProductName.value,
    price: getProductPrice(product.value),
    quantity: 1,
    image: product.value.images?.[0]?.directus_files_id || product.value.main_image || '',
  }];
});

// Handle checkout success and error
const handleOrderSuccess = (response: any) => {
  // Remove the ordered items from the cart if they exist there
  if (response.orderedItemIds && Array.isArray(response.orderedItemIds)) {
    removeItems(response.orderedItemIds);
  }

  console.log('Order success, items removed from cart if they existed:', response);
};

const handleOrderError = (error: any) => {
  // Order error is already handled in the CheckoutModal component
  console.error('Order error:', error);
};

const addToCart = (product: Products) => {
  // Check stock availability before adding to cart
  if (product.stock_remaining !== null && product.stock_remaining !== undefined) {
    const currentCartQuantity = cart.value[product.id] || 0;
    if (currentCartQuantity >= product.stock_remaining) {
      toast.add({
        title: t('global.insufficient_stock'),
        description: t('global.max_stock_available', { stock: product.stock_remaining }),
        color: 'error',
      });
      return;
    }
  }

  addItem(product, 1);
  toast.add({ title: t('global.product_added_cart'), color: 'success' });
};

// Old ordering code removed - now handled by CheckoutModal component

// SEO meta
useSeoMeta({
  title: () => translatedProductName.value,
  description: () => translatedProductDescription.value,
  ogTitle: () => translatedProductName.value,
  ogDescription: () => translatedProductDescription.value,
  ogImage: () => selectedImage.value,
});
</script>
