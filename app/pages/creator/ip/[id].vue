<template>
  <UPage>
    <!-- Full width banner with IP image and details overlay -->
    <div class="relative h-[55vh] w-full overflow-hidden lg:h-[65vh] xl:h-[75vh]" transition-style="in:circle:top-right">
      <NuxtImg
        v-if="ip?.main_image"
        :src="directusAssetsUrl(ip.main_image as string) ?? undefined"
        class="h-full w-full object-cover object-top blur-none transition-all duration-100"
        :alt="ipName"
        loading="eager"
        placeholder
        placeholder-class="blur-xs"
      />
      <div v-else class="h-full w-full bg-gradient-to-br from-neutral-800 to-neutral-900"></div>

      <!-- Gradient overlay that fades to background color -->
      <div class="absolute inset-0 bg-gradient-to-t from-[#1B1A21] via-transparent to-transparent"></div>

      <!-- Details overlay at bottom -->
      <div class="absolute bottom-0 left-0 w-full p-4 md:p-10">
        <div class="mx-auto flex max-w-[92rem] flex-col gap-2">
          <!-- IP Category Badge -->
          <UBadge v-if="ip?.category" variant="outline" color="secondary" :ui="{ base: 'text-neutral' }" class="w-max">
            {{ useTranslatedName((ip.category as IpCategories).translations) }}
          </UBadge>

          <!-- IP Name -->
          <h1 class="text-2xl font-bold text-white md:text-3xl lg:text-4xl">
            {{ ipName }}
          </h1>

          <!-- IP Description -->
          <p class="md:text-md line-clamp-3 max-w-3xl text-sm text-neutral-200" transition-style="in:wipe:right">
            {{ ipDescription }}
          </p>
        </div>
      </div>
    </div>

    <UContainer>
      <UPageBody>
        <!-- Products section -->
        <section id="products" class="grid gap-6">
          <div class="flex">
            <div class="flex w-full items-center gap-2">
              <h2 class="text-2xl font-bold">{{ $t('global.my_licensed_products') }}</h2>
              <!-- <span class="text-sm text-neutral-500"
                >({{ filteredProducts.length }}{{ productSearchQuery ? ` / ${products?.length || 0}` : '' }})</span
              > -->
              <NuxtLinkLocale
                :to="{ name: 'creator-my-products', query: { ipCategory: (ip?.category as IpCategories)?.code } }"
                class="text-sm text-neutral-400"
              >
                >> {{ $t('global.view_more') }}
              </NuxtLinkLocale>

              <!-- Keyword Search for products -->
              <div class="flex flex-col-reverse md:flex-row gap-2 ml-auto">
                <UInput
                  :model-value="productSearchQuery"
                  @input="debouncedSearchInput"
                  :placeholder="$t('global.search_product')"
                  icon="i-lucide-search"
                  class="w-48"
                />

                <!-- New product application -->
                <USlideover v-model:open="isCreateProductOpen" >
                  <UButton
                    class="px-6 justify-center rounded-full"
                  >
                    {{ $t('global.add_new_product') }}
                  </UButton>

                  <template #content>
                    <CreatorNewProductApplicationForm class="w-full p-8" @close="handleSubmitComplete"/>
                  </template>
                </USlideover>
              </div>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5" v-auto-animate>
            <LazyVerticalProductCard
              v-for="product in filteredProducts"
              :key="product.id"
              mode="creator"
              :product="product"
              class="max-h-[460px]"
            />
          </div>

          <!-- No results message -->
          <div v-if="productSearchQuery && filteredProducts.length === 0" class="py-8 text-center">
            <p class="text-neutral-500">{{ $t('global.no_products_found') }}</p>
          </div>
        </section>
      </UPageBody>
    </UContainer>
  </UPage>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'creator-fullwidth-header',
  title: 'pages.ip.details.title',
  middleware: 'creator-auth',
});

const route = useRoute('creator-ip-id___en');
const { t } = useI18n();
const { useDirectusFetch, directusAssetsUrl } = useDirectus();
const { profile } = useUser();

// Search functionality for products
const productSearchQuery = ref('');
const isCreateProductOpen = ref(false);

// Debounced search input handler
const debouncedSearchInput = useDebounceFn(
  (e: any) => {
    productSearchQuery.value = e.target.value;
  },
  500,
  { maxWait: 3000 },
);


// Computed property for filtered products based on search query
const filteredProducts = computed(() => {
  if (!products.value) return [];

  if (!productSearchQuery.value.trim()) {
    return products.value;
  }

  const searchTerm = productSearchQuery.value.toLowerCase().trim();

  return products.value.filter((product) => {
    // Search in product name (translations)
    const productName = useTranslatedName(product.translations).value.toLowerCase();
    if (productName.includes(searchTerm)) return true;

    // Search in product description (translations)
    const productDescription = useTranslatedDescription(product.translations).value.toLowerCase();
    if (productDescription.includes(searchTerm)) return true;

    // Search in creator nickname
    const creatorName = (product.creator as Creator)?.nickname?.toLowerCase() || '';
    if (creatorName.includes(searchTerm)) return true;

    return false;
  });
});

// Fetch IP data with all related information
const { data: ip } = await useDirectusFetch<Ip>(`/items/ip/${route.params.id}`, {
  key: `ip-${route.params.id}`,
  params: {
    fields: [
      'id',
      'translations.*',
      'main_image',
      'images.directus_files_id',
      'category.translations.*',
      'owner.name',
      'owner.company_name',
      'owner.user.first_name',
      'owner.user.last_name',
    ],
  },
});

// Get translated IP name and description
const ipName = computed(() => {
  if (!ip.value?.translations) return '';
  return useTranslatedName(ip.value.translations).value;
});

const ipDescription = computed(() => {
  if (!ip.value?.translations) return '';
  return useTranslatedDescription(ip.value.translations).value;
});

// Fetch products for this IP
const { data: products, refresh} = await useDirectusFetch<Products[]>('/items/products', {
  key: `ip-products-${route.params.id}`,
  params: {
    fields: [
      'id',
      'status',
      'translations.*',
      'price',
      'price_jpy',
      'price_krw',
      'base_currency',
      'discount',
      'main_image',
      'category.translations.*',
      'creator.nickname',
      'creator.*',
      'user_created.avatar',
    ],
    filter: {
      _and: [
        {
          ip: {
            _eq: route.params.id,
          },
        },
        {
          creator: {
            user: {
              _eq: profile.value?.id,
            },
          },
        },
      ]
    },
    sort: ['-date_created'],
  },
});

const handleSubmitComplete = () => {
  isCreateProductOpen.value = false;
  refresh();
}

// SEO and page meta
useSeoMeta({
  title: () => ipName.value,
  description: () => ipDescription.value,
  ogTitle: () => ipName.value,
  ogDescription: () => ipDescription.value,
  ogImage: () => directusAssetsUrl(ip.value?.main_image as string) ?? undefined,
});
</script>