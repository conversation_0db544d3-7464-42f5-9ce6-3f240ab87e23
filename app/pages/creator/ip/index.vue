<template>
 <UPage>
    <UPageBody v-auto-animate>
      <div class="flex flex-col gap-4">
        <h1 class="text-2xl font-bold">{{ $t('global.available_ip') }}</h1>
        <HeroCarousel mode="creator" class="-mt-3 md:mt-0" transition-style="in:circle:top-left"/>

        <div class="flex gap-4">
          <!-- sort filter -->
          <USelect
            v-model="filter.sortBy"
            :items="sortItems"
            class="w-48 cursor-pointer"
            icon="i-lucide-sort-desc"
            :placeholder="$t('global.sort_by')"
          />

          <!-- keyword filter -->
          <UInput
            :model-value="filter.productName"
            @input="debouncedSearchInput"
            :placeholder="$t('global.search_product')"
            icon="i-lucide-search"
            class="ml-auto"
          />
        </div>

        <IpCategoriesFilter v-model="selectedIpCategory" transition-style="in:wipe:right"/>
        <!-- IP Item Cards -->
        <div class="grid gap-4 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4" v-auto-animate>
          <IPCard :ip="ip" v-for="ip in ips" :key="ip.id" :locale="locale" mode="creator" />
        </div>
      </div>
    </UPageBody>
  </UPage>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'creator',
  title: 'pages.creator.ip.title',
  middleware: 'creator-auth',
})
const route = useRoute();
const { t } = useI18n();
const { locale } = useI18n();
const { useDirectusFetch } = useDirectus();

const filter = ref({
  productName: '', //keyword search
  status: '',
  page: 1,
  sortBy: 'latest',
})

// Add a watcher for searchKeyword with debounce
const debouncedSearchInput = useDebounceFn(
  (e: any) => {
    filter.value.productName = e.target.value;
  },
  500,
  { maxWait: 3000 },
);


// Initialize filter state from URL query parameters
const selectedIpCategory = ref(route.query.category as string);

const filterParams = computed(() => {
  const filters = [];
  
  // Category filter
  if (selectedIpCategory.value) {
    filters.push({
      category: {
        code: {
          _eq: selectedIpCategory.value,
        },
      },
    });
  }
  
  // Search by name filter
  if (filter.value.productName) {
    filters.push({
      translations: {
        name: {
          _icontains: filter.value.productName,
        },
      },
    });
  }

  return {
    fields: ['id, translations.*, images.directus_files_id, category.translations.*','main_image'],
    filter: filters.length > 0 ? { _and: filters } : {},
    sort: filter.value.sortBy === 'latest' 
      ? ['-date_created'] 
      : filter.value.sortBy === 'oldest'
        ? ['date_created']
        : filter.value.sortBy === 'price_low'
          ? ['price']
          : filter.value.sortBy === 'price_high'
            ? ['-price']
            : ['-date_created'],
  };
});

const { data: ips } = useDirectusFetch<Ip[]>('/items/ip', {
  key: 'all-ips',
  watch: [selectedIpCategory],
  params: filterParams,
});

const sortItems = ref([
  {
    label: t('global.newest'),
    value: 'latest',
  },
  {
    label: t('global.oldest'),
    value: 'oldest',
  }
]);
</script>
