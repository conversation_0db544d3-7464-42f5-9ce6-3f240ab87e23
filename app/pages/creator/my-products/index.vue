<template>
  <UPage>
    <UPageBody v-auto-animate>
      <h2 class="text-3xl font-bold my-4">{{ $t('global.my_products') }}</h2>
      <div class="flex gap-4 my-4">
        <h3 @click="toggleMode" class="text-xl font-bold cursor-pointer" :class="displayMode === 'licensed-products' ? 'text-primary' : 'text-zinc-500'">{{ $t('global.licensed_products') }}</h3>
        <h3 @click="toggleMode" class="text-xl font-bold cursor-pointer" :class="displayMode !== 'licensed-products' ? 'text-primary' : 'text-zinc-500'">{{ $t('global.application_list') }}</h3>
      </div>
      <div class="flex gap-4">
        <!-- IP filter -->
        <USelectMenu
          v-model="selectedIpId"
          value-key="id"
          :items="localizedIpFilterItems"
          icon="i-lucide-layers"
          class="w-48 cursor-pointer"
          :placeholder="$t('global.filter_by_ip')"
        />
        <!-- select product category filter -->
        <USelectMenu
          v-model="selectedProductCategoryCode"
          value-key="code"
          :items="localzedProductCategoryItems"
          icon="i-lucide-tag"
          class="w-48 cursor-pointer"
          :placeholder="$t('global.filter_by_product_category')"
        />
        <!-- select status filter -->
        <USelect
          v-model="selectedStatus"
          :items="statusItems"
          class="w-48 cursor-pointer"
          icon="i-lucide-filter"
          :placeholder="$t('global.filter_by_status')"
        />
        <!-- Product Keyword Search Input -->
        <UInput
          :model-value="selectedProductName"
          @input="debouncedSearchInput"
          :placeholder="$t('global.search_product')"
          icon="i-lucide-search"
          class="ml-auto"
        />
      </div>
      <div v-if="displayMode === 'licensed-products'" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4 -mt-6"  v-auto-animate ref="el">
        <LazyVerticalProductCard
          mode="creator"
          v-for="product in myProducts"
          :key="product.id"
          :product="product"
          class="w-full max-h-[460px]"
        />
        <div v-if="myProducts?.length === 0" class="mt-8 w-full">
          <p class="text-center text-xl font-bold">{{ $t('global.no_products_available') }}</p>
        </div>
      </div>

      <div v-else class="-mt-10">
        <div id="product-application-list" class="" v-auto-animate>
          <div id="orders-headers" class="grid grid-cols-6 p-4 gap-4 text-sm text-neutral-500">
            <p>{{ $t('global.preview_image') }}</p>
            <p>{{ $t('global.product_name') }}</p>
            <p>{{ $t('global.ip') }}</p>
            <p>{{ $t('global.date_ordered') }}</p>
            <p>{{ $t('global.product_category') }}</p>
            <p>{{ $t('global.status') }}</p>
          </div>
          <NuxtLinkLocale
            :id="`item-${product.id}`"
            v-for="product in myProducts"
            :key="product.id"
            class="grid grid-cols-6 items-center rounded-lg border border-neutral-700 p-2 gap-4 mb-4"
            :to="{ name: 'creator-my-products-id', params: { id: product.id } }"
          >
            <div class="col-span-3 md:col-span-1">
              <NuxtImg
                :src="directusAssetsUrl(product?.main_image as string, 200, 200) ?? '/images/missing-product.png'"
                class="h-32 w-full rounded-lg object-cover"
                placeholder
                placeholder-class="blur-xs"
              />
            </div>
            <div class="col-span-3 md:col-span-1">
              <p>{{ useTranslatedName(product.translations) }}</p>
            </div>
            <div class="col-span-2 md:col-span-1 text-sm md:text-md">
              <p>{{ useTranslatedName((product.ip as Ip).translations) }}</p>
            </div>
            <div class="col-span-2 md:col-span-1 text-sm md:text-md">
              <p>{{ format(new Date(product.date_updated!), 'yyyy-MM-dd') }}</p>
            </div>
            <div class="col-span-2 md:col-span-1 text-sm md:text-md">
              <p>{{ useTranslatedName((product.category as ProductCategories).translations) }}</p>
            </div>
            <div class="col-span-2 md:col-span-1 text-sm md:text-md">
              <!-- Product status badge -->
              <UBadge
                class="justify-center px-4 capitalize"
                variant="subtle"
                :color="getStatusColor(product.status)"
                >{{ $t(`global.${product.status}`) }}</UBadge
              >
            </div>
          </NuxtLinkLocale>
          <div v-if="myProducts?.length === 0" class="mt-8 w-full">
            <p class="text-center text-xl font-bold">{{ $t('global.no_products_available') }}</p>
          </div>
        </div>
      </div>
    </UPageBody>
  </UPage>
</template>

<script setup lang="ts">
import { format } from 'date-fns';
import { getStatusColor } from '~~/shared/utils/ui';

definePageMeta({
  title: 'pages.creator.myProducts.title',
  layout: 'creator',
  middleware: 'creator-auth'
});

const { useDirectusFetch } = useDirectus();
const { profile } = useUser();
const { t } = useI18n();
const { directusAssetsUrl } = useDirectus();

const selectedProductName = ref('')
const selectedIpId = ref('')
const selectedStatus = ref('')
const selectedProductCategoryCode = ref('')
const displayMode = ref('licensed-products');

const toggleMode = () => {
  // Reset filters
  selectedProductName.value = '';
  selectedIpId.value = '';
  selectedStatus.value = '';
  selectedProductCategoryCode.value = '';

  displayMode.value = displayMode.value === 'licensed-products' ? 'application-list' : 'licensed-products';
}

const debouncedSearchInput = useDebounceFn(
  (e: any) => {
    selectedProductName.value = e.target.value;
  },
  500,
  { maxWait: 3000 },
);

const { data: profileData } = await useFetch('/api/me', {
  key: 'profile',
  params: {
    fields: ['*','creator_profile.*','role.name'],
  },
});
profile.value = profileData.value || null;


const filterParams = computed(()=> {
  const filters: any[] = [{ creator: { _eq: profile.value?.creator_profile[0].id } }];

  // Product name search filter
  if (selectedProductName.value !== '' && selectedProductName.value !== undefined) {
    filters.push({
      translations: {
        name: {
          _icontains: selectedProductName.value,
        },
      },
    });
  }

  // IP filter - skip if 'all' is selected
  if (selectedIpId.value !== '' && selectedIpId.value !== undefined && selectedIpId.value !== 'all') {
    filters.push({
      ip: {
        _eq: parseInt(selectedIpId.value), // Convert string back to number for API
      },
    });
  }

  // Product category filter - skip if 'all' is selected
  if (selectedProductCategoryCode.value !== '' && selectedProductCategoryCode.value !== undefined && selectedProductCategoryCode.value !== 'all') {
    filters.push({
      category: {
        code: {
          _eq: selectedProductCategoryCode.value,
        },
      },
    });
  }

  // Status filter
  if (selectedStatus.value) {
    filters.push({
      status: {
        _eq: selectedStatus.value,
      },
    });
  }

  return {
    fields: [
      '*',
      'translations.*',
      'category.code',
      'category.translations.*',
      'ip.translations.ip_id',
      'ip.translations.name',
      'ip.translations.languages_id',
    ],
    sort: ['-date_updated'],
    filter: filters.length > 0 ? { _and: filters } : {},
  };
})

// Create a serializable key from filter values instead of the computed ref
const filterKey = computed(() => {
  return `creator-products-${profile.value?.creator_profile[0].id}-${selectedProductName.value}-${selectedIpId.value}-${selectedProductCategoryCode.value}-${selectedStatus.value}`;
});

const { data: myProducts } = await useDirectusFetch<Products[]>(`/items/products`, {
  params: filterParams,
  key: filterKey,
  server: false
});

const creatorId = profile.value?.creator_profile[0].id;

// Get all product categories for this creator (returns category IDs)
const { data: creatorProductCategories } = await useFetch(`/api/products/categories?creatorId=${creatorId}`, {
  key: `creator-${creatorId}-product-categories`,
});

// Get all IPs for this creator (returns full IP objects with translations)
const { data: creatorIps } = await useFetch(`/api/creator/ips?creatorId=${creatorId}`, {
  key: `creator-${creatorId}-ips`,
});

// Status filter options
const statusItems = computed(() => [
  {
    label: t('global.all'),
    value: undefined,
  },
  {
    label: t('global.published'),
    value: 'published',
  },
  {
    label: t('global.pending'),
    value: 'pending',
  },
  {
    label: t('global.unpublished'),
    value: 'unpublished',
  },
  {
    label: t('global.rejected'),
    value: 'rejected',
  }
]);

// Create IP filter options from fetched data
const localizedIpFilterItems = computed(() => {
  const items = [
    {
      label: t('global.all'),
      id: 'all',
    }
  ];

  if (creatorIps.value && Array.isArray(creatorIps.value)) {
    creatorIps.value.forEach((ip: Ip) => {
      const ipName = useTranslatedName(ip.translations).value || `IP ${ip.id}`;
      items.push({
        label: ipName,
        id: String(ip.id), // Convert to string for consistency
      });
    });
  }

  return items;
});

// Create product category filter options from fetched data
const localzedProductCategoryItems = [
  {
    label: t('global.all'),
    code: 'all',
  },
  ...PRODUCT_CATEGORIES.map((product) => ({
    label: t(product.label),
    code: product.value,
  })),
];



</script>
