<template>
<UPage>
  <UPageBody>
    <!-- Mobile and Desktop Layout -->
    <div class="flex flex-col gap-6 lg:grid lg:min-h-[calc(100vh-12rem)] lg:grid-cols-12 lg:gap-8">
      <!-- Order Items Section -->
      <div class="lg:col-span-4 lg:flex lg:h-full lg:flex-col xl:col-span-3">
        <!-- Order Details Header with Status -->
        <div class="mb-6 flex items-center justify-between">
          <h1 id="order-detail" class="text-2xl font-bold">
            {{ $t('global.order_details') }}
          </h1>
          <!-- Order Status Badge -->
          <UBadge
            class="justify-center px-4 capitalize"
            variant="subtle"
            :color="getStatusColor(order?.status || '')"
          >
            {{ $t(`global.${orderStatusKey}`) }}
          </UBadge>
        </div>

        <!-- Item Counter and Navigation -->
        <div v-if="order?.order_items && order.order_items.length > 1" class="mb-4 flex items-center justify-between">
          <p class="text-sm text-neutral-400">
            {{ currentItemIndex + 1 }} {{ $t('global.of') }} {{ order.order_items.length }} {{ $t('global.items') }}
          </p>
          <div class="flex gap-2">
            <UButton @click="previousItem" icon="heroicons:chevron-left" size="sm" variant="soft" color="neutral" />
            <UButton @click="nextItem" icon="heroicons:chevron-right" size="sm" variant="soft" color="neutral" />
          </div>
        </div>

        <!-- Current Order Item Card -->
        <div v-if="currentItem" :id="`order-details-card-${currentItem.id}`"
          class="flex flex-col gap-4 rounded-lg border border-neutral-700 bg-neutral-900 p-4 transition-all duration-300 lg:flex-1">
          <NuxtLinkLocale :to="{ name: 'products-id', params: { id: currentItem.product.id } }" class="block">
            <NuxtImg :src="directusAssetsUrl(currentItem.product.main_image)"
              class="h-48 w-full cursor-pointer rounded-lg object-cover transition-all duration-200 hover:brightness-110"
              placeholder placeholder-class="blur-xs" />
          </NuxtLinkLocale>
          <h3 class="text-lg font-bold">{{ useTranslatedName(currentItem.product?.translations) }}</h3>
          <p class="line-clamp-3 text-sm text-neutral-400">
            {{ useTranslatedDescription(currentItem.product?.translations) }}
          </p>

          <div class="space-y-2">
            <div class="flex justify-between text-xs">
              <p>{{ $t('global.product_category') }}</p>
              <p>{{ useTranslatedName(currentItem.product?.category?.translations) }}</p>
            </div>

            <div class="flex justify-between text-xs">
              <p>{{ $t('global.creator') }}</p>
              <p>{{ currentItem.product?.creator?.nickname ?? 'Creator' }}</p>
            </div>

            <div class="flex justify-between text-xs">
              <p>{{ $t('global.date_ordered') }}</p>
              <p>{{ format(new Date(currentItem.date_created), 'yyyy-MM-dd') }}</p>
            </div>

            <div class="flex justify-between text-xs">
              <p>{{ $t('global.price') }}</p>
              <!-- Per unit price in product's base currency -->
              <p>{{ formatProductBaseCurrencyPrice(getProductBaseCurrencyUnitPrice()) }}</p>
            </div>

            <div class="flex justify-between text-xs">
              <p>{{ $t('global.quantity') }}</p>
              <p>{{ currentItem.quantity }}</p>
            </div>

            <!-- Subtotal(item x quantity) in product's base currency -->
            <div class="flex justify-between text-xs">
              <p>{{ $t('global.subtotal') }}</p>
              <p>{{ formatProductBaseCurrencyPrice(subtotal) }}</p>
            </div>

          </div>


          
          
          <!-- Bottom section with navigation and button -->
          <div class="mt-auto space-y-3 pt-4">
            <!-- Item Dots Navigation (for multiple items) -->
            <div v-if="order?.order_items && order.order_items.length > 1" class="flex justify-center gap-2">
              <button v-for="(item, index) in order.order_items" :key="item.id" @click="currentItemIndex = index"
              class="h-2 w-2 rounded-full transition-all duration-200 cursor-pointer"
              :class="index === currentItemIndex ? 'bg-primary-500' : 'bg-neutral-600 hover:bg-neutral-500'" />
            </div>

            <!-- Divider -->
            <div class="mt-8 border-t border-neutral-700"></div>

            <!-- Order Shipping Details -->
            <div class="p-3">
              <h4 class="mb-3 font-semibold text-sm">{{ $t('global.buyer_address') }}</h4>
              <div class="space-y-1 text-xs text-neutral-300">
                <p class="font-medium">{{ order?.shipping_name || 'N/A' }}</p>
                <p v-if="order?.shipping_address_line_1">{{ order.shipping_address_line_1 }}</p>
                <p v-if="order?.shipping_address_line_2">{{ order.shipping_address_line_2 }}</p>
                <p v-if="order?.shipping_city || order?.shipping_province || order?.shipping_postcode">
                  {{ [order?.shipping_city, order?.shipping_province, order?.shipping_postcode].filter(Boolean).join(', ') }}
                </p>
                <p v-if="order?.shipping_phone_number">{{ order.shipping_phone_number }}</p>
                <p v-if="order?.shipping_country" class="font-medium">{{ order.shipping_country }}</p>
              </div>
            </div>
            
            <!-- Total price of order (sum of subtotals) -->
            <div>
              <div class="flex justify-between font-semibold text-primary text-xl">
                <!-- <p>{{ $t('global.total') }}</p> -->
                <p>{{ formatProductBaseCurrencyPrice(orderTotal) }}</p>
              </div>
            </div>

            <!-- Complete Transaction Button -->
            <creator-shipped-order-modal :orderId="order!.id" @confirm="refresh">
              <UButton v-if="order?.status === 'processing'" color="primary"
               size="lg" class="w-full justify-center rounded-full">
               {{ $t('global.complete_transaction') }}
              </UButton>
            </creator-shipped-order-modal>
          </div>
        </div>
      </div>

      <!-- Chat Section -->
      <div class="lg:col-span-8 lg:flex lg:h-full lg:flex-col xl:col-span-9">
        <!-- Mobile Chat Toggle -->
        <div class="mb-4 lg:hidden">
          <UButton @click="isChatExpanded = !isChatExpanded" variant="outline" color="neutral"
            class="w-full justify-between"
            :trailing-icon="isChatExpanded ? 'heroicons:chevron-up' : 'heroicons:chevron-down'">
            {{ $t('global.chat_with_creator') }}
            <UBadge v-if="messages?.length" size="xs" color="primary">{{ messages.length }}</UBadge>
          </UButton>
        </div>

        <!-- Chat Container -->
        <div class="flex flex-col gap-4 sm:gap-6 lg:min-h-0 lg:flex-1" :class="{ 'hidden lg:flex': !isChatExpanded }">
          <!-- Messages Area (Scrollable) -->
          <div class="flex-1 space-y-4 overflow-y-auto lg:min-h-0">
            <div v-for="message in messages" :key="message.id">
              <!-- Timestamp -->
              <p class="text-xs text-neutral-500" :class="message.role === 'user' ? 'text-right' : 'ml-12'">
                {{ format(message.createdAt, 'yyyy-MM-dd HH:mm') }}
              </p>
              <!-- Chat Message -->
              <UChatMessage :message="message" :id="`${message.id}-${message.role}`" :role="message.role"
                :content="message.content" :createdAt="message.createdAt" :avatar="message.avatar" :side="message.side"
                :actions="message.actions" :variant="message.variant" :ui="{
                  actions: message.emoji ? '!opacity-100' : undefined,
                }">
                <template #actions>
                  <div v-auto-animate>
                    <!-- IF message.emoji, show that, else should show a list of emoji buttons. -->
  
                  <template v-if="!message.emoji">
                      <UButton v-if="message.role !== 'user'" v-for="emoji in emojis" :key="emoji.icon" :icon="emoji.icon" color="primary"
                        variant="ghost" @click="handleChangeReaction(message.id, emoji.icon)" />
                    </template>
                    <template v-else>
                      <div v-if="message.role !== 'user'" class="group relative ml-[6px] cursor-pointer text-xl"
                        @click="handleChangeReaction(message.id)">
                        <UIcon :name="message.emoji" class="transition-opacity group-hover:opacity-0" />
                        <UIcon name="heroicons:x-mark"
                          class="absolute inset-0 text-red-500 opacity-0 transition-opacity group-hover:opacity-100" />
                      </div>
                      <div v-else class="group relative ml-[6px] text-xl">
                        <UIcon :name="message.emoji" />
                      </div>
                    </template>
     
                  </div>
                </template>
                
              </UChatMessage>
            </div>
          </div>
          
          <!-- Shipped -->
          <div>
            <p v-if="order?.status === 'shipped'" class="text-center text-xl font-semibold">{{ $t('global.pending_buyer_confirmation') }}</p>
            <p v-if="order?.status === 'shipped'" class="text-center text-xs">{{ $t('global.creator_order_confirmation') }}</p>
          </div>
            
          <!-- Chat input (Fixed at bottom) -->
          <div class="flex-shrink-0" v-if="order?.status !== 'completed'">
            <UChatPrompt v-model="chatInput" @submit="handleSubmitChat" :placeholder="$t('global.message_to_creator')">
              <UChatPromptSubmit :status="status === 'pending' ? 'submitted' : 'ready'" />
            </UChatPrompt>
          </div>



          <!-- Show reviews for completed orders -->
          <div v-if="order?.status === 'completed'" class="w-96 mx-auto border border-zinc-700 bg-zinc-900 rounded-lg p-4 grid gap-2">
            <!-- User avatar + name -->
            <div class="flex flex-row gap-2">
              <UAvatar class="col-span-1 w-12 h-12" :src="directusAssetsUrl((order.user as any)?.avatar as string)" />
              <div class="flex flex-col justify-center">
                <h2 class="font-bold">{{ (order.user as any)?.nickname || `${profile?.first_name} ${profile?.last_name}` ||
                  'Anonymous' }}
                </h2>
              </div>
            </div>
            <div v-if="order?.reviews && order.reviews.length > 0" class="grid gap-2">
              <div v-for="review in order.reviews" :key="review.id" class="flex flex-row gap-2">
                <!-- <img :src="directusAssetsUrl((review.product as Products)?.main_image as string)" class="h-12 w-12 rounded-lg object-cover" /> -->
                <div>
                  <div class="flex flex-row gap-2">
                    <h4>{{ useTranslatedName((review.product as Products)?.translations || []).value }}</h4>
                    <div>
                      <vue3-star-ratings :modelValue="review.rating || 0" :starSize="16" starColor="#ff0055"
                        disableClick />
                    </div>
                  </div>
                  <p class="text-sm text-gray-600">{{ review.text }}</p>
                </div>
              </div>
            </div>
            <div v-else class="text-center text-gray-500">
              {{ $t('global.no_reviews_available') }}
            </div>
          </div>
          <p v-if="order?.status === 'completed'" class="text-center">{{ $t('global.order_is_complete') }}</p>
        </div>
      </div>
    </div>
  </UPageBody>
</UPage>
</template>

<script lang="ts" setup>
import { format } from 'date-fns';
import vue3StarRatings from 'vue3-star-ratings';
import { currencyPriceFormatter } from '~/utils/formatter';
definePageMeta({
  title: 'pages.creator.orders.title',
  layout: 'creator',
  middleware: 'creator-auth'
});

const route = useRoute('fan-orders-id___en');
const { useDirectusFetch, directusAssetsUrl } = useDirectus();
const { profile } = useUser();

// Computed property to get correct translation key for order status
const orderStatusKey = computed(() => {
  const status = order.value?.status || '';
  const statusKeyMap: Record<string, string> = {
    pending: 'pending',
    processing: 'not_yet_shipped',
    shipped: 'shipped',
    completed: 'completed',
    cancelled: 'cancelled'
  };
  return statusKeyMap[status] || status;
});

// Reactive state for order item navigation
const currentItemIndex = ref(0);
const isChatExpanded = ref(false);
// Initialize product ratings array with default rating of 4 for each product
const productRatings = ref<number[]>([]);
// Initialize product messages array for per-product reviews
const productMessages = ref<string[]>([]);

// Computed property for current item
const currentItem = computed(() => {
  if (!order.value?.order_items || order.value.order_items.length === 0) return null;
  return order.value.order_items[currentItemIndex.value];
});

// Get product's base currency unit price from order item
const getProductBaseCurrencyUnitPrice = () => {
  if (!currentItem.value?.product) return 0;

  const product = currentItem.value.product;
  const baseCurrency = product.base_currency?.toUpperCase();

  switch (baseCurrency) {
    case 'JPY':
      return currentItem.value.price_jpy_at_order || currentItem.value.price_at_order || 0;
    case 'KRW':
      return currentItem.value.price_krw_at_order || currentItem.value.price_at_order || 0;
    case 'USD':
    default:
      return currentItem.value.price_at_order || 0;
  }
};

// Format price in product's base currency
const formatProductBaseCurrencyPrice = (price: number) => {
  if (!currentItem.value?.product) return '';

  const baseCurrency = currentItem.value.product.base_currency || 'USD';
  return currencyPriceFormatter(price, baseCurrency);
};

// Calculate subtotal in product's base currency
const subtotal = computed(() => {
  const unitPrice = getProductBaseCurrencyUnitPrice();
  const quantity = currentItem.value?.quantity || 0;
  return unitPrice * quantity;
});

// Calculate total order amount in base currency (sum of all subtotals)
const orderTotal = computed(() => {
  if (!order.value?.order_items || order.value.order_items.length === 0) return 0;

  return order.value.order_items.reduce((total, item) => {
    const product = item.product;
    const baseCurrency = product?.base_currency?.toUpperCase();

    // Get the appropriate price based on product's base currency
    let unitPrice = 0;
    switch (baseCurrency) {
      case 'JPY':
        unitPrice = item.price_jpy_at_order || item.price_at_order || 0;
        break;
      case 'KRW':
        unitPrice = item.price_krw_at_order || item.price_at_order || 0;
        break;
      case 'USD':
      default:
        unitPrice = item.price_at_order || 0;
        break;
    }

    const itemSubtotal = unitPrice * (item.quantity || 0);
    return total + itemSubtotal;
  }, 0);
});

// Navigation functions with looping
const nextItem = () => {
  if (order.value?.order_items) {
    if (currentItemIndex.value < order.value.order_items.length - 1) {
      currentItemIndex.value++;
    } else {
      // Loop back to first item
      currentItemIndex.value = 0;
    }
  }
};

const previousItem = () => {
  if (order.value?.order_items) {
    if (currentItemIndex.value > 0) {
      currentItemIndex.value--;
    } else {
      // Loop back to last item
      currentItemIndex.value = order.value.order_items.length - 1;
    }
  }
};

// Get order details from API
const {
  data: order,
  refresh,
  status,
} = await useDirectusFetch<Orders>(`/items/orders/${route.params.id}`, {
  key: `order-${route.params.id}`,
  params: {
    fields: [
      '*',
      'order_items.*',
      'chat_messages.*',
      'creator.avatar',
      'order_items.product.*',
      'order_items.product.translations.*',
      'order_items.product.category.translations.*',
      'reviews.rating',
      'reviews.text',
      'reviews.product.main_image',
      'reviews.product.translations.*',
      'user.avatar',
      'user.nickname',
      'user.first_name',
      'user.last_name',
    ],
  },
});

if (!order.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Order not found',
  });
}

// Initialize product ratings array with default rating of 4 for each product
productRatings.value = order.value.order_items.map(() => 4);
// Initialize product messages array with empty strings for each product
productMessages.value = order.value.order_items.map(() => '');

// Chat
const chatInput = ref('');

// Unified function to submit messages
const submitMessage = async (messageText: string) => {
  if (!messageText.trim()) return;

  await useDirectusFetch('/items/messages', {
    method: 'POST',
    body: {
      order: Number(route.params.id),
      text: messageText,
    },
  });

  await refresh();
};

const handleSubmitChat = async () => {
  if (!chatInput.value.trim()) return;
  await submitMessage(chatInput.value);
  chatInput.value = '';
};

const handleChangeReaction = async (messageId: string, emoji?: string) => {
  try {
    await useDirectusFetch(`/items/messages/${messageId}`, {
      method: 'PATCH',
      body: {
        emoji: emoji || null,
      },
    });
    await refresh();
  } catch (error) {
    console.error('Failed to add reaction:', error);
  }
};

const emojis = [
  {
    label: 'Like',
    icon: 'heroicons-solid:thumb-up',
  },
  {
    label: 'Love',
    icon: 'heroicons-solid:heart',
  },
  {
    label: 'Haha',
    icon: 'twemoji:slightly-smiling-face',
  },
  {
    label: 'Wow',
    icon: 'twemoji:exploding-head',
  },
  {
    label: 'Sad',
    icon: 'twemoji:sad-but-relieved-face',
  },
  {
    label: 'Party',
    icon: 'twemoji:partying-face',
  },
];

const messages = computed<any>(() => {
  return order.value?.chat_messages.map((message) => {
    const isCreatorMessage = message.user_created === profile.value?.id;
    
    return {
      id: message.id,
      role: isCreatorMessage ? 'user' : 'assistant',
      content: message.text as string,
      createdAt: new Date(message.date_created),
      side: isCreatorMessage ? 'right' : 'left',
      variant: isCreatorMessage ? 'soft' : 'solid',
      emoji: message.emoji,
      avatar: {
        src: isCreatorMessage
          ? (directusAssetsUrl(profile.value?.avatar as string) ?? undefined)
          : (directusAssetsUrl((order.value?.user as any)?.avatar as string) ?? '/images/missing-product.png'),
      },
    };
  });
});

// Watch each product rating and always seal it to the next integer. So if it's 0.1, make it 1.
// Only update if the rating is not already a whole number to prevent infinite loops
watch(productRatings, (newRatings) => {
  const updatedRatings = newRatings.map(rating => Math.ceil(rating));
  // Only update if there are actual changes to prevent infinite loop
  if (!updatedRatings.every((rating, index) => rating === productRatings.value[index])) {
    productRatings.value = updatedRatings;
  }
}, { deep: true });
</script>
