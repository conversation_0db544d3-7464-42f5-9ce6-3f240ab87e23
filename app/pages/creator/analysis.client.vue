<script setup lang="ts">
import {today, getLocalTimeZone, toCalendarDate} from '@internationalized/date';

definePageMeta({
  title: 'pages.creator.analysis.title',
  middleware: 'creator-auth',
  layout: 'creator',
})

const { profile } = useUser()
const { t } = useI18n()
const { directusAssetsUrl } = useDirectus()

const startDate = ref(today(getLocalTimeZone()).subtract({ months: 3 })) // 3 months ago
const endDate = ref(today(getLocalTimeZone()).add({ days: 1 })) // Tomorrow to avoid timezone issues
const selectedIps = ref<Ip[]>([])

// Type the API response
interface ChartDataPoint {
  date: string
  [ipKey: string]: number | string
}

interface IpInfo {
  ip: {
    id: number
    translations: Array<{
      name: string
      languages_id: number
    }>
  }
  color: string
}

interface ApiResponse {
  revenueChartData: ChartDataPoint[]
  quantityChartData: ChartDataPoint[]
  ipData: Record<string, IpInfo>
}

const query = computed(() => ({
  startDate: toCalendarDate(startDate.value).toDate(getLocalTimeZone()),
  endDate: toCalendarDate(endDate.value).toDate(getLocalTimeZone()),
  ips: selectedIps.value.map(ip => ip.id),
}))

const { data: performanceData } = await useFetch<ApiResponse>('/api/creator/performance/charts',{
  query,
  key: JSON.stringify(query.value),
  watch: [startDate, endDate, selectedIps],
})

// X-axis formatters for both charts
const revenueXFormatter = (i: number): string => performanceData.value?.revenueChartData[i]?.date || ''
const quantityXFormatter = (i: number): string => performanceData.value?.quantityChartData[i]?.date || ''

// Create categories using useTranslatedName for locale reactivity
const categories = computed(() => {
  const cats: Record<string, { name: string; color: string }> = {}

  Object.keys(performanceData.value?.ipData || {}).forEach(ipKey => {
    const ipInfo = performanceData.value?.ipData[ipKey]
    if (ipInfo?.ip?.translations) {
      cats[ipKey] = {
        name: useTranslatedName(ipInfo.ip.translations).value,
        color: ipInfo.color
      }
    }
  })

  return cats
})

// Get profile
const { data: profileData } = await useFetch('/api/me', {
  key: 'profile',
  params: {
    fields: ['*','creator_profile.*','role.name'],
  },
});
profile.value = profileData.value || null;

// Get all IPs that this creator has products for.
const {data: creatorIps } = await useFetch('/api/creator/ips',{
  query:{
    creatorId: profile.value?.creator_profile[0].id,
  }
})
selectedIps.value = creatorIps.value || []

// Modal state for adding IPs
const isAddIpModalOpen = ref(false)

// Get available IPs that aren't already selected
const availableIps = computed(() => {
  if (!creatorIps.value) return []
  const selectedIpIds = selectedIps.value.map(ip => ip.id)
  return creatorIps.value.filter(ip => !selectedIpIds.includes(ip.id))
})

// Function to add IP to selected list
const addIpToSelection = (ip: Ip) => {
  selectedIps.value.push(ip)
  isAddIpModalOpen.value = false
}


// Products Section
const productsStartDate = ref(today(getLocalTimeZone()).subtract({ months: 3 }))
const productsEndDate = ref(today(getLocalTimeZone()).add({ days: 1 })) // Tomorrow to avoid timezone issues
const productsSortBy = ref('units_sold')

const queryProducts = computed(() => ({
  startDate: toCalendarDate(productsStartDate.value).toDate(getLocalTimeZone()),
  endDate: toCalendarDate(productsEndDate.value).toDate(getLocalTimeZone()),
  sortBy: productsSortBy.value,
}))

const { data: bestSellingProducts } = useFetch('/api/creator/performance/products', {
  query: queryProducts,
  watch: [productsStartDate, productsEndDate, productsSortBy],
})

const sortItems = ref([
  {
    label: t('global.number_sold'),
    value: 'units_sold',
  },
  {
    label: t('global.revenue'),
    value: 'revenue',
  },
]);

</script>

<template>
<UPage>
  <UPageBody>
      <h1 class="text-2xl font-bold">{{ $t('global.performance_report') }}</h1>
      <section id="ip-charts" class="-mt-8 flex flex-col gap-4">
        <h2 class="text-xl font-bold">{{ $t('global.ip') }}</h2>

        <!-- Period Selector -->
        <div id="date-range" class="flex gap-4 items-center">
          <p class="text-sm w-10">{{ $t('global.period') }}</p>
          <UIDateRangePicker v-model:start-date="startDate" v-model:end-date="endDate" />
        </div>

        <!-- IP selector -->
        <div id="ip-selection" class="flex gap-4 items-center">
          <p class="text-sm w-10">{{ $t('global.ip_s') }}</p>
          <div class="flex gap-2 flex-wrap" v-auto-animate>
            <UBadge 
              @click="() => selectedIps = selectedIps.filter(i => i.id !== ip.id)" 
              v-for="ip in selectedIps" 
              :key="ip.id" 
              class="cursor-pointer"
              color="neutral" 
              trailing-icon="i-heroicons-x-mark" 
              variant="outline">{{ useTranslatedName(ip.translations) }}</UBadge>
            <!-- Add IP Modal -->
            <UModal v-model:open="isAddIpModalOpen" title="Add IP" :close="true">
              <UButton
                v-if="availableIps.length > 0"
                size="sm"
                variant="outline"
                color="neutral"
                class="rounded-full"
                @click="isAddIpModalOpen = true"
              >
                +
              </UButton>
              <template #content>
                <div class="flex flex-col gap-4 p-4">
                  <p class="text-sm text-neutral-400">{{ $t('global.please_select_an_ip') }}</p>
                  <div class="grid gap-2">
                    <UButton
                      v-for="ip in availableIps"
                      :key="ip.id"
                      variant="ghost"
                      color="neutral"
                      class="justify-start p-3 text-left"
                      @click="addIpToSelection(ip)"
                    >
                      {{ useTranslatedName(ip.translations) }}
                    </UButton>
                  </div>
                </div>
              </template>
            </UModal>
          </div>
        </div>


        <!-- Two charts side by side -->
        <div class="grid grid-cols-2 gap-8">
          <!-- Revenue Chart -->
          <div class=" border border-neutral-500 bg-[#22202C] rounded-xl grid gap-2 text-center p-4 w-full">
            <h3 class="text-lg font-semibold">{{ $t('global.ip_monthly_revenue_summary') }}</h3>
            <LineChart :data="performanceData?.revenueChartData || []" :curve-type="CurveType.Linear" :categories="categories"
              :x-formatter="revenueXFormatter" :y-grid-line="true" :y-num-ticks="6" :x-num-ticks="12"
              :legend-position="LegendPosition.Bottom" :height="250" />
          </div>

          <!-- Quantity Chart -->
          <div class=" border border-neutral-500 bg-[#22202C] rounded-xl grid gap-2 text-center p-4 w-full">
            <h3 class="text-lg font-semibold">{{ $t('global.ip_monthly_sale_count_summary') }}</h3>
            <LineChart :data="performanceData?.quantityChartData || []" :curve-type="CurveType.Linear" :categories="categories"
              :x-formatter="quantityXFormatter" :y-grid-line="true" :y-num-ticks="6" :x-num-ticks="12"
              :height="250" />
          </div>
        </div>
      </section>

      <section id="product-performance-list">
        <!-- Period Selector & Sort -->
        <div id="date-range" class="flex gap-4 items-center">
          <p class="text-sm w-10">{{ $t('global.period') }}</p>
          <UIDateRangePicker v-model:start-date="productsStartDate" v-model:end-date="productsEndDate" />
          <p class="ml-auto text-sm">{{ $t('global.sort_by') }}</p>
          <USelect
            v-model="productsSortBy"
            :items="sortItems"
            class="w-48 cursor-pointer"
            icon="i-lucide-sort-desc"
            :placeholder="$t('global.sort_by')"
          />
        </div>

        <!-- Product List Headers -->
        <div id="product-list-headers" class="grid gap-4 p-4 text-sm text-neutral-500" style="grid-template-columns: 60px 1fr 1fr 1fr 1fr 1fr;">
          <p class="text-center">#</p>
          <p>{{ $t('global.preview_image') }}</p>
          <p>{{ $t('global.product_name') }}</p>
          <p>{{ $t('global.ip') }}</p>
          <p>{{ $t('global.revenue') }}</p>
          <p>{{ $t('global.number_sold') }}</p>
        </div>

         <!-- Empty state -->
        <div v-if="!bestSellingProducts && bestSellingProducts?.length === 0" class="flex flex-col items-center justify-center py-16 text-center">
          <UIcon name="heroicons:shopping-bag" class="mb-4 size-16 text-neutral-400" />
          <h3 class="mb-2 text-lg font-semibold text-neutral-300"> 
            {{ $t('global.no_data_found') }}
          </h3>
          <p class="mb-6 text-neutral-500">
            {{ $t('global.no_data_found') }}
          </p>
          <NuxtLinkLocale
            :to="{ name: 'index' }"
            class="bg-primary hover:bg-primary/90 inline-flex items-center rounded-lg px-4 py-2 text-sm font-medium text-white"
          >
            <UIcon name="heroicons:shopping-cart" class="mr-2 size-4" />
            {{ $t('global.start_shopping') }}
          </NuxtLinkLocale>
        </div>

        <!-- Product List -->
        <div v-else  v-auto-animate>
          <div v-for="(item,index) in bestSellingProducts" :key="item.product" class="mb-6 grid gap-4 rounded-lg border border-neutral-700 p-4 items-center" style="grid-template-columns: 60px 1fr 1fr 1fr 1fr 1fr;">
            <div class="text-center">
              <p class="font-semibold text-lg">{{ index + 1 }}</p>
            </div>
            <div>
              <NuxtImg
                :src="directusAssetsUrl(item.productData?.main_image as string, 200, 200) ?? '/images/missing-product.png'"
                class="h-32 w-full rounded-lg object-cover"
                placeholder
                placeholder-class="blur-xs"
              />
            </div>
            <div>
              <p>{{ item.productData?.translations ? useTranslatedName(item.productData.translations) : 'N/A' }}</p>
            </div>
            <div class="text-sm md:text-md">
              <p>{{ (item.productData?.ip as any)?.translations ? useTranslatedName((item.productData?.ip as any).translations) : 'N/A' }}</p>
            </div>
            <div class="text-sm md:text-md">
              <p>{{ Number(item.sum.price_at_order).toFixed(0) }}</p>
            </div>
            <div class="text-sm md:text-md">
              <p>{{ item.sum.quantity }}</p>
            </div>
          </div>
        </div>
      </section>
  </UPageBody>
</UPage>
</template>