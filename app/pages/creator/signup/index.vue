<template>
  <div class="flex min-h-screen flex-col items-center justify-center px-2">
    <Otp mode="signup" role="creator" />
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: "fullscreen",
  title: "pages.creator.signup.title"
});

const route = useRoute();
const { email: signupEmail, role: signupRole } = useSignup();

// Handle email and role state when component mounts
onMounted(() => {
  // If there's an email in the URL params, store it in signup state
  if (route.query.email) {
    signupEmail.value = route.query.email as string;
  }

  // Always set role to 'creator' since we're on the creator signup page
  signupRole.value = 'creator';
});
</script>
