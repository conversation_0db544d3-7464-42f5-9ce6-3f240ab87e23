<template>
  <div>
    <div class="flex min-h-screen flex-col items-center justify-center px-4 py-8">
      <div
        class="flex w-full max-w-md flex-col items-center rounded-lg border border-[#3F3D47] bg-[#1B1A2199] p-4 backdrop-blur-xl sm:max-w-lg sm:p-6 md:max-w-xl md:p-8 lg:max-w-2xl"
      >
        <ULink to="/">
          <img :src="'/images/ipgo-cherry.svg'" alt="IPGO" class="-mt-12 h-12 w-12 md:-mt-16" />
        </ULink>
        <h2 class="my-2 text-center text-xl font-bold">
          {{ $t('global.edit_profile') }}
        </h2>
        <CreatorProfileDetails class="mx-2 lg:py-24" mode="signup" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'fullscreen',
});

const localePath = useLocalePath();
const { email, role } = useSignup();

// Redirect to OTP if no email or wrong role
if (!email.value || role.value !== 'creator') {
  navigateTo(localePath('creator-signup'));
}
</script>
