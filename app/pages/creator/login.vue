<template>
  <div>
    <Login mode="creator" />
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: "fullscreen",
  title: 'pages.creator.login.title'
});

const { accessToken, refreshToken } = useUser();
const localePath = useLocalePath()

if (refreshToken.value && !accessToken.value) {
  try {
    await useFetch('/api/auth/refresh-token', {
      method: 'POST',
    });
    navigateTo(localePath('creator-ip'))
  } catch (error) {
    console.error(error)
  }
}
</script>
