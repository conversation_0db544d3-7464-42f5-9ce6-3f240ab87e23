<template>
  <UPage>
    <UPageBody>
      <!-- Wide Profile Card -->
      <div class="flex gap-4 rounded-xl border border-neutral-700 bg-neutral-900 p-4">
        <!-- Avatar -->
        <div v-if="creator?.user?.avatar">
          <UAvatar class="col-span-1 h-24 w-24" :src="directusAssetsUrl(creator?.user?.avatar as string)" />
        </div>
        <div v-else>
          <UIInitialAvatar :name shape="circle" class="col-span-2 h-28 w-28" />
        </div>
        <!-- Creator Details -->
        <div class="col-span-8 flex flex-col justify-center">
          <h2 class="text-2xl font-bold">{{ name }}</h2>
          <p class="text-sm text-neutral-500">{{ creator?.introduction }}</p>
        </div>
      </div>

      <!-- Creator Products -->
      <section id="creator-products" class="grid gap-4">
        <h3 class="text-2xl font-bold">{{ $t('global.licensed_products') }}</h3>
        <!-- Mobile-friendly filters: stack vertically on mobile, horizontal on desktop -->
        <div id="creator-product-filters" class="flex flex-col gap-4 sm:flex-row">
          <USelectMenu
            v-model="selectedIpCategory"
            :items="localzedIpCategoryItems"
            icon="i-lucide-user"
            class="w-full sm:w-48 cursor-pointer"
            :placeholder="$t('global.filter_by_ip')"
          />
          <USelectMenu
            v-model="selectedProductCategory"
            :items="localzedProductCategoryItems"
            icon="i-lucide-user"
            class="w-full sm:w-48 cursor-pointer"
            :placeholder="$t('global.filter_by_product_category')"
          />
          <UInput
            v-model="filter.productName"
            :placeholder="$t('global.search_product')"
            icon="i-lucide-search"
            class="w-full sm:ml-auto sm:w-48"
          />
        </div>
        <div class="grid grid-cols-2 gap-4 sm:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5" v-auto-animate>
          <LazyVerticalProductCard
            v-for="product in creatorProducts"
            :product="product"
            class="max-h-92 md:min-w-2xs"
          />
        </div>
      </section>
    </UPageBody>
  </UPage>
</template>

<script lang="ts" setup>

const { useDirectusFetch, directusAssetsUrl } = useDirectus();
const { t, locale } = useI18n();
const route = useRoute('creators-id___en');
const { data: creator } = await useDirectusFetch<Creator>(`/items/creator/${route.params.id}`, {
  params: {
    fields: ['nickname', 'user.avatar', 'user.first_name', 'user.last_name','introduction'],
  },
});

const name = computed(() => {
  return (
    [creator.value?.first_name, creator.value?.last_name].filter(Boolean).join(' ') || creator.value?.nickname || ''
  );
});

// Filter state - using string values to match the Fan filter system
const filter = useState('creatorProductFilter', () => ({
  productName: '',
  productCategory: '',
  ipCategory: '',
}));

// Filter parameters computed property - similar to Fan/ProductResultList.vue
const filterParams = computed(() => {
  const filters: any[] = [];

  // Always filter by creator
  filters.push({
    creator: {
      _eq: route.params.id,
    },
  });

  // Product name search (search in translations)
  if (filter.value.productName) {
    filters.push({
      translations: {
        name: {
          _icontains: filter.value.productName,
        },
      },
    });
  }

  // Product category filter
  if (filter.value.productCategory) {
    filters.push({
      category: {
        code: {
          _eq: filter.value.productCategory,
        },
      },
    });
  }

  // IP category filter
  if (filter.value.ipCategory) {
    filters.push({
      ip: {
        category: {
          code: {
            _eq: filter.value.ipCategory,
          },
        },
      },
    });
  }

  return {
    fields: [
      '*',
      'translations.*',
      'category.translations.*',
      'ip.translations.ip_id',
      'ip.translations.name',
      'ip.translations.languages_id',
    ],
    filter: filters.length > 0 ? { _and: filters } : {},
  };
});

// Licensed Products with filter support
const { data: creatorProducts } = useDirectusFetch<Products[]>('/items/products', {
  key: `creator-${route.params.id}-products`,
  watch: [filter],
  params: filterParams,
});

// Fetch IP categories from Directus (same approach as IpCategoriesFilter.vue)
const { data: populatedIpCategories } = await useFetch('/api/ips/categories', { key: 'populated-ip-categories' });

const { data: ipCategories } = await useDirectusFetch<IpCategories[]>('/items/ip_categories', {
  key: 'ip-categories',
  watch: [locale], // Make reactive to locale changes
  params: {
    fields: ['code, translations.name, translations.languages_id'],
    filter: {
      id: {
        _in: populatedIpCategories,
      },
    },
    sort: ['translations.name'],
  },
});

const localzedIpCategoryItems = computed(() => {
  if (!ipCategories.value) return [];
  return ipCategories.value.map((item) => ({
    label: useTranslatedName(item.translations).value,
    value: item.code,
  }));
});

const localzedProductCategoryItems = computed(() => {
  const productItems = PRODUCT_CATEGORIES;
  return [
    {
      label: t('global.all'),
      value: '',
    },
    ...productItems.map((item) => ({
      label: t(item.label),
      value: item.value,
    })),
  ];
});

// Computed properties for USelectMenu v-model (they expect objects)
const selectedIpCategory = computed({
  get: () => {
    if (!filter.value.ipCategory) return undefined;
    return localzedIpCategoryItems.value.find((item) => item.value === filter.value.ipCategory) || undefined;
  },
  set: (value) => {
    filter.value.ipCategory = value?.value || '';
  },
});

const selectedProductCategory = computed({
  get: () => {
    if (!filter.value.productCategory) return undefined;
    return localzedProductCategoryItems.value.find((item) => item.value === filter.value.productCategory) || undefined;
  },
  set: (value) => {
    filter.value.productCategory = value?.value || '';
  },
});
useSeoMeta({
  title: () => name.value,
  description: () => creator.value?.introduction,
  ogTitle: () => name.value,
  ogDescription: () => creator.value?.introduction,
  ogImage: () => directusAssetsUrl((creator.value?.user as DirectusUsers)?.avatar as string),
});
</script>
