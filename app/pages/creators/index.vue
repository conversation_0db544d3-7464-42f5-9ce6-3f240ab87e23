<template>
  <UPage>
    <UPageBody v-auto-animate>
      <h1 class="text-2xl font-bold">{{ $t('global.creators') }}</h1>
      <div class="-mt-4 grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-5 lg:grid-cols-8">
        <FanCreatorCard
          :creator="creator"
          v-for="creator in creators"
          :key="creator.id"
          class="hover:-translate-y-0.5"
        />
      </div>
    </UPageBody>
  </UPage>
</template>

<script lang="ts" setup>
// const route = useRoute();
const { t } = useI18n();
// const { locale } = useI18n();
useSeoMeta({
  title: () => t('global.all_creators'),
});
definePageMeta({
  title: 'pages.fan.creators.title',
});

const { data: creators } = await useFetch('/api/creators', {
  key: 'all-creators',
});
</script>
