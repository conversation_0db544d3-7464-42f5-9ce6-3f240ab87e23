<template>
  <div class="flex min-h-screen flex-col items-center justify-center px-2">
    <div
      class="flex w-md max-w-full flex-col items-center rounded-lg border border-[#3F3D47] bg-[#1B1A2199] p-6 backdrop-blur-xl md:p-8"
    >
      <ULink to="/">
        <NuxtPicture src="/images/ipgo-cherry.svg" alt="IPGO" class="-mt-14 h-12 w-12 md:-mt-16" loading="eager" />
      </ULink>
      <h2 class="my-2 text-center text-xl font-bold">
        {{ $t('global.set_password') }}
      </h2>
      <form @submit.prevent="" v-auto-animate class="w-full">
        <div class="flex flex-col gap-4">
          <!-- Password input -->
          <UFormField name="password" :label="$t('global.password')">
            <UInput
              v-model="password1"
              :type="showPassword ? 'text' : 'password'"
              id="password"
              class="w-full"
              size="xl"
              :placeholder="$t('global.input_password_placeholder')"
            >
            </UInput>
          </UFormField>

          <!-- Password input again -->
          <UFormField name="password" :label="$t('global.password')">
            <UInput
              v-model="password2"
              type="password"
              id="password"
              class="w-full"
              size="xl"
              :placeholder="$t('global.input_password_placeholder')"
            >
            </UInput>
          </UFormField>

          <!-- Reset Password Button-->
          <UButton
            color="primary"
            size="lg"
            class="mt-4 w-full justify-center rounded-full"
            :disabled="isLoading"
            :loading="isLoading"
            @click.prevent="resetPassword"
          >
            {{ isLoading ? 'Resetting...' : 'Reset Password' }}
          </UButton>
        </div>
      </form>
    </div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'fullscreen',
  title: 'pages.fan.recoverPassword.title',
});
const route = useRoute();
const toast = useToast();
const { t }= useI18n();
const localePath = useLocalePath();
const { email } = useSignup();
const { profile } = useUser();
const showPassword = ref(false);
const password1 = ref('');
const password2 = ref('');
const isLoading = ref(false);

// Get email from URL query params or signup state
const userEmail = computed(() => {
  return (route.query.email as string) || email.value || '';
});

const isPasswordValid = computed(() => password1.value === password2.value && password1.value.length > 0);

async function resetPassword() {
  if (!userEmail.value) {
    toast.add({
      title: 'Error',
      description: t('global.please_restart_password_recovery_process'),
      color: 'error',
    });
    return;
  }

  if (!isPasswordValid.value) {
    toast.add({
      title: 'Error',
      description: t('global.password_confirm_not_match'),
      color: 'error',
    });
    return;
  }

  isLoading.value = true;

  try {
    console.log('🚀 Sending reset password request...');
    const resetResponse = await $fetch<{success: boolean, role: 'fan' | 'creator' | 'owner', email: string, accessToken: string}>('/api/auth/reset-password', {
      method: 'POST',
      body: {
        email: userEmail.value,
        password: password1.value,
      },
    });

    toast.add({
      title: 'Success',
      description: 'Password reset successfully! Logging you in...',
      color: 'success',
    });

    // Refresh user profile data since they're now logged in
    try {
      const profileData = await $fetch('/api/me');
      profile.value = profileData;
    } catch (profileError) {
      console.warn('Failed to fetch profile after login:', profileError);
    }

    // User is now automatically logged in, redirect to their dashboard
    switch (resetResponse.role) {
      case 'creator':
        navigateTo(localePath('creator-ip'));
        break;
      case 'owner':
        navigateTo(localePath('owner-ip'));
        break;
      case 'fan':
      default:
        navigateTo(localePath('fan'));
        break;
    }
  } catch (error: any) {
    console.error('Failed to reset password', error);
    toast.add({
      title: 'Error',
      description: error?.data?.statusMessage || t('global.unexpected_error'),
      color: 'error',
    });
  } finally {
    isLoading.value = false;
  }
}

// Set email from URL params on mount
onMounted(() => {
  if (route.query.email && !email.value) {
    email.value = route.query.email as string;
  }
});
</script>
