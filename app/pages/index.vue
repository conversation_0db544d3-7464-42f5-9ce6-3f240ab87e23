<template>
  <UPage>
    <template #left>
      <FanLeftMenu />
    </template>
    <UPageBody>
      <template v-if="!filterApplied">
        <HeroCarousel class="-mt-3 md:mt-0" transition-style="in:circle:top-left"/>

        <!-- <section id="sales-and-special-offers">
          <h3 class="mb-2 text-2xl font-bold">{{ $t('global.sales_and_special_offers') }}</h3>
          <FanPromotionCarousel />
        </section> -->

        <section id="new-products" >
          <!-- <h3 class="mb-2 text-2xl font-bold">{{ $t('global.new_products') }}</h3> -->
          <div class="mb-2 flex items-center gap-2">
            <h3 class="text-2xl font-bold">{{ $t('global.new_products') }}</h3>
            <NuxtLinkLocale to="products" class="text-xs">
              >> {{ $t('global.view_more') }}
            </NuxtLinkLocale>
          </div>
          <FanNewProductCarousel />
        </section>

        <section id="popuar-ips">
          <div class="mb-2 flex items-center gap-2">
            <h3 class="text-2xl font-bold">{{ $t('global.popular_ip') }}</h3>
            <NuxtLinkLocale to="ip" class="text-xs">
              >> {{ $t('global.view_more') }}
            </NuxtLinkLocale>
          </div>
          <FanPopularIPs class="w-full" />
        </section>

        <section id="top-creators">
          <div class="mb-2 flex items-center gap-2">
            <h3 class="text-2xl font-bold">{{ $t('global.top_creators') }}</h3>
            <NuxtLinkLocale to="creators" class="text-xs">
              >> {{ $t('global.view_more') }}
            </NuxtLinkLocale>
          </div>
          <FanTopCreators />
        </section>
      </template>
      <template v-else>
        <FanTopBarFilterMenu />
        <FanProductResultList class="-mt-6" />
      </template>
    </UPageBody>
  </UPage>
</template>

<script lang="ts" setup>
const localePath = useLocalePath();

definePageMeta({
  title: 'pages.fan.top.title',
});

const { filter, filterApplied, resetFilter } = useFanFilter();

resetFilter();

// apply the filter as query param to the url
watch(
  filter,
  () => {
    // Check if all filter values are empty/default (filter is cleared)
    const isFilterCleared =
      filter.value.status === '' &&
      filter.value.availability === '' &&
      filter.value.productCategory === '' &&
      filter.value.minPrice === '' &&
      filter.value.maxPrice === '' &&
      filter.value.ipCategory === '' &&
      filter.value.creator.value === '' &&
      filter.value.searchValue === '' &&
      filter.value.page === 1 &&
      filter.value.sortBy === 'latest';

    if (isFilterCleared) {
      // Clear the URL by navigating to the base path without query params
      navigateTo({ path: localePath('index'), query: {} });
    } else {
      // Build query object with only non-empty values
      const query: Record<string, string | number> = {};

      if (filter.value.status) query.status = filter.value.status;
      if (filter.value.availability) query.availability = filter.value.availability;
      if (filter.value.productCategory) query.productCategory = filter.value.productCategory;
      if (filter.value.minPrice) query.minPrice = filter.value.minPrice;
      if (filter.value.maxPrice) query.maxPrice = filter.value.maxPrice;
      if (filter.value.ipCategory) query.ipCategory = filter.value.ipCategory;
      if (filter.value.creator.value) query.creatorId = filter.value.creator.value;
      if (filter.value.searchValue) query.searchValue = filter.value.searchValue;
      if (filter.value.page !== 1) query.page = filter.value.page;
      if (filter.value.sortBy !== 'latest') query.sortBy = filter.value.sortBy;

      navigateTo({ path: localePath('products'), query });
    }
  },
  { deep: true },
);
</script>
