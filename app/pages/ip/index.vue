<template>
  <UPage>
    <UPageBody v-auto-animate>
      <div class="flex flex-col gap-4">
        <h1 class="text-2xl font-bold">{{ $t('global.available_ip') }}</h1>
        <IpCategoriesFilter v-model="selectedIpCategory" />

        <!-- IP Item Cards -->
        <div class="grid gap-4 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4" v-auto-animate ref="scrollContainer">
          <IPCard :ip="ip" v-for="ip in allIps" :key="ip.id" :locale="locale" class="" />
        </div>

        <!-- No IPs found message (only when zero items) -->
        <div v-if="!isLoading && allIps.length === 0" class="text-center py-8 text-gray-500">
          {{ $t('global.no_data_found') }}
        </div>

        <!-- Loading indicator -->
        <div v-if="isLoading" class="flex justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    </UPageBody>
  </UPage>
</template>

<script lang="ts" setup>
const route = useRoute();
const { t } = useI18n();
const { locale } = useI18n();

useSeoMeta({
  title: () => t('global.all_ips'),
});

definePageMeta({
  title: 'pages.fan.all_ips.title',
});

const { dFetch } = useDirectus();

// Initialize filter state from URL query parameters
const selectedIpCategory = ref(route.query.category as string);

// Infinite scroll state
const allIps = ref<Ip[]>([]);
const currentPage = ref(1);
const isLoading = ref(false);
const hasReachedEnd = ref(false);
const scrollContainer = ref<HTMLElement>();
const itemsPerPage = 30;

// Filter parameters for API calls
const getFilterParams = (page: number) => ({
  fields: ['id, translations.*, images.directus_files_id, main_image, category.translations.*'],
  limit: itemsPerPage,
  offset: (page - 1) * itemsPerPage,
  filter: selectedIpCategory.value
    ? [
        {
          category: {
            code: {
              _eq: selectedIpCategory.value,
            },
          },
        },
      ]
    : [],
});

// Load more IPs function
const loadMoreIps = async () => {
  console.log('loadMoreIps called', { isLoading: isLoading.value, hasReachedEnd: hasReachedEnd.value, currentPage: currentPage.value });

  if (isLoading.value || hasReachedEnd.value) {
    console.log('Skipping load - already loading or reached end');
    return;
  }

  isLoading.value = true;
  console.log('Loading page:', currentPage.value);

  try {
    const params = getFilterParams(currentPage.value);
    console.log('API params:', params);

    const newIps = await dFetch<Ip[]>('/items/ip', {
      params,
    });

    console.log('Received IPs:', newIps?.length || 0);

    if (newIps.length < itemsPerPage) {
      hasReachedEnd.value = true;
      console.log('Reached end of results');
    }

    if (newIps.length > 0) {
      allIps.value.push(...newIps);
      currentPage.value++;
      console.log('Total IPs now:', allIps.value.length);
    }
  } catch (error) {
    console.error('Error loading more IPs:', error);
  } finally {
    isLoading.value = false;
  }
};

// Reset and reload when category changes
const resetAndReload = async () => {
  allIps.value = [];
  currentPage.value = 1;
  hasReachedEnd.value = false;
  await loadMoreIps();
};

// Watch for category changes
watch(selectedIpCategory, resetAndReload, { immediate: true });

// Setup infinite scroll - use window instead of container
useInfiniteScroll(
  window,
  loadMoreIps,
  { distance: 300 }
);
</script>

<style></style>
