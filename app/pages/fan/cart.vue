<template>
  <div class="grid grid-cols-12 gap-4">
    <!-- Cart List -->
    <div id="cart-list" class="col-span-12 flex flex-col gap-6 lg:col-span-10 xl:col-span-8">
      <!-- Cart Items Grouped by Creator -->
      <div
        v-if="cartItemsByCreator && Object.keys(cartItemsByCreator).length > 0"
        v-for="(creatorItems, creatorId) in cartItemsByCreator"
        :key="creatorId"
        class="rounded-xl border border-neutral-700 bg-neutral-900 p-4"
      >
        <!-- Creator Header -->
        <div class="mb-4 flex items-center justify-between border-b border-neutral-700 pb-4">
          <div class="flex items-center gap-3">
            <div
              class="bg-primary-500 flex h-10 w-10 items-center justify-center rounded-full font-semibold text-white"
            >
              {{ getCreatorInitials(getCreatorData(creatorItems[0]).nickname) }}
            </div>
            <div>
              <h3 class="text-lg font-semibold">{{ getCreatorData(creatorItems[0]).nickname }}</h3>
              <p class="text-sm text-neutral-400">
                {{ creatorItems.length }} {{ creatorItems.length === 1 ? 'item' : 'items' }}
              </p>
            </div>
          </div>
          <div class="text-right">
            <p class="text-sm text-neutral-400">{{ $t('global.total') }}</p>
            <p class="text-primary text-xl font-bold">
              {{ formatPrice(getCreatorTotal(creatorItems)) }}
            </p>
          </div>
        </div>

        <!-- Desktop Layout: Items on left, Message on right -->
        <div class="hidden md:flex md:gap-6">
          <!-- Left: Creator's Items -->
          <div class="flex-1 space-y-4">
            <div
              v-for="item in creatorItems"
              :key="item.id"
              id="cart-item"
              class="grid grid-cols-12 items-start gap-4"
            >
              <!-- Product Image -->
              <NuxtLinkLocale class="col-span-3" :to="{ name: 'products-id', params: { id: item.id } }">
                <div
                  class="flex aspect-square h-full w-full items-center justify-center rounded-lg border border-neutral-700"
                >
                  <NuxtImg
                    :src="directusAssetsUrl(item.main_image as string) ?? '/images/missing-product.png'"
                    class="object-cover-lg h-full w-full rounded-lg object-cover"
                    placeholder
                    placeholder-class="blur-xs"
                  />
                </div>
              </NuxtLinkLocale>

              <!-- Product Details -->
              <div class="col-span-8 flex grow-1 flex-col gap-2">
                <!-- Product Name -->
                <h3 class="text-lg font-bold">{{ useTranslatedName(item.translations) }}</h3>
                <p class="line-clamp-2 text-sm text-neutral-500">{{ useTranslatedDescription(item.translations) }}</p>

                <!-- Product Qty -->
                <div class="flex items-center gap-2">
                  <div class="flex w-max items-center gap-2 rounded-lg border border-neutral-700 bg-neutral-900 p-1">
                    <UButton icon="i-heroicons-minus" size="sm" variant="ghost" @click="decreaseQuantity(item.id)" />
                    <span class="w-8 text-center">{{ item.quantity }}</span>
                    <UButton icon="i-heroicons-plus" size="sm" variant="ghost" @click="increaseQuantity(item.id)" />
                  </div>
                  <!-- Stock indicator badge -->
                  <UBadge
                    v-if="item.stock_remaining !== null && item.stock_remaining !== undefined && item.quantity >= item.stock_remaining"
                    color="warning"
                    variant="soft"
                    size="xs"
                  >
                    {{ $t('global.max_stock_available', { stock: item.stock_remaining }) }}
                  </UBadge>
                </div>

                <!-- Product Price -->
                <div class="flex flex-col gap-1">
                  <p class="font-semibold">
                    {{ formatPrice(getItemTotal(item)) }}
                  </p>
                </div>
              </div>

              <!-- Remove Item Button -->
              <div class="col-span-1 flex items-start justify-end">
                <UButton
                  color="error"
                  variant="ghost"
                  icon="heroicons:x-mark"
                  size="sm"
                  @click="removeFromCart(item.id)"
                />
              </div>
            </div>
          </div>

          <!-- Right: Message to Creator -->
          <div class="w-80 flex-shrink-0">
            <UFormField :label="$t('global.message_to_creator')" class="mb-4">
              <UTextarea
                v-model="creatorMessages[creatorId]"
                class="w-full"
                :placeholder="$t('global.send_message_to_complete_transaction')"
                :rows="8"
              />
            </UFormField>
          </div>
        </div>

        <!-- Mobile Layout: Items stacked, Message below -->
        <div class="md:hidden space-y-4">
          <!-- Creator's Items -->
          <div class="space-y-4">
            <div
              v-for="item in creatorItems"
              :key="item.id"
              id="cart-item"
              class="grid grid-cols-8 items-start gap-4"
            >
              <!-- Product Image -->
              <NuxtLinkLocale class="col-span-3" :to="{ name: 'products-id', params: { id: item.id } }">
                <div
                  class="flex aspect-square h-full w-full items-center justify-center rounded-lg border border-neutral-700"
                >
                  <NuxtImg
                    :src="directusAssetsUrl(item.main_image as string) ?? '/images/missing-product.png'"
                    class="object-cover-lg h-full w-full rounded-lg object-cover"
                    placeholder
                    placeholder-class="blur-xs"
                  />
                </div>
              </NuxtLinkLocale>

              <!-- Product Details -->
              <div class="col-span-4 flex grow-1 flex-col gap-2">
                <!-- Product Name -->
                <h3 class="text-lg font-bold">{{ useTranslatedName(item.translations) }}</h3>
                <p class="line-clamp-2 text-sm text-neutral-500">{{ useTranslatedDescription(item.translations) }}</p>

                <!-- Product Qty -->
                <div class="flex items-center gap-2">
                  <div class="flex w-max items-center gap-2 rounded-lg border border-neutral-700 bg-neutral-900 p-1">
                    <UButton icon="i-heroicons-minus" size="sm" variant="ghost" @click="decreaseQuantity(item.id)" />
                    <span class="w-8 text-center">{{ item.quantity }}</span>
                    <UButton icon="i-heroicons-plus" size="sm" variant="ghost" @click="increaseQuantity(item.id)" />
                  </div>
                  <!-- Stock indicator badge -->
                  <UBadge
                    v-if="item.stock_remaining !== null && item.stock_remaining !== undefined && item.quantity >= item.stock_remaining"
                    color="warning"
                    variant="soft"
                    size="xs"
                  >
                    {{ $t('global.max_stock_available', { stock: item.stock_remaining }) }}
                  </UBadge>
                </div>

                <!-- Product Price -->
                <div class="flex flex-col gap-1">
                  <p class="font-semibold">
                    {{ formatPrice(getItemTotal(item)) }}
                  </p>
                </div>
              </div>

              <!-- Remove Button -->
              <UButton
                color="error"
                variant="ghost"
                icon="heroicons:x-mark"
                size="sm"
                class="ml-auto"
                @click="removeFromCart(item.id)"
              />
            </div>
          </div>

          <!-- Message to Creator (Mobile) -->
          <div class="border-t border-neutral-700 pt-4">
            <UFormField :label="$t('global.message_to_creator')" class="mb-4">
              <UTextarea
                v-model="creatorMessages[creatorId]"
                class="w-full"
                :placeholder="$t('global.send_message_to_complete_transaction')"
                :rows="3"
              />
            </UFormField>
          </div>
        </div>

        <!-- Creator Checkout Section -->
        <div class="flex items-center justify-between">
          <div class="text-sm text-neutral-400">
            {{ $t('global.shipping_from') }}: {{ getCreatorData(creatorItems[0]).nickname }}
          </div>
          <CheckoutModal
            :ref="(el) => { if (el) checkoutModals[creatorId] = el }"
            :items="formatCreatorItemsForCheckout(creatorItems)"
            :title="$t('global.pay_now')"
            :predefined-message="creatorMessages[creatorId] || ''"
            :hide-message-field="true"
            @success="handleOrderSuccess"
            @error="handleOrderError"
          >
            <UButton
              color="primary"
              size="lg"
              class="justify-center rounded-full px-8"
              @click="openCheckoutModal(creatorId)"
              :disabled="!creatorItems || creatorItems.length === 0"
            >
              {{ $t('global.order_now') }} -
              {{ formatPrice(getCreatorTotal(creatorItems)) }}
            </UButton>
          </CheckoutModal>
        </div>
      </div>

      <!-- Empty Cart State -->
      <div v-else class="w-full">
        <p
          class="grid h-42 items-center justify-center rounded-xl border border-neutral-700 text-center text-neutral-500"
        >
          {{ $t('global.cart_empty') }}
        </p>
      </div>
    </div>

    <!-- Cart Summary -->
    <div id="totals" class="col-span-12 flex flex-col gap-4 lg:col-span-2 xl:col-span-4">
      <div class="rounded-xl border border-neutral-700 bg-neutral-900 p-4">
        <h3 class="mb-4 text-lg font-semibold">{{ $t('global.cart_summary') }}</h3>

        <!-- Creator Breakdown -->
        <div v-if="cartItemsByCreator && Object.keys(cartItemsByCreator).length > 0" class="space-y-3">
          <div
            v-for="(creatorItems, creatorId) in cartItemsByCreator"
            :key="creatorId"
            class="flex justify-between text-sm"
          >
            <span class="text-neutral-400">{{ getCreatorData(creatorItems[0]).nickname }}</span>
            <span class="font-medium">{{
              formatPrice(getCreatorTotal(creatorItems))
            }}</span>
          </div>

          <div class="border-t border-neutral-700 pt-3">
            <p class="mb-2 text-xs text-neutral-500">{{ $t('global.multiple_orders_note') }}</p>
            <p class="text-sm text-neutral-400">{{ $t('global.total_items') }}: {{ totalItemCount }}</p>
          </div>
        </div>

        <div v-else class="py-4 text-center text-neutral-500">
          {{ $t('global.cart_empty') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>


definePageMeta({
  layout: 'fan-profile',
  middleware: 'auth',
});

const { directusAssetsUrl } = useDirectus();
const { cart, removeItem, removeItems } = useCart();
const toast = useToast();
const { t } = useI18n();
const { formatPrice: formatCurrencyPrice, getProductPrice } = useCurrency();

// Fetch full product details from server (only once on page load)
const { data: serverCartData } = await useFetch('/api/cart/calculate', {
  method: 'POST',
  body: cart,
});

// Local reactive cart items with quantities that can be updated
const cartItems = ref<Array<any>>([]);

// Initialize cart items with server data and handle stock adjustments
watch(
  () => serverCartData.value,
  (newData) => {
    if (newData?.items) {
      // Update cart items with server-validated quantities
      cartItems.value = newData.items.map((item: any) => ({
        ...item,
        // Use the server-adjusted quantity, not the original cart quantity
        quantity: item.quantity,
      }));

      // Update cart cookie with adjusted quantities and show notifications
      // Only process adjustments if the data structure is complete
      if (newData.hasAdjustments && Array.isArray(newData.adjustedItems)) {
        newData.items.forEach((item: any) => {
          if (item.wasAdjusted) {
            cart.value[item.id] = item.quantity;
          }
        });

        // Show notifications for adjusted items
        if (newData.adjustedItems.length > 0) {
          newData.adjustedItems.forEach((adjustment: any) => {
            if (adjustment.availableQuantity === 0) {
              // Item completely out of stock
              toast.add({
                title: t('global.item_out_of_stock'),
                description: t('global.item_removed_from_cart', { name: adjustment.name }),
                color: 'error',
              });
              // Remove from cart cookie
              delete cart.value[adjustment.id];
            } else {
              // Quantity reduced but still available
              toast.add({
                title: t('global.quantity_adjusted'),
                description: t('global.quantity_reduced_due_to_stock', {
                  name: adjustment.name,
                  available: adjustment.availableQuantity,
                  requested: adjustment.requestedQuantity
                }),
                color: 'warning',
              });
            }
          });
        }
      }
    }
  },
  { immediate: true }
);

// Group cart items by creator
const cartItemsByCreator = computed(() => {
  if (!cartItems.value || cartItems.value.length === 0) {
    return {};
  }

  return cartItems.value.reduce((groups: Record<string, any[]>, item: any) => {
    // Use the helper function to get creator ID consistently
    const creatorData = getCreatorData(item);
    const creatorId = creatorData.id;

    if (!groups[creatorId]) {
      groups[creatorId] = [];
    }
    groups[creatorId]!.push(item);
    return groups;
  }, {});
});

// Calculate total item count
const totalItemCount = computed(() => {
  return cartItems.value.reduce((total, item) => total + item.quantity, 0);
});

// Quantity management functions
const increaseQuantity = (productId: number) => {
  const item = cartItems.value.find((item) => item.id === productId);
  if (item) {
    // Check stock availability before increasing quantity
    if (item.stock_remaining !== null && item.stock_remaining !== undefined) {
      if (item.quantity >= item.stock_remaining) {
        toast.add({
          title: t('global.insufficient_stock'),
          description: t('global.max_stock_available', { stock: item.stock_remaining }),
          color: 'error',
        });
        return;
      }
    }

    item.quantity += 1;
    cart.value[productId] = item.quantity; // Update cookie
  }
};

const decreaseQuantity = (productId: number) => {
  const item = cartItems.value.find((item) => item.id === productId);
  if (item && item.quantity > 1) {
    item.quantity -= 1;
    cart.value[productId] = item.quantity; // Update cookie
  }
};

const removeFromCart = (productId: number) => {
  removeItem(productId);
  cartItems.value = cartItems.value.filter((item) => item.id !== productId);
};

// Price formatting utility - locale-aware
const formatPrice = (price: number) => {
  return formatCurrencyPrice(price);
};

// Calculate item total (price × quantity) using locale-based pricing
const getItemTotal = (item: any) => {
  const localePrice = getProductPrice(item);
  return localePrice * item.quantity;
};

// Utility functions
const getCreatorData = (item: any) => {
  if (!item.creator) return { id: 'unknown', nickname: 'Unknown Creator' };

  // If creator is an object
  if (typeof item.creator === 'object' && item.creator !== null) {
    return {
      id: item.creator.id?.toString() || 'unknown',
      nickname: item.creator.nickname || 'Unknown Creator',
    };
  }

  // If creator is just an ID
  return {
    id: item.creator.toString(),
    nickname: 'Unknown Creator',
  };
};

const getCreatorInitials = (nickname: string | undefined): string => {
  if (!nickname) return '?';
  return nickname
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
};

const getCreatorTotal = (creatorItems: any[]): number => {
  return creatorItems.reduce((total, item) => total + getItemTotal(item), 0);
};

// Format creator items for CheckoutModal component
const formatCreatorItemsForCheckout = (creatorItems: any[]) => {
  return creatorItems.map((item) => ({
    id: item.id,
    name: useTranslatedName(item.translations).value, // Get the actual string value
    price: getProductPrice(item),
    quantity: item.quantity,
    image: item.main_image || '',
    // No individual message - will use order-level message from checkout modal
  }));
};

// Store refs for checkout modals
const checkoutModals = ref<Record<string, any>>({});

// Store messages for each creator
const creatorMessages = ref<Record<string, string>>({});

// Open checkout modal for specific creator
const openCheckoutModal = (creatorId: string) => {
  const modalRef = checkoutModals.value[creatorId];
  if (modalRef && modalRef.open) {
    modalRef.open();
  }
};

// Handle checkout success
const handleOrderSuccess = (response: any) => {
  // Remove the ordered items from the cart
  if (response.orderedItemIds && Array.isArray(response.orderedItemIds)) {
    removeItems(response.orderedItemIds);

    // Update local cart items to reflect the removal
    cartItems.value = cartItems.value.filter(item =>
      !response.orderedItemIds.includes(item.id)
    );
  }

  console.log('Order success, items removed from cart:', response);
};

// Handle checkout error
const handleOrderError = (error: any) => {
  // Error handling is already done in CheckoutModal
  console.error('Order error:', error);
};

// Old checkout function removed - now handled by CheckoutModal component
</script>
