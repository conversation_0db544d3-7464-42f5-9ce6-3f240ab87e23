<template>
  <div id="favorites">
    <!-- favorite products -->
    <div
      id="favorite-products"
      class="grid grid-cols-2 gap-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5"
      v-auto-animate
    >
      <div v-for="product in favoriteProducts" :key="product.id" class="relative max-h-96">
        <LazyVerticalProductCard :product="product" class="h-full" />
        <!-- Remove from favorites button -->
        <UButton
          @click="handleRemoveFromFavorites(product.id)"
          icon="i-heroicons-x-mark"
          color="error"
          variant="solid"
          size="sm"
          class="absolute top-2 right-2 z-10"
          :ui="{ base: 'bg-red-500/80 hover:bg-red-600/90' }"
        />
      </div>
    </div>

    <!-- Empty state -->
    <div v-if="!favoriteProducts || favoriteProducts.length === 0" class="py-12 text-center">
      <p class="text-xl text-neutral-400">{{ $t('global.no_favorites_yet') }}</p>
      <UButton :to="$localePath('index')" class="mt-4">
        {{ $t('global.browse_products') }}
      </UButton>
    </div>

    <!-- related products based on favourite product category -->
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  title: 'pages.fan.favorites.title',
  middleware: 'auth',
  layout: 'fan-profile'
});

const { data: favoriteProducts, refresh } = await useFetch('/api/favourites/products');
const { removeFromFavorites } = useFavorites();

// Track which product is being removed for loading state
// const removingProductId = ref<number | null>(null);

// Handle remove from favorites with optimistic update
const handleRemoveFromFavorites = async (productId: number) => {
  try {
    // Call the remove function from composable
    const result = await removeFromFavorites(productId);

    if (result.success) {
      // Refresh the favorites list to reflect the change
      await refresh();
    }
  } catch (error) {
    console.error('Error removing from favorites:', error);
  }
};
</script>
