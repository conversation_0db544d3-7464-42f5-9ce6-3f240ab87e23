<template>
  <div id="my-orders" class="flex flex-col gap-4">
    <!-- Tabs for Ongoing or Completed orders -->
    <div class="flex flex-row gap-4 text-xl font-bold">
      <!-- Should be red if active -->
      <button
        @click="mode = 'ongoing'"
        :active="mode === 'ongoing'"
        class="cursor-pointer"
        :class="mode === 'ongoing' ? 'text-primary' : ''"
      >
        {{ $t('global.ongoing') }}
      </button>
      <button
        @click="mode = 'completed'"
        :active="mode === 'completed'"
        class="cursor-pointer"
        :class="mode === 'completed' ? 'text-primary' : ''"
      >
        {{ $t('global.completed') }}
      </button>
    </div>
    <div id="orders-headers" class="grid grid-cols-6 p-4 text-sm text-neutral-500">
      <p>{{ $t('global.preview_image') }}</p>
      <p>{{ $t('global.product_name') }}</p>
      <p>{{ $t('global.product_category') }}</p>
      <p>{{ $t('global.date_ordered') }}</p>
      <p>{{ $t('global.status') }}</p>
      <p>{{ $t('global.operation') }}</p>
    </div>

    <!-- Empty state -->
    <div v-if="!orders || orders.length === 0" class="flex flex-col items-center justify-center py-16 text-center">
      <UIcon name="heroicons:shopping-bag" class="mb-4 size-16 text-neutral-400" />
      <h3 class="mb-2 text-lg font-semibold text-neutral-300">
        {{ mode === 'ongoing' ? $t('global.no_ongoing_orders') : $t('global.no_completed_orders') }}
      </h3>
      <p class="mb-6 text-neutral-500">
        {{
          mode === 'ongoing' ? $t('global.no_ongoing_orders_description') : $t('global.no_completed_orders_description')
        }}
      </p>
      <NuxtLinkLocale
        :to="{ name: 'index' }"
        class="bg-primary hover:bg-primary/90 inline-flex items-center rounded-lg px-4 py-2 text-sm font-medium text-white"
      >
        <UIcon name="heroicons:shopping-cart" class="mr-2 size-4" />
        {{ $t('global.start_shopping') }}
      </NuxtLinkLocale>
    </div>

    <!-- Orders list -->
    <div v-else>
      <div v-for="order in orders" :key="order.id">
        <div id="order-card" class="mb-6 rounded-lg border border-neutral-700 p-4 grid gap-4">
          <div
            :id="`item-${item.id}`"
            v-for="item in order.order_items"
            :key="item.id"
            class="grid grid-cols-6 items-center gap-4"
          >
            <NuxtLinkLocale :to="{ name: 'fan-orders-id', params: { id: order.id } }" class="col-span-3 md:col-span-1 cursor-pointer">
              <NuxtImg
                :src="directusAssetsUrl(item.product?.main_image as string, 200, 200) ?? '/images/missing-product.png'"
                class="h-32 w-full rounded-lg object-cover"
                placeholder
                placeholder-class="blur-xs"
              />
            </NuxtLinkLocale>
            <div class="col-span-3 md:col-span-1">
              <p>{{ item.product_name_at_order }}</p>
              <!-- IP Name -->
              <p class="text-xs text-neutral-400">{{ useTranslatedName(item.product?.ip?.translations) }}</p>
              <p class="text-xs text-neutral-400">x{{ item.quantity }}</p>
            </div>
            <div class="col-span-2 md:col-span-1 text-sm md:text-md">
              <p>{{ useTranslatedName((item.product?.category as IpCategories).translations) }}</p>
            </div>
            <div class="col-span-2 md:col-span-1 text-sm md:text-md">
              <p>{{ format(new Date(order.date_created!), 'yyyy-MM-dd') }}</p>
            </div>
            <div class="col-span-2 md:col-span-1 text-sm md:text-md">
              <!-- Use shared status color utility -->
              <UBadge
                class="justify-center px-4 capitalize"
                variant="subtle"
                :color="getStatusColor(order.status)"
                >{{ $t(`global.${getOrderStatusKey(order.status)}`) }}</UBadge
              >
            </div>
            <NuxtLinkLocale class="col-span-6 md:col-span-1 flex justify-center md:justify-start" :to="{ name: 'fan-orders-id', params: { id: order.id } }">
              <UIcon name="heroicons:chat-bubble-oval-left" class="size-6" />
              <p class="ml-2 align-middle">{{ $t('global.chat') }}</p>
            </NuxtLinkLocale>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { format } from 'date-fns';
import { getStatusColor } from '~~/shared/utils/ui';
definePageMeta({
  layout: 'fan-profile',
  middleware: 'auth',
  title: 'pages.fan.orders.title',
});
const { directusAssetsUrl } = useDirectus();
const mode = ref('ongoing');

// Helper function to get correct translation key for order status
const getOrderStatusKey = (status: string) => {
  const statusKeyMap: Record<string, string> = {
    pending: 'pending',
    processing: 'not_yet_shipped',
    shipped: 'shipped',
    completed: 'completed',
    cancelled: 'cancelled'
  };
  return statusKeyMap[status] || status;
};

const statusFilter = computed(() => {
  if (mode.value === 'ongoing') {
    return {
      status: {
        _in: ['pending', 'processing', 'shipped'],
      },
    };
  } else if (mode.value === 'completed') {
    return {
      status: {
        _in: ['completed', 'cancelled', 'refunded'],
      },
    };
  } else {
    return {};
  }
});

// Fetch orders with reactive status filtering
const { data: orders } = await useFetch<Orders[]>('/api/orders', {
  query: {
    ...statusFilter,
  },
  key: `my-orders-${mode.value}`,
  watch: [mode],
});
</script>
