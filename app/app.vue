<script setup lang="ts">
import * as locales from '@nuxt/ui/locale';
const appConfig = useAppConfig();
const { t, locale } = useI18n();
const route = useRoute();
const head = useLocaleHead();
const { useDirectusFetch } = useDirectus();

const title = computed(() => `IPGO - ${t((route.meta?.title as string) ?? t('pages.fan.default.title'))}`);

const { data: settings } = await useDirectusFetch<WebsiteSettings>('/items/website_settings');

</script>

<template>
  <UApp :locale="locales[locale as keyof typeof locales]" :toaster="appConfig.toaster">
    <NuxtRouteAnnouncer />
    <NuxtLayout v-if="!settings?.is_fan_maintenance || route.query.bypass === settings.bypass_key">
      <Html :lang="head.htmlAttrs.lang" :dir="head.htmlAttrs.dir">
        <Head>
          <Title>{{ title ?? 'IPGO' }}</Title>
          <template v-for="link in head.link" :key="link.hid">
            <Link :id="link.hid" :rel="link.rel" :href="link.href" :hreflang="link.hreflang" />
          </template>
          <template v-for="meta in head.meta" :key="meta.hid">
            <Meta :id="meta.hid" :property="meta.property" :content="meta.content" />
          </template>
        </Head>

        <Body>
          <NuxtPage />
        </Body>
      </Html>
    </NuxtLayout>
    <NuxtLayout v-else name="fullscreen">
      <Html :lang="head.htmlAttrs.lang" :dir="head.htmlAttrs.dir">
        <Head>
          <Title>{{ title ?? 'IPGO' }}</Title>
          <template v-for="link in head.link" :key="link.hid">
            <Link :id="link.hid" :rel="link.rel" :href="link.href" :hreflang="link.hreflang" />
            </template>
          <template v-for="meta in head.meta" :key="meta.hid">
            <Meta :id="meta.hid" :property="meta.property" :content="meta.content" />
          </template>
        </Head>

        <Body>
          <div class="flex h-screen flex-col items-center justify-center gap-4">
            <h1 class="text-3xl font-bold drop-shadow-xl">{{ $t('global.maintenance') }}</h1>
            <p class="drop-shadow-xl">{{ $t('global.maintenance_message') }}</p>
          </div>
        </Body>
      </Html>
    </NuxtLayout>
  </UApp>
</template>
