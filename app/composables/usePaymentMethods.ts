export default function () {
  // State
  const paymentMethods = useState<any[]>('paymentMethods', () => []);
  const selectedPaymentMethod = useState<any>('selectedPaymentMethod', () => null);
  const defaultPaymentMethodId = ref<string | null>(null);
  const isLoading = ref(false);

  // Fetch payment methods
  const fetchPaymentMethods = async () => {
    console.log('🔄 Fetching payment methods...');

    // Use $fetch instead of useFetch to avoid caching issues
    const data = await $fetch('/api/payment/method/list');

    console.log('📦 Raw API response:', data);

    paymentMethods.value = data?.data || [];
    defaultPaymentMethodId.value = (data as any)?.default_payment_method || null;

    console.log('💳 Payment methods updated:', paymentMethods.value.length, 'methods');
    console.log('🎯 Default payment method ID:', defaultPaymentMethodId.value);

    // Set default at selected if none is selected yet. If no default, set first one
    if (!selectedPaymentMethod.value && defaultPaymentMethodId.value) {
      const defaultPM = paymentMethods.value.find(pm => pm.id === defaultPaymentMethodId.value);
      selectedPaymentMethod.value = defaultPM || null;
      console.log('✅ Set default payment method as selected:', defaultPM?.id);
    } else if (!selectedPaymentMethod.value && paymentMethods.value.length > 0) {
      selectedPaymentMethod.value = paymentMethods.value[0] || null;
      console.log('✅ Set first payment method as selected:', paymentMethods.value[0]?.id);
    }

    return paymentMethods.value;
  };

  // Delete payment method
  const deletePaymentMethod = async (paymentMethodId: string) => {
    await $fetch(`/api/payment/method/${paymentMethodId}`, { method: 'DELETE' });
    
    // Remove from local state
    paymentMethods.value = paymentMethods.value.filter(pm => pm.id !== paymentMethodId);
    
    // Update selected payment method if needed
    if (selectedPaymentMethod.value?.id === paymentMethodId) {
      // Try default payment method first
      const defaultPM = paymentMethods.value.find(pm => pm.id === defaultPaymentMethodId.value);
      selectedPaymentMethod.value = defaultPM || paymentMethods.value[0] || null;
    }
  };

  // Set selected payment method
  const setSelectedPaymentMethod = (paymentMethod: any) => {
    selectedPaymentMethod.value = paymentMethod;
  };
  return {
    paymentMethods,
    selectedPaymentMethod,
    defaultPaymentMethodId,
    isLoading,
    fetchPaymentMethods,
    deletePaymentMethod,
    setSelectedPaymentMethod
  };
}
