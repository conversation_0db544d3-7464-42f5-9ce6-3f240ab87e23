export default function () {
  const filter = useState('fanFilter', () => ({
    productName: '', //keyword search
    status: '',
    availability: '',
    productCategory: '',
    minPrice: '',
    maxPrice: '',
    ipCategory: '',
    creator: {
      label: '',
      value: '',
    },
    searchValue: '',
    page: 1,
    sortBy: 'latest',
  }));
  const resetFilter = () => {
    filter.value = {
      productName: '',
      status: '',
      availability: '',
      productCategory: '',
      minPrice: '',
      maxPrice: '',
      ipCategory: '',
      creator: {
        label: '',
        value: '',
      },
      searchValue: '',
      page: 1,
      sortBy: 'latest',
    };
  };

  const filterApplied = computed(() => {
    const isFilterCleared =
      filter.value.productName === '' &&
      filter.value.status === '' &&
      filter.value.availability === '' &&
      filter.value.productCategory === '' &&
      filter.value.minPrice === '' &&
      filter.value.maxPrice === '' &&
      filter.value.ipCategory === '' &&
      filter.value.creator.value === '' &&
      filter.value.searchValue === '' &&
      filter.value.page === 1 &&
      filter.value.sortBy === 'latest';
    return !isFilterCleared;
  });
  return {
    filter,
    filterApplied,
    resetFilter,
  };
}
