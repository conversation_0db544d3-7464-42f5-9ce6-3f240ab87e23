export default function () {
  const toast = useToast();
  const { t } = useI18n();

  // Add product to favorites
  const addToFavorites = async (productId: number) => {
    try {
      await $fetch('/api/favourites/add', {
        method: 'POST',
        body: { productId },
      });

      toast.add({
        title: t('global.product_added_to_favorites'),
        color: 'success',
        duration: 1000,
      });

      return { success: true };
    } catch (error) {
      console.error('Failed to add to favorites:', error);
      toast.add({
        title: t('global.failed_to_add_favorites'),
        color: 'error',
        duration: 1000,
      });

      return { success: false, error };
    }
  };

  // Remove product from favorites
  const removeFromFavorites = async (productId: number) => {
    try {
      await $fetch('/api/favourites/remove', {
        method: 'POST',
        body: { productId },
      });

      toast.add({
        title: t('global.product_removed_from_favorites'),
        color: 'success',
        duration: 1000,
      });

      return { success: true };
    } catch (error) {
      console.error('Failed to remove from favorites:', error);
      toast.add({
        title: t('global.failed_to_remove_favorites'),
        color: 'error',
      });

      return { success: false, error };
    }
  };

  // Check if product is in favorites
  const checkFavoriteStatus = async (productId: number) => {
    try {
      const response = await $fetch('/api/favourites/check', {
        method: 'POST',
        body: { productId },
      });

      return response.isFavorite;
    } catch (error) {
      console.error('Failed to check favorite status:', error);
      return false;
    }
  };

  return {
    addToFavorites,
    removeFromFavorites,
    checkFavoriteStatus,
  };
}
