export default function () {
  const cart = useCookie<LocalCart>('my-cart', { default: () => ({}) });
  const cartItemCount = computed(() => Object.values(cart.value).reduce((acc, item) => acc + item, 0));

  const addItem = (product: Products, quantity: number = 1) => {
    const existingItem = cart.value[product.id];
    if (existingItem) {
      cart.value[product.id] = (cart.value[product.id] || 0) + quantity;
    } else {
      cart.value[product.id] = quantity;
    }
  };

  const minusItem = (id: number) => {
    const existingItem = cart.value[id];
    if (existingItem) {
      cart.value[id] = (cart.value[id] || 0) - 1;
      if (cart.value[id] === 0) {
        removeItem(id);
      }
    }
  };

  const removeItem = (id: number) => {
    delete cart.value[id];
  };

  // Remove multiple items from cart (for after successful checkout)
  const removeItems = (ids: number[]) => {
    ids.forEach(id => {
      delete cart.value[id];
    });
  };

  // Update item quantity directly
  const updateQuantity = (id: number, quantity: number) => {
    if (quantity <= 0) {
      removeItem(id);
    } else {
      cart.value[id] = quantity;
    }
  };

  const total = 1;

  return {
    cart,
    cartItemCount,
    addItem,
    minusItem,
    removeItem,
    removeItems,
    updateQuantity,
    total,
  };
}
