/**
 * Composable for creator product management
 * Handles common product operations like publish/unpublish, delete, and status checks
 */

export default function useCreatorProduct() {
  const { useDirectusFetch, directusAssetsUrl } = useDirectus();
  const toast = useToast();

  /**
   * Check if product status is approved (approved, published, or unpublished)
   */
  const isProductApproved = (status: string | undefined): boolean => {
    return ['approved', 'published', 'unpublished'].includes(status || '');
  };

  /**
   * Calculate average rating from product reviews
   */
  const calculateAverageRating = (reviews: any[]): number => {
    if (!reviews || reviews.length === 0) return 0;
    
    // Parse ratings to numbers to ensure proper numeric addition (ratings come as strings from DB)
    const totalRating = reviews.reduce((acc, review: any) => {
      const rating = parseFloat(review.rating) || 0;
      return acc + rating;
    }, 0);
    
    return Math.round((totalRating / reviews.length) * 10) / 10; // Round to 1 decimal place
  };

  /**
   * Toggle product publish status (published <-> unpublished)
   */
  const toggleProductPublish = async (productId: string, currentStatus: string, refreshCallback?: () => Promise<void>) => {
    const isCurrentlyPublished = currentStatus === 'published';
    
    try {
      await useDirectusFetch(`/items/products/${productId}`, {
        method: 'PATCH',
        body: {
          status: isCurrentlyPublished ? 'unpublished' : 'published',
        },
      });
      
      toast.add({
        title: isCurrentlyPublished ? 'Product unpublished successfully' : 'Product published successfully',
        color: 'success',
      });
      
      // Call refresh callback if provided
      if (refreshCallback) {
        await refreshCallback();
      }
      
      return { success: true };
    } catch (error) {
      toast.add({
        title: isCurrentlyPublished ? 'Failed to unpublish product' : 'Failed to publish product',
        color: 'error',
      });
      console.error('Failed to toggle publish:', error);
      return { success: false, error };
    }
  };

  /**
   * Delete product (archive it)
   */
  const deleteProduct = async (productId: string, redirectPath?: string) => {
    try {
      await useDirectusFetch(`/items/products/${productId}`, {
        method: 'PATCH',
        body: {
          status: 'archived',
        },
      });
      
      toast.add({
        title: 'Product deleted successfully',
        color: 'success',
      });
      
      // Navigate to redirect path if provided
      if (redirectPath) {
        await navigateTo(redirectPath);
      }
      
      return { success: true };
    } catch (error) {
      toast.add({
        title: 'Failed to delete product',
        color: 'error',
      });
      console.error('Failed to delete product:', error);
      return { success: false, error };
    }
  };

  /**
   * Product gallery management
   */
  const useProductGallery = (product: Ref<Products | null | undefined>) => {
    const selectedImageIndex = ref(0);
    const imageIndexRef = ref(0);
    const lightboxVisible = ref(false);

    // Computed property for the currently selected image
    const selectedImage = computed(() => {
      if (!product.value?.images || product.value.images.length === 0) {
        return '/images/missing-product.png';
      }

      const selectedImg = product.value.images[selectedImageIndex.value];
      return directusAssetsUrl(selectedImg.directus_files_id as string, 600, 600) ?? '/images/missing-product.png';
    });

    // All images for lightbox
    const allImages = computed(() => {
      return product.value?.images.map((img: any) => directusAssetsUrl(img.directus_files_id as string)) ?? [];
    });

    // Function to open lightbox
    const openLightbox = (index?: number) => {
      lightboxVisible.value = true;
      imageIndexRef.value = index ?? selectedImageIndex.value;
    };

    return {
      selectedImageIndex,
      imageIndexRef,
      lightboxVisible,
      selectedImage,
      allImages,
      openLightbox,
    };
  };

  return {
    isProductApproved,
    calculateAverageRating,
    toggleProductPublish,
    deleteProduct,
    useProductGallery,
  };
}
