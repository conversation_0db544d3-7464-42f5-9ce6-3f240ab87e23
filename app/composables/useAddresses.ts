export default function () {
  const { profile } = useUser();
  const { useDirectusFetch } = useDirectus();

  // State
  const addresses = useState<Addresses[]>('addresses', () => []);
  const selectedAddress = useState<Addresses | null>('selectedAddress', () => null);
  const isLoading = ref(false);

  // Fetch addresses
  const fetchAddresses = async () => {
    const { data } = await useFetch('/api/addresses/list', {
      key: 'shipping-addresses',
    });

    addresses.value = data.value?.data || [];
    const defaultAddressId = data.value?.default_address;

    // Set default at selected if none is selected yet. If no default, set first one
    if (!selectedAddress.value && addresses.value.length > 0) {
      const defaultAddr = addresses.value.find(addr => addr.id === defaultAddressId);
      selectedAddress.value = defaultAddr || addresses.value[0] || null;
    }

    return addresses.value;
  };

  // Delete address
  const deleteAddress = async (addressId: number) => {
    await useDirectusFetch(`/items/addresses/${addressId}`, { method: 'DELETE' });
    
    // Remove from local state
    addresses.value = addresses.value.filter(addr => addr.id !== addressId);
    
    // Update selected address if needed
    if (selectedAddress.value?.id === addressId) {
      // Try default address first
      const defaultAddr = addresses.value.find(addr => addr.id === profile.value?.default_address);
      selectedAddress.value = defaultAddr || addresses.value[0] || null;
    }
  };

  // Set selected address
  const setSelectedAddress = (address: Addresses | null) => {
    selectedAddress.value = address;
  };

  return {
    addresses,
    selectedAddress,
    isLoading,
    fetchAddresses,
    deleteAddress,
    setSelectedAddress
  };
}
