/**
 * Composable for accessing IP categories with i18n support
 * 
 * This composable provides access to the globally provided IP categories map
 * that contains translated category names based on the current locale.
 */

export default function () {
  const { useDirectusFetch } = useDirectus();
  const { locale } = useI18n();
  const 
  // Get ip categories
  const { data: ipCategories } = useDirectusFetch('/items/ip_categories', {
    params: {
      fields: ['code, translations.name'],
    },
    dedupe: 'defer'
  });

  //
  const getIpCategoryName = (category: IpCategories, locale: string) => {
    return useTranslation(category.translations, 'name', locale);
  };
  
  return {
    ipCategories
  };
};
