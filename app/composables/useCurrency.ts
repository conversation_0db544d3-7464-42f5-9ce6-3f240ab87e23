/**
 * Composable for currency handling with locale support
 * 
 * This composable provides utilities for displaying prices in the user's locale currency
 * and formatting them appropriately.
 */

import { 
  getProductPriceByLocale, 
  getCurrencyByLocale, 
  currencyPriceFormatter,
  formatProductCurrentPriceByLocale,
  formatProductDiscountedPriceByLocale,
  hasProductDiscount
} from '~/utils/formatter';

export default function useCurrency() {
  const { locale } = useI18n();

  /**
   * Get the currency code for the current locale
   */
  const currentCurrency = computed(() => getCurrencyByLocale(locale.value));

  /**
   * Format a price amount in the current locale's currency
   */
  const formatPrice = (amount: number, currency?: string) => {
    const currencyCode = currency || currentCurrency.value;
    return currencyPriceFormatter(amount, currencyCode);
  };

  /**
   * Get a product's price in the current locale's currency
   */
  const getProductPrice = (product: Products) => {
    return getProductPriceByLocale(product, locale.value);
  };

  /**
   * Format a product's current price in the current locale's currency
   */
  const formatProductPrice = (product: Products) => {
    return formatProductCurrentPriceByLocale(product, locale.value);
  };

  /**
   * Format a product's discounted price in the current locale's currency
   */
  const formatProductDiscountPrice = (product: Products) => {
    return formatProductDiscountedPriceByLocale(product, locale.value);
  };

  /**
   * Check if a product has a discount
   */
  const productHasDiscount = (product: Products) => {
    return hasProductDiscount(product);
  };

  return {
    currentCurrency,
    formatPrice,
    getProductPrice,
    formatProductPrice,
    formatProductDiscountPrice,
    productHasDiscount,
  };
}
