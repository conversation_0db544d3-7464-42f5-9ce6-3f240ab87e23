import type { UseFetchOptions } from '#app';
import { defu } from 'defu';
import { createFetch } from 'ofetch';
import type { ProductsTranslations, IpTranslations, Languages } from '../../shared/types/directus';

export default function () {
  const config = useRuntimeConfig();
  const userToken = useCookie('access_token');
  const { locale } = useI18n();

  // Assets URL with optional width and height parameters
  const directusAssetsUrl = (id: string, width?: number, height?: number) => {
    if (!id) return undefined;
    if (width && height) {
      return `${config.public.directus.baseUrl}/assets/${id}?width=${width}&height=${height}`;
    }
    return `${config.public.directus.baseUrl}/assets/${id}`;
  };

  // Mapping from i18n locale codes to Directus language IDs
  const localeToLanguageId = (localeCode: string): number => {
    switch (localeCode) {
      case 'en':
        return 1; // English
      case 'jp':
        return 2; // Japanese
      case 'kr':
        return 3; // Korean
      default:
        return 1; // Default to English
    }
  };

  // Helper to extract language ID from languages_id field (handles both number and Languages object)
  const getLanguageId = (languagesId: number | Languages | null | undefined): number | null => {
    if (typeof languagesId === 'number') {
      return languagesId;
    }
    if (languagesId && typeof languagesId === 'object' && 'id' in languagesId) {
      return languagesId.id;
    }
    return null;
  };

  // Generic reactive translation helper
  const getTranslation = <
    T extends { languages_id?: number | Languages | null; name?: string | null; description?: string | null },
  >(
    translations: T[],
    field: keyof T = 'name' as keyof T,
    fallbackLocale: string = 'en',
  ) => {
    return computed(() => {
      if (!translations || translations.length === 0) {
        return '';
      }

      const currentLanguageId = localeToLanguageId(locale.value);

      // Try to find translation for current language
      let translation = translations.find((t) => getLanguageId(t.languages_id) === currentLanguageId);

      // If not found, try fallback language
      if (!translation || !translation[field]) {
        const fallbackLanguageId = localeToLanguageId(fallbackLocale);
        translation = translations.find((t) => getLanguageId(t.languages_id) === fallbackLanguageId);
      }

      // If still not found, get first available translation
      if (!translation || !translation[field]) {
        translation = translations.find((t) => t[field]);
      }

      return (translation?.[field] as string) || '';
    });
  };

  // Specific helpers for common translation types
  const getProductName = (translations: ProductsTranslations[]) => {
    return getTranslation(translations, 'name');
  };

  const getProductDescription = (translations: ProductsTranslations[]) => {
    return getTranslation(translations, 'description');
  };

  const getIpName = (translations: IpTranslations[]) => {
    return getTranslation(translations, 'name');
  };

  const getIpDescription = (translations: IpTranslations[]) => {
    return getTranslation(translations, 'description');
  };

  // Equivalent to useFetch
  const useDirectusFetch = <T>(url: string | (() => string), options: UseFetchOptions<T> = {}) => {
    const defaults: UseFetchOptions<T> = {
      baseURL: config.public.directus.baseUrl,
      headers:
        userToken.value != null
          ? {
              Authorization: `Bearer ${userToken.value}`,
            }
          : undefined,
      onResponse(_ctx) {
        _ctx.response._data = _ctx.response._data.data;
      },
      dedupe: 'defer'
    };

    const params = defu(options, defaults);

    return useFetch(url, params);
  };

  // Equivalent to $fetch
  const dFetch = createFetch({
    defaults: {
      baseURL: config.public.directus.baseUrl,
      headers:
        userToken.value != null
          ? {
              Authorization: `Bearer ${userToken.value}`,
            }
          : undefined,
      onResponse(_ctx) {
        _ctx.response._data = _ctx.response._data.data;
      },
    },
  });

  return {
    useDirectusFetch,
    dFetch,
    directusAssetsUrl,
    getTranslation,
    getProductName,
    getProductDescription,
    getIpName,
    getIpDescription,
  };
}
