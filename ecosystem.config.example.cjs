module.exports = {
  apps: [
    {
      name: 'ipgo-nuxt',
      port: '3000',
      exec_mode: 'cluster',
      instances: 'max',
      script: './.output/server/index.mjs',
      args: '',
      cwd: './',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        NITRO_PORT: 3000,
        NITRO_HOST: '0.0.0.0',
        PATH: process.env.PATH, // Inherit PATH to ensure NVM node is found
        
        // Nuxt UI Pro License
        NUXT_UI_PRO_LICENSE: 'your_nuxt_ui_pro_license',
        
        // AWS Configuration for SES
        NUXT_AWS_REGION: 'ap-northeast-1',
        NUXT_AWS_ACCESS_KEY_ID: 'your_aws_access_key_id',
        NUXT_AWS_SECRET_ACCESS_KEY: 'your_aws_secret_access_key',
        
        // Email Configuration
        NUXT_EMAIL_DEFAULT_FROM: '<EMAIL>',
        
        // Site Configuration
        NUXT_PUBLIC_DOMAIN_URL: 'https://yourdomain.com',
        
        // Directus Configuration
        NUXT_DIRECTUS_ADMIN_KEY: 'your_directus_admin_key',
        NUXT_PUBLIC_DIRECTUS_BASE_URL: 'https://your-directus-instance.com',
        
        // Stripe Configuration
        NUXT_STRIPE_KEY: 'your_stripe_secret_key',
        NUXT_STRIPE_PUBLIC_KEY: 'your_stripe_public_key',
        
        // Legacy API
        NUXT_OLD_BACKEND_API: 'https://your-backend-api.com'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
        NITRO_PORT: 3000,
        NITRO_HOST: '0.0.0.0'
      },
      // Logging
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',

      // Advanced PM2 features
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',

      // Graceful shutdown
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,

      // Health monitoring
      min_uptime: '10s',
      max_restarts: 10,

      // Source map support for better error tracking
      source_map_support: true,
    }
  ]
};
