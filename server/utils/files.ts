export const uploadBase64ToDirectus = async (base64: string, filename?: string, folder?: string) => {
  // convert base64 and do multipart file upload for directus
  if (base64 && base64.startsWith('data:')) {
    // Extract MIME type and base64 data from the data URL
    const [mimeTypePart, base64Data] = base64.split(',');
    const mimeType = mimeTypePart.split(':')[1].split(';')[0]; // Extract MIME type (e.g., 'application/pdf', 'image/jpeg')
    const buffer = Buffer.from(base64Data, 'base64');

    // Create a Blob from the buffer with correct MIME type
    const blob = new Blob([buffer], { type: mimeType });

    // Create FormData for multipart upload
    const formData = new FormData();
    formData.append('title', filename ?? crypto.randomUUID());
    formData.append('folder', folder ?? 'e15ab896-96c2-4886-9071-c93e0529ff78');
    formData.append('filename_download', filename ?? crypto.randomUUID());
    formData.append('file', blob);

    // Upload to Directus
    const fileRes = await elevatedFetch<BackendResponse<DirectusFiles>>('/files', {
      method: 'POST',
      body: formData,
    });
    return fileRes.data;
  }
};
