/**
 * Email template utilities for common email types
 */

interface WelcomeEmailData {
  name: string;
  loginUrl?: string;
}

interface VerificationEmailData {
  email: string;
  mode: 'signup' | 'recover';
  verificationCode: string;
  expiresIn?: string;
  role?: 'fan' | 'creator' | 'owner';
}

interface ProductApplicationEmailData {
  ipName?: string;
  creatorUsername?: string;
  creatorEmail?: string;
  productName?: string;
  productDescription?: string;
  productCategory?: string;
  applicationType?: string;
  productImages?: string[];
  price?: string;
  currency?: string;
  totalStock?: number;
}

/**
 * Generate welcome email HTML template
 */
export const generateWelcomeEmail = (data: WelcomeEmailData): string => {
  const { name, loginUrl = '#' } = data;

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Welcome to IPGO</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #3b82f6, #8b5cf6); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Welcome to IPGO!</h1>
        </div>
        <div class="content">
          <h2>Hello!</h2>
          <p>Welcome to IPGO, your gateway to amazing intellectual property content and products.</p>
          <p>We're excited to have you join our community of creators and fans.</p>
          <a href="${loginUrl}" class="button">Get Started</a>
          <p>If you have any questions, feel free to reach out to our support team.</p>
        </div>
        <div class="footer">
          <p>&copy; ${new Date().getFullYear()} IPGO. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
};

/**
 * Generate verification email HTML template
 */
export const generateVerificationEmail = (data: VerificationEmailData): string => {
  const { email, verificationCode, mode, expiresIn = '10 minutes', role = 'fan' } = data;

  // Get domain URL from runtime config
  const config = useRuntimeConfig();
  const domainUrl = (config as any).public?.domainUrl || 'http://localhost:3000';

  // Generate verification URL using the verification code
  const verificationUrl = () => {
    if (mode === 'signup') {
      // Generate role-specific signup URLs
      switch (role) {
        case 'creator':
          return `${domainUrl}/creator/signup?code=${verificationCode}&email=${email}`;
        case 'owner':
          return `${domainUrl}/owner/signup?code=${verificationCode}&email=${email}`;
        case 'fan':
        default:
          return `${domainUrl}/signup?code=${verificationCode}&email=${email}`;
      }
    }
    if (mode === 'recover') {
      return `${domainUrl}/recover-password?code=${verificationCode}&email=${email}`;
    }
    return '';
  };

  return `
  <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Email Verification</title>
  <style>
    a {
      text-decoration: none !important;
      color: #ffffff !important;
    }
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f6f6f6;
      color: #333;
    }
    .container {
      max-width: 600px;
      margin: 40px auto;
      background-color: #ffffff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .header {
      background-color: #f8f9fa;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      margin: 0;
      font-size: 24px;
      color: #333;
    }
    .content {
      padding: 40px;
      text-align: center;
    }
    .content p {
      font-size: 16px;
      line-height: 1.5;
      margin: 0 0 20px;
    }
    .code {
      font-size: 32px;
      font-weight: bold;
      letter-spacing: 5px;
      color: #333;
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      display: inline-block;
      margin: 20px 0;
    }
    .cta-button {
      display: inline-block;
      padding: 12px 24px;
      background-color: #ff0055;
      color: #ffffff;
      text-decoration: none;
      font-size: 16px;
      font-weight: 500;
      border-radius: 5px;
      margin: 20px 0;
    }
    .cta-button:hover {
      background-color: #be003f;
    }
    .footer {
      padding: 20px;
      text-align: center;
      font-size: 14px;
      color: #777;
      background-color: #f8f9fa;
    }
    .footer p {
      margin: 0;
    }
    @media only screen and (max-width: 600px) {
      .container {
        margin: 20px;
      }
      .content {
        padding: 20px;
      }
      .code {
        font-size: 24px;
        letter-spacing: 3px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>${mode == 'signup' ? 'Verify Your Email' : 'Reset Your Password'}</h1>
    </div>
    <div class="content">
      <p>Please verify your email address to continue. Click the button below or use the code provided.</p>
      <a href="${verificationUrl()}" class="cta-button">${mode == 'signup' ? 'Verify Email Address' : 'Reset Password'}</a>
      <p>If you can't click the button, use this verification code:</p>
      <div class="code">${verificationCode}</div>
      <p>This code is valid for the next ${expiresIn ?? '10 minutes'}.</p>
    </div>
    <div class="footer">
      <p>If you did not request this, please ignore this email.</p>
    </div>
  </div>
</body>
</html>
  `;
};

/**
 * Generate product application email HTML template
 */
export const generateProductApplicationEmail = (data: ProductApplicationEmailData): string => {
  const {
    ipName = 'N/A',
    creatorUsername = 'N/A',
    creatorEmail = 'N/A',
    productName = 'N/A',
    productDescription = 'N/A',
    productCategory = 'N/A',
    applicationType = 'N/A',
    productImages = [],
    price = 'N/A',
    currency = '',
    totalStock = 0,
  } = data;

  // Generate image tags for product images
  const generateImageTags = (images: string[]): string => {
    if (!images || images.length === 0) {
      return '<p>No images provided</p>';
    }

    return images
      .map(
        (imagePath) =>
          `<img src="${imagePath}" alt="Product Image" style="max-width: 200px; margin: 5px; border-radius: 4px;">`,
      )
      .join('<br>');
  };

  return `
  <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>New Product Application</title>
  <style>
    a {
      text-decoration: none !important;
      color: #ffffff !important;
    }
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f6f6f6;
      color: #333;
    }
    .container {
      max-width: 600px;
      margin: 40px auto;
      background-color: #ffffff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    .header h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
    .content {
      padding: 40px 30px;
      line-height: 1.6;
    }
    .content h2 {
      color: #333;
      font-size: 20px;
      margin-bottom: 20px;
      border-bottom: 2px solid #667eea;
      padding-bottom: 10px;
    }
    .info-row {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
      border-left: 4px solid #667eea;
    }
    .info-label {
      font-weight: 600;
      color: #555;
      display: inline-block;
      min-width: 150px;
    }
    .info-value {
      color: #333;
    }
    .images-section {
      margin-top: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 4px;
    }
    .images-section img {
      max-width: 200px;
      margin: 5px;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .footer {
      background-color: #f8f9fa;
      padding: 20px 30px;
      text-align: center;
      color: #666;
      font-size: 14px;
    }
    .price-highlight {
      font-size: 18px;
      font-weight: 600;
      color: #667eea;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>New Product Application Submitted</h1>
    </div>
    <div class="content">
      <h2>Application Details</h2>

      <div class="info-row">
        <span class="info-label">IP:</span>
        <span class="info-value">${ipName}</span>
      </div>

      <div class="info-row">
        <span class="info-label">Creator's Username:</span>
        <span class="info-value">${creatorUsername}</span>
      </div>

      <div class="info-row">
        <span class="info-label">Creator's Email:</span>
        <span class="info-value">${creatorEmail}</span>
      </div>

      <div class="info-row">
        <span class="info-label">Product Name:</span>
        <span class="info-value">${productName}</span>
      </div>

      <div class="info-row">
        <span class="info-label">Product Description:</span>
        <span class="info-value">${productDescription}</span>
      </div>

      <div class="info-row">
        <span class="info-label">Product Category:</span>
        <span class="info-value">${productCategory}</span>
      </div>

      <div class="info-row">
        <span class="info-label">Application Type:</span>
        <span class="info-value">${applicationType}</span>
      </div>

      <div class="info-row">
        <span class="info-label">Price:</span>
        <span class="info-value price-highlight">${currency}${price}</span>
      </div>

      <div class="info-row">
        <span class="info-label">Total Stock:</span>
        <span class="info-value">${totalStock}</span>
      </div>

      <div class="images-section">
        <div class="info-label">Design / Images:</div>
        <div style="margin-top: 10px;">
          ${generateImageTags(productImages)}
        </div>
      </div>
    </div>
    <div class="footer">
      <p>This is an automated notification for a new product application submission.</p>
    </div>
  </div>
</body>
</html>
  `;
};
