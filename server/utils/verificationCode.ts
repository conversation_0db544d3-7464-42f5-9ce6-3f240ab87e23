// should be 6 char long mix of numbers, symbols and char. dont use a charset
export const createVerificationCode = (length = 6): string => {
  const chars = [];

  // Generate characters of each type
  for (let i = 0; i < length; i++) {
    const type = Math.floor(Math.random() * 2); // 0: number, 1: letter

    if (type === 0) {
      // Add a random number (0-9)
      chars.push(Math.floor(Math.random() * 10).toString());
    } else if (type === 1) {
      // Add a random letter (A-Z or a-z)
      const isUpperCase = Math.random() > 0.5;
      const charCode = isUpperCase
        ? 65 + Math.floor(Math.random() * 26) // A-Z
        : 97 + Math.floor(Math.random() * 26); // a-z
      chars.push(String.fromCharCode(charCode));
    }
  }

  // Shuffle the array to randomize order
  for (let i = chars.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [chars[i], chars[j]] = [chars[j], chars[i]];
  }

  return chars.join('');
};
