import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';

interface EmailOptions {
  to: string | string[];
  subject: string;
  body: string;
  isHtml?: boolean;
  from?: string;
  replyTo?: string;
}

// Initialize SES client
const createSESClient = () => {
  const config = useRuntimeConfig();

  return new SESClient({
    region: (config as any).aws?.region || 'ap-northeast-1',
    credentials: {
      accessKeyId: (config as any).aws?.accessKeyId || '',
      secretAccessKey: (config as any).aws?.secretAccessKey || '',
    },
  });
};

/**
 * Send email using AWS SES
 * @param options - Email configuration options
 * @returns Promise with the result of the email send operation
 */
export const sendEmail = async (options: EmailOptions) => {
  const { to, subject, body, isHtml = false, from, replyTo } = options;

  const config = useRuntimeConfig();
  const defaultFrom = (config as any).email?.defaultFrom || '<EMAIL>';

  try {
    const sesClient = createSESClient();

    // Prepare destination addresses
    const destinations = Array.isArray(to) ? to : [to];

    const command = new SendEmailCommand({
      Source: from || defaultFrom,
      Destination: {
        ToAddresses: destinations,
      },
      Message: {
        Subject: {
          Data: subject,
          Charset: 'UTF-8',
        },
        Body: isHtml
          ? {
              Html: {
                Data: body,
                Charset: 'UTF-8',
              },
            }
          : {
              Text: {
                Data: body,
                Charset: 'UTF-8',
              },
            },
      },
      ReplyToAddresses: replyTo ? [replyTo] : undefined,
    });

    const result = await sesClient.send(command);

    return {
      success: true,
      messageId: result.MessageId,
      data: result,
    };
  } catch (error) {
    console.error('Failed to send email:', error);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

/**
 * Simple email sender (backward compatible)
 * @param email - Recipient email address
 * @param subject - Email subject
 * @param body - Email body (text)
 * @returns Promise with the result
 */
export const sendSimpleEmail = async (email: string | string[], subject: string, body: string) => {
  return sendEmail({
    to: email,
    subject,
    body,
    isHtml: false,
  });
};

/**
 * Send HTML email
 * @param email - Recipient email address
 * @param subject - Email subject
 * @param htmlBody - HTML email body
 * @returns Promise with the result
 */
export const sendHtmlEmail = async (email: string | string[], subject: string, htmlBody: string) => {
  return sendEmail({
    to: email,
    subject,
    body: htmlBody,
    isHtml: true,
  });
};
