import type { LocalCart } from '../../../shared/types/cart';

export default defineEventHandler(async (event) => {
  const body = await readBody<LocalCart>(event);

  // Track items that had quantities adjusted due to stock limitations
  const adjustedItems: Array<{
    id: number;
    name: string;
    requestedQuantity: number;
    availableQuantity: number;
  }> = [];

  // Get each of the items from directus and validate stock
  const items = await Promise.all(
    Object.keys(body).map(async (key) => {
      const product = await dFetch(`/items/products/${key}`, {
        params: {
          fields: [
            'id',
            'main_image',
            'translations.*',
            'price',
            'price_jpy',
            'price_krw',
            'base_currency',
            'creator.id',
            'creator.nickname',
            'creator.avatar',
            'stock_remaining',
            'stock_total',
          ],
        },
      });

      const requestedQuantity = body[Number(key)];
      let finalQuantity = requestedQuantity;

      // Check stock availability and adjust quantity if necessary
      if (
        product.data.stock_remaining !== null &&
        product.data.stock_remaining !== undefined &&
        requestedQuantity > product.data.stock_remaining
      ) {
        finalQuantity = Math.max(0, product.data.stock_remaining);

        // Track this adjustment
        adjustedItems.push({
          id: product.data.id,
          name: product.data.translations?.[0]?.name || `Product ${product.data.id}`,
          requestedQuantity,
          availableQuantity: finalQuantity,
        });
      }

      return {
        ...product.data,
        quantity: finalQuantity,
        originalQuantity: requestedQuantity, // Keep track of what was originally requested
        wasAdjusted: finalQuantity !== requestedQuantity,
      };
    }),
  );

  // Filter out items with zero quantity (completely out of stock)
  const availableItems = items.filter(item => item.quantity > 0);

  // Calculate total price using adjusted quantities
  const total = availableItems.reduce((acc, item) => acc + item.price * item.quantity, 0);

  // return updated cart with total price, detailed items, and adjustment info
  return {
    total,
    items: availableItems,
    adjustedItems: adjustedItems || [], // Ensure this is always an array
    hasAdjustments: adjustedItems.length > 0,
  };
});
