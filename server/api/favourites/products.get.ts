interface Favourites {
  favorites: { products_id: number }[];
}

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  try {
    const data = await dFetch<BackendResponse<Favourites>>('/users/me', {
      headers: {
        Authorization: 'Bearer ' + accessToken,
      },
      params: {
        fields: ['favorites.products_id'],
      },
    });

    const products = await dFetch<BackendResponse<Products[]>>('/items/products', {
      params: {
        fields: [
          'id',
          'translations.*',
          'price',
          'price_jpy',
          'price_krw',
          'base_currency',
          'discount',
          'main_image',
          'category.translations.*',
          'creator.nickname',
          'creator.id',
          'user_created.avatar',
        ],
        filter: {
          id: {
            _in: data.data.favorites.map((fav: any) => fav.products_id),
          },
        },
      },
    });

    return products.data;
  } catch (error) {
    console.error('Failed to fetch user data:', error);
    // delete access_token
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }
});
