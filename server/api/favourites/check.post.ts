import { z } from 'zod/v4';

const schema = z.object({
  productId: z.coerce.number(),
});

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const params = await readValidatedBody(event, schema.parse);

  const myData = await dFetch<BackendResponse<any>>('/users/me', {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    params: {
      fields: ['favorites.id, favorites.products_id'],
    },
  });

  // check if product id is in favorites and return the id
  const fav = myData.data.favorites.find((fav: any) => fav.products_id === params.productId);

  return fav;
});
