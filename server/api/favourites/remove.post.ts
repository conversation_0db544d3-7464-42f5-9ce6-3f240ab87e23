import { z } from 'zod/v4';

const schema = z.object({
  productId: z.number(),
});

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const body = await readValidatedBody(event, schema.parse);

  // Get current user ID first
  const me = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    params: {
      fields: ['id'],
    },
  });

  const favList = await dFetch<BackendResponse<JunctionDirectusUsersProducts[]>>(
    '/items/junction_directus_users_products',
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        filter: {
          products_id: {
            _eq: body.productId,
          },
          directus_users_id: {
            _eq: me.data.id,
          },
        },
      },
    },
  );

  if (!favList.data[0]) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Favorite not found',
    });
  }
  const favId = favList.data[0].id;

  try {
    // Delete the favorite
    await dFetch<BackendResponse<any>>(`/items/junction_directus_users_products/${favId}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    // Return 204 No Content for successful deletion
    setResponseStatus(event, 204);
  } catch (error) {
    console.error('Failed to remove from favorites:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to remove from favorites',
    });
  }
});
