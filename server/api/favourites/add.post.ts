import { z } from 'zod/v4';

const schema = z.object({
  productId: z.coerce.number(),
});

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const body = await readValidatedBody(event, schema.parse);

  const me = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
    headers: {
      Authorization: `Bear<PERSON> ${accessToken}`,
    },
    params: {
      fields: ['id'],
    },
  });

  try {
    await dFetch<BackendResponse<any>>('/items/junction_directus_users_products', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      body: {
        products_id: body.productId,
        directus_users_id: me.data.id,
      },
    });

    // Return 201 Created for successful creation
    setResponseStatus(event, 201);
  } catch (error) {
    console.error('Failed to add to favorites:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to add to favorites',
    });
  }
});
