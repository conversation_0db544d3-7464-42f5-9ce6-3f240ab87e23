interface Favourites {
  favorites: { products_id: number }[];
}

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  try {
    const data = await dFetch<BackendResponse<Favourites>>('/users/me', {
      headers: {
        Authorization: 'Bearer ' + accessToken,
      },
      params: {
        fields: ['favorites.products_id'],
      },
    });
    return data.data.favorites.map((fav: any) => fav.products_id);
  } catch (error) {
    console.error('Failed to fetch user data:', error);
    // delete access_token
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }
});
