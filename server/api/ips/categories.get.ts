// we need to get just the IPCategories that have IPs
export default defineEventHandler(async () => {
  const ips = await dFetch<BackendResponse<Ip[]>>('/items/ip', {
    params: {
      limit: 1000,
      fields: ['category'],
      filter: {
        category: {
          _nnull: true,
        },
      },
    },
  });
  const populatedIpCategories = ips.data.map((ip: any) => ip.category);
  // unique only
  const uniqueIpCategories = [...new Set(populatedIpCategories)];

  return Array.from(new Set(uniqueIpCategories));
});
