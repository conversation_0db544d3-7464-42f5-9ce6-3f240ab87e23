import { z } from 'zod/v4';
import { useServerStripe } from "#stripe/server";

// Schema for order creation from payment
const createOrderFromPaymentSchema = z.object({
  payment_intent_id: z.string().min(1),
});

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const body = await readValidatedBody(event, createOrderFromPaymentSchema.parse);

  try {
    // Initialize Stripe to retrieve payment intent
    const stripe = await useServerStripe(event);
    
    // Retrieve payment intent to verify it's paid and get metadata
    const paymentIntent = await stripe.paymentIntents.retrieve(body.payment_intent_id);

    if (paymentIntent.status !== 'succeeded') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Payment has not been completed successfully',
      });
    }

    // Extract order data from payment intent metadata
    const userId = paymentIntent.metadata.user_id;
    const creatorId = paymentIntent.metadata.creator_id;
    const shippingAddressId = paymentIntent.metadata.shipping_address_id;
    const orderItems = JSON.parse(paymentIntent.metadata.order_items);

    if (!userId || !creatorId || !orderItems) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid payment intent metadata',
      });
    }

    // Get current user
    const currentUser = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: ['id', 'first_name', 'last_name', 'nickname'],
      },
    });

    // Fetch the actual shipping address if provided
    let shippingAddress = null;
    if (shippingAddressId && shippingAddressId !== '') {
      try {
        const addressResponse = await dFetch<BackendResponse<Addresses>>(`/items/addresses/${shippingAddressId}`, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });
        shippingAddress = addressResponse.data;
      } catch (error) {
        console.warn('Failed to fetch shipping address:', error);
      }
    }

    // Verify the payment intent belongs to the current user
    if (currentUser.data.id !== userId) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Payment intent does not belong to current user',
      });
    }

    // Check if order already exists for this payment intent
    const existingOrderResponse = await dFetch<BackendResponse<Orders[]>>('/items/orders', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        filter: {
          payment_id: {
            _eq: body.payment_intent_id,
          },
        },
        limit: 1,
      },
    });

    if (existingOrderResponse.data && existingOrderResponse.data.length > 0) {
      // Order already exists, return existing order
      return {
        success: true,
        orderId: existingOrderResponse.data[0].id,
        message: 'Order already exists for this payment',
        existing: true,
      };
    }

    // Fetch product details for order creation
    const productIds = orderItems.map((item: any) => item.id);
    const productsResponse = await dFetch<BackendResponse<Products[]>>('/items/products', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        filter: {
          id: {
            _in: productIds,
          },
        },
        fields: [
          'id',
          'creator',
          'stock_remaining',
          'translations.name',
          'price',
          'price_jpy',
          'price_krw',
          'base_currency',
        ],
      },
    });

    const products = productsResponse.data;

    // Calculate totals using the same logic as original order submission
    let totalAmount = 0;
    let baseTotalAmount = 0;

    // Determine currency from the first product's base_currency
    const firstProduct = products.find((p) => p.id === orderItems[0]?.id);
    const currency = firstProduct?.base_currency || 'USD';

    orderItems.forEach((item: any) => {
      const product = products.find((p) => p.id === item.id);
      if (!product) return;

      // Use product pricing data from database with proper fallbacks
      const itemPrice =
        product.base_currency === 'USD'
          ? Number(product.price) || 0
          : product.base_currency === 'JPY'
            ? Number(product.price_jpy) || Number(product.price) || 0
            : Number(product.price_krw) || Number(product.price) || 0;

      totalAmount += itemPrice * item.quantity;
      baseTotalAmount += (Number(product.price) || 0) * item.quantity; // Always use USD as base
    });

    // Prepare shipping address snapshot from selected address or user profile
    const userData = currentUser.data;
    const addressData = shippingAddress as any || {};

    const shippingName = addressData.name ||
      userData.nickname ||
      `${userData.first_name || ''} ${userData.last_name || ''}`.trim() ||
      'Unknown';

    // Create order in Directus (using elevated permissions)
    const orderData = await elevatedFetch<BackendResponse<Orders>>('/items/orders', {
      method: 'POST',
      body: {
        user: userId, // The customer who placed the order (UUID string)
        creator: parseInt(creatorId), // Creator ID is a number
        status: 'processing', // Payment completed, order is being processed
        currency_code: currency, // Use the product's base currency
        total_amount: totalAmount,
        base_total_amount: baseTotalAmount,
        total_amount_after_discount: totalAmount, // No discounts for now
        base_total_amount_after_discount: baseTotalAmount,
        payment_id: body.payment_intent_id, // Link to Stripe payment intent
        // Shipping address snapshot from selected address
        shipping_name: shippingName,
        shipping_phone_number: addressData.phone_number || null,
        shipping_address_line_1: addressData.address_line_1 || null,
        shipping_address_line_2: addressData.address_line_2 || null,
        shipping_city: addressData.city || null,
        shipping_province: addressData.province || null, // Use province field from addresses table
        shipping_postcode: addressData.postcode || null,
        shipping_country: addressData.country || null,
      },
    });

    const orderId = orderData.data.id;

    // Create order items
    const orderItemsPromises = orderItems.map((item: any) => {
      const product = products.find((p) => p.id === item.id);
      if (!product) return Promise.resolve();

      // Always use USD price for price_at_order
      const usdPrice = Number(product.price) || 0;

      const orderItemPayload = {
        order: orderId,
        product: item.id,
        quantity: item.quantity,
        price_at_order: Number(usdPrice.toFixed(2)), // Always store in USD
        price_jpy_at_order: product.price_jpy ? Number(Number(product.price_jpy).toFixed(2)) : null, // Snapshot for reference
        price_krw_at_order: product.price_krw ? Number(Number(product.price_krw).toFixed(2)) : null, // Snapshot for reference
        message: item.message || '',
        product_name_at_order: product?.translations?.[0]?.name || `Product ${item.id}`, // Snapshot of product name
        status: 'published', // Required status field
      };

      return elevatedFetch('/items/order_item', {
        method: 'POST',
        body: orderItemPayload,
      });
    });

    await Promise.all(orderItemsPromises);

    // Create order messages for each item (communication with creators) - same as original
    const messagePromises = orderItems
      .filter((item: any) => item.message && item.message.trim().length > 0)
      .map((item: any) =>
        dFetch('/items/messages', {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          method: 'POST',
          body: {
            order: orderId,
            text: item.message,
            user_created: userId, // User ID is UUID string
            status: 'published',
          },
        }),
      );

    await Promise.all(messagePromises);

    // Update product stock (reduce by ordered quantities)
    const stockUpdatePromises = orderItems.map((item: any) => {
      const product = products.find((p) => p.id === item.id);
      if (product && product.stock_remaining !== null && product.stock_remaining !== undefined) {
        const newStock = product.stock_remaining - item.quantity;
        return elevatedFetch(`/items/products/${item.id}`, {
          method: 'PATCH',
          body: {
            stock_remaining: Math.max(0, newStock), // Ensure stock doesn't go negative
          },
        });
      }
      return Promise.resolve();
    });

    await Promise.all(stockUpdatePromises);

    return {
      success: true,
      orderId: orderId,
      totalAmount: totalAmount,
      currency: currency,
      creatorId: parseInt(creatorId),
      itemCount: orderItems.length,
      paymentIntentId: body.payment_intent_id,
      message: `Order created successfully for ${orderItems.length} item(s)`,
    };

  } catch (error) {
    console.error('Failed to create order from payment:', error);
    
    // Handle Stripe-specific errors
    if (error && typeof error === 'object' && 'type' in error) {
      throw createError({
        statusCode: 400,
        statusMessage: `Payment verification error: ${(error as any).message || 'Unknown payment error'}`,
      });
    }
    
    // Re-throw our custom errors
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create order from payment',
    });
  }
});
