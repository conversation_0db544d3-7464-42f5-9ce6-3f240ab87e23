import { z } from 'zod/v4';

// Query schema for filtering orders
const querySchema = z.object({
  status: z.enum(['ongoing', 'completed', 'all']).default('all'),
  limit: z.coerce.number().default(50),
  offset: z.coerce.number().default(0),
});

export default defineEventHandler(async (event) => {
  // Check authentication
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  // Parse query parameters
  const query = await getValidatedQuery(event, querySchema.parse);

  try {
    // Get current user ID
    const currentUser = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: ['id'],
      },
    });

    const userId = currentUser.data.id;

    // Build filter based on status
    let statusFilter = {};
    if (query.status === 'ongoing') {
      statusFilter = {
        status: {
          _in: ['pending', 'processing', 'shipped'],
        },
      };
    } else if (query.status === 'completed') {
      statusFilter = {
        status: {
          _in: ['completed', 'cancelled', 'refunded'],
        },
      };
    }

    // Fetch user's orders with related data using elevated permissions
    const ordersResponse = await elevatedFetch<BackendResponse<Orders[]>>('/items/orders', {
      params: {
        fields: [
          '*',
          'order_items.*',
          'order_items.product.main_image',
          'order_items.product.ip.translations.*',
          'order_items.product.translations.*',
          'order_items.product.category.translations.*',
          'creator.avatar, creator.nickname',
        ],
        filter: {
          user: {
            _eq: userId,
          },
          ...statusFilter,
        },
        sort: ['-date_created'],
        limit: query.limit,
        offset: query.offset,
      },
    });

    return ordersResponse.data;
  } catch (error) {
    console.error('Failed to fetch orders:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch orders',
    });
  }
});
