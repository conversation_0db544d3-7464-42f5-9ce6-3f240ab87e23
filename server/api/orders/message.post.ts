import { z } from 'zod/v4';

const schema = z.object({
  orderId: z.string(),
  message: z.string(),
});

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const body = await readValidatedBody(event, schema.parse);

  const data = await dFetch<BackendResponse<any[]>>(`/items/messages`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: {
      order_id: body.orderId,
      message: body.message,
    },
  });

  return data.data;
});
