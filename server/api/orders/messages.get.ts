import { z } from 'zod/v4';

const schema = z.object({
  productId: z.string(),
});

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const body = await readValidatedBody(event, schema.parse);

  const data = await dFetch<BackendResponse<any[]>>('/items/messages', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    params: {
      fields: ['*'],
      filter: {
        product: {
          _eq: body.productId,
        },
      },
      sort: ['date_created'],
    },
  });

  return data.data;
});
