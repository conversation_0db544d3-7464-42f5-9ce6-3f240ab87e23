export default defineEventHandler(async (event) => {
  const params = getRouterParams(event);
  const data = await dFetch<Review[]>(`/items/review`, {
    params: {
      filter: {
        product: {
          _eq: params.id,
        },
        rating: {
          _neq: null,
        },
      },
    },
  });

  const totalRating = data.data.reduce((acc, review: any) => acc + review.rating, 0);
  const averageRating = totalRating / data.data.length;

  return averageRating;
});
