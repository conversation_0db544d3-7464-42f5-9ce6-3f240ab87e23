import { z } from 'zod/v4';

// we need to get just the IPCategories that have IPs

const querySchema = z.object({
  limit: z.number().default(-1),
  creatorId: z.coerce.number().optional(),
  ipId: z.coerce.number().optional(),
});

export default defineEventHandler(async (event) => {
  const query = await getValidatedQuery(event, querySchema.parse);
  const accessToken = getCookie(event, 'access_token');

  const products = await dFetch<BackendResponse<Products[]>>('/items/products', {
    headers: {
      Authorization: `Bearer ${getCookie(event, 'access_token')}`,
    },
    params: {
      limit: query.limit,
      fields: ['category'],
      filter: {
        category: {
          _nnull: true,
        },
        ...(query.creatorId ? { creator: { _eq: query.creatorId } } : {}),
        ...(query.ipId ? { ip: { _eq: query.ipId } } : {}),
      },
    },
  });
  const distinctProductCategories = new Set(products.data.map((product: any) => product.category));
  return Array.from(distinctProductCategories);
});
