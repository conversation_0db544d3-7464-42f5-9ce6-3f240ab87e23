import { z } from 'zod/v4';
import type { Products } from '~/shared/types/directus';

// Stock validation schema
const stockValidationSchema = z.object({
  items: z.array(z.object({
    id: z.number(),
    quantity: z.number().min(1),
  })),
});

export default defineEventHandler(async (event) => {
  // Check authentication
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const body = await readValidatedBody(event, stockValidationSchema.parse);

  try {
    // Fetch current stock levels for all products
    const productIds = body.items.map((item) => item.id);
    const productsResponse = await dFetch<BackendResponse<Products[]>>('/items/products', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        filter: {
          id: {
            _in: productIds,
          },
        },
        fields: [
          'id',
          'stock_remaining',
          'translations.name',
        ],
      },
    });

    if (!productsResponse.data) {
      return {
        success: false,
        message: 'Failed to fetch product data',
      };
    }

    const products = productsResponse.data;

    // Validate stock availability for each item
    for (const item of body.items) {
      const product = products.find((p) => p.id === item.id);
      
      if (!product) {
        return {
          success: false,
          message: `Product ${item.id} not found`,
        };
      }

      // Check if product has stock tracking enabled and validate availability
      if (
        product.stock_remaining !== null &&
        product.stock_remaining !== undefined &&
        product.stock_remaining < item.quantity
      ) {
        const productName = product.translations?.[0]?.name || `Product ${item.id}`;
        return {
          success: false,
          message: `Insufficient stock for ${productName}. Available: ${product.stock_remaining}, Requested: ${item.quantity}`,
        };
      }
    }

    // All items have sufficient stock
    return {
      success: true,
      message: 'All items are in stock',
    };

  } catch (error) {
    console.error('Stock validation error:', error);
    return {
      success: false,
      message: 'Failed to validate stock availability',
    };
  }
});
