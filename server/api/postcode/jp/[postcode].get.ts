type AddressResponse = {
  message: string | null;
  results: JapaneseAddressResult[] | null;
  status: number;
};

interface JapaneseAddressResult {
  address1: string;
  address2: string;
  address3: string;
  kana1: string;
  kana2: string;
  kana3: string;
  prefcode: string;
  zipcode: string;
}

export default defineCachedEventHandler(async (event) => {
  const postcode = getRouterParam(event, 'postcode');
  
  if (!postcode) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Postcode is required'
    });
  }

  // Clean postcode (remove hyphens and validate format)
  const cleanPostcode = postcode.replace(/-/g, '');
  
  // Validate Japanese postcode format (7 digits)
  if (!/^\d{7}$/.test(cleanPostcode)) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid Japanese postcode format. Must be 7 digits.'
    });
  }

  return await $fetch<AddressResponse>(`https://zipcloud.ibsnet.co.jp/api/search?zipcode=${cleanPostcode}`, {
    parseResponse: JSON.parse,
    timeout: 3000
  });
},{
  getKey: (event) => {
    return `${event.context.params?.postcode}`
  }
});
