export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  try {
    const data = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
      headers: {
        Authorization: 'Bearer ' + accessToken,
      },
      params: {
        fields: ['*','creator_profile.*','ip_owner_profile.*','role.name'],
        filter: {
          status: {
            _eq: 'active',
          },
        },
      },
    });

    if (!data.data.email) {
      setCookie(event, 'access_token', '', { maxAge: 0 });
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      });
    }

    return data.data;
  } catch (error) {
    console.error('Failed to fetch user data:', error);
    setCookie(event, 'access_token', '', { maxAge: 0 });
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }
});
