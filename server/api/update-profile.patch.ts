import { z } from 'zod/v4';

const schema = z.object({
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  nickname: z.string().optional(),
  birthday: z.string().optional(),
  avatar: z.string().optional(), // Directus file ID
});

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const body = await readValidatedBody(event, schema.parse);

  try {
    // Get current user ID
    const currentUser = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    const userId = currentUser.data.id;

    // Update user profile in Directus
    const updatedUser = await dFetch<BackendResponse<DirectusUsers>>(`/users/${userId}`, {
      method: 'PATCH',
      headers: {
        Authorization: `Bear<PERSON> ${accessToken}`,
      },
      body: {
        ...body,
      },
    });
    
    return {
      success: true,
      user: updatedUser.data,
    };
  } catch (error) {
    console.error('Failed to update profile:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update profile',
    });
  }
});
