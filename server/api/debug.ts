// Just returns the details of what was recieved all in the body. Headers, params, body its self.

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const params = getRouterParams(event)
  const headers = getHeaders(event)

  // console.log({body, params, headers})

  // if we got auth token in "cookie" header , console warn
  if (headers?.cookie) {
    console.warn("Auth token in cookie")
    console.warn(headers.cookie)
  }


  return {
    headers,
    params,
    body,
  }
})
