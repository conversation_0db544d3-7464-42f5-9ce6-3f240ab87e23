import { z } from 'zod/v4';

const schema = z.object({
  // Basic product info
  ipName: z.string().optional(),
  creatorUsername: z.string().optional(),
  creatorEmail: z.string().optional(),
  productName: z.string().optional(),
  productDescription: z.string().optional(),
  productCategory: z.string().optional(),
  applicationType: z.string().optional(),
  
  // Product details
  productImages: z.array(z.string()).optional(),
  price: z.string().optional(),
  currency: z.string().optional(),
  totalStock: z.number().optional(),
  
  // Email recipients
  recipients: z.array(z.string()).optional(), // Array of email addresses to send to
});

export default defineEventHandler(async (event) => {
  try {
    const body = await readValidatedBody(event, schema.parse);

    // Default recipients if none provided (you can configure this)
    const defaultRecipients = ['<EMAIL>']; // Replace with actual admin emails
    const recipients = body.recipients || defaultRecipients;

    // Validate that we have at least one recipient
    if (!recipients || recipients.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'No email recipients specified',
      });
    }

    // Generate email content using the new template
    const htmlContent = generateProductApplicationEmail({
      ipName: body.ipName,
      creatorUsername: body.creatorUsername,
      creatorEmail: body.creatorEmail,
      productName: body.productName,
      productDescription: body.productDescription,
      productCategory: body.productCategory,
      applicationType: body.applicationType,
      productImages: body.productImages,
      price: body.price,
      currency: body.currency,
      totalStock: body.totalStock,
    });

    // Send email to all recipients
    const emailResults = await Promise.allSettled(
      recipients.map(recipient =>
        sendHtmlEmail(
          recipient,
          'New Product Application Submitted - IPGO',
          htmlContent
        )
      )
    );

    // Check if any emails failed
    const failedEmails = emailResults
      .map((result, index) => ({ result, recipient: recipients[index] }))
      .filter(({ result }) => result.status === 'rejected' || !result.value.success);

    if (failedEmails.length > 0) {
      console.error('Some emails failed to send:', failedEmails);
      
      // If all emails failed, throw an error
      if (failedEmails.length === recipients.length) {
        throw createError({
          statusCode: 500,
          statusMessage: 'Failed to send product application emails to all recipients',
        });
      }
    }

    // Return success with details
    const successfulEmails = emailResults.filter(
      result => result.status === 'fulfilled' && result.value.success
    ).length;

    return {
      success: true,
      message: 'Product application email sent successfully',
      details: {
        totalRecipients: recipients.length,
        successfulSends: successfulEmails,
        failedSends: failedEmails.length,
        recipients: recipients,
      },
    };

  } catch (error: any) {
    console.error('Product application email API error:', error);

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error while sending product application email',
    });
  }
});
