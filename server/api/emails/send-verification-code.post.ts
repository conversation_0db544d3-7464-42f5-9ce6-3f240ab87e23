export default defineEventHandler(async (event) => {
  try {
    const body = await readBody<{
      email: string;
      mode: 'signup' | 'recover';
      role?: 'fan' | 'creator' | 'owner';
    }>(event);
    const { email, mode, role = 'fan' } = body;

    // Validate required fields
    if (!email || !mode) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields: email, mode',
      });
    }

    // 1. create verification code
    const verificationCode = createVerificationCode();
    const user = await elevatedFetch<BackendResponse<DirectusUsers[]>>(`/users?filter[email][_eq]=${body.email}`);
    if (!user.data[0]) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User not found',
      });
    }

    // 2. set user's verification_code in directus
    const userId = user.data[0].id;
    const data = await elevatedFetch<BackendResponse<DirectusUsers[]>>(`/users/${userId}`, {
      method: 'PATCH',
      body: {
        verification_code: verificationCode,
      },
    });

    // Generate email content
    const htmlContent = generateVerificationEmail({
      email,
      verificationCode,
      mode,
      role,
      expiresIn: '10 minutes',
    });

    // Send verification email
    const result = await sendHtmlEmail(email, 'Verify Your Email - IPGO', htmlContent);

    if (!result.success) {
      throw createError({
        statusCode: 500,
        statusMessage: `Failed to send verification email: ${result.error}`,
      });
    }

    return {
      success: true,
      messageId: result.messageId,
      message: 'Verification email sent successfully',
    };
  } catch (error: any) {
    console.error('Verification email API error:', error);

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
    });
  }
});
