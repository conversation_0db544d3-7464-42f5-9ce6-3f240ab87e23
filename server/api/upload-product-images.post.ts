import { z } from 'zod/v4';

const schema = z.object({
  images: z.array(z.string()), // Array of base64 data
  productName: z.string().optional(),
  type: z.enum(['product', 'user', 'ip', 'company']).default('product'),
});

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const body = await readValidatedBody(event, schema.parse);

  try {
    const uploadedFiles = [];
    
    // Upload each image to Directus
    for (let i = 0; i < body.images.length; i++) {
      const image = body.images[i];
      const filename = body.productName 
        ? `${body.productName}-${i + 1}-${Date.now()}`
        : `${body.type}-image-${i + 1}-${Date.now()}`;
      
      const fileObject = await uploadBase64ToDirectus(image, filename, UPLOAD_FOLDERS[body.type]);
      
      if (fileObject?.id) {
        uploadedFiles.push({
          id: fileObject.id,
          filename: fileObject.filename_download,
          title: fileObject.title,
          folder: fileObject.folder,
        });
      }
    }

    return {
      fileIds: uploadedFiles.map(file => file.id),
      files: uploadedFiles,
      success: true,
    };
  } catch (error) {
    console.error('Failed to upload product images:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to upload product images',
    });
  }
});
