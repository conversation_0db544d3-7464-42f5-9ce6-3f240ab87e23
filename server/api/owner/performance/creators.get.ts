import { z } from 'zod/v4';

const querySchema = z.object({
    startDate: z.string(),
    endDate: z.string(),
    creators: z.union([
        z.coerce.number().transform(val => [val]), // Single number -> array
        z.array(z.coerce.number()) // Already an array
    ]).optional(),
});

interface Data {
  total_amount_after_discount: number;
  creator:                     Creator;
  order_items:                 OrderItem[];
}

interface Creator {
  id: number;
  nickname: string;
}

interface OrderItem {
  quantity: number;
  price_at_order: string;
}

export default defineEventHandler(async (event) => {
    const accessToken = getCookie(event, 'access_token');

    const query = await getValidatedQuery(event, querySchema.parse);

    // Build filter object - only add Creator filter if creators array is provided and not empty
    const filter: any = {
        date_created: {
            _between: [query.startDate, query.endDate],
        },
    };

    // Add Creator filter if creatorss array is provided and not empty
    if (query.creators && query.creators.length > 0) {
        filter['creator'] = {
          id: {
              _in: query.creators
          }
        };
    } else {
        console.log('🔍 Creator Filter Debug - No Creator filter applied');
    }

    // Creators total revenue
    const fetchCreatorsData = await dFetch<BackendResponse<Data[]>>('/items/orders', {
        headers: {
            Authorization: `Bearer ${accessToken}`,
        },
        params: {
            limit: -1,
            fields: [
                'id',
                'creator.id',
                'creator.nickname',
                'order_items.quantity',
                'order_items.price_at_order'
            ],
            filter
        },
    })

    // If order doesnt have order_items, means it didnt have any of the creators products. remove it
    const cleanedCreatorData = fetchCreatorsData.data.filter((order) => order.order_items.length > 0);

    // Group data by Creator and sum up total_amount_after_discount
    const creatorsRevenue: {creatorNickname: string, count: number; total: number; }[] = [];
    const creatorTotals: Record<number, {creatorNickname: string; count: number; total: number;}> = {};

    cleanedCreatorData.forEach(order => {
        const creator = order.creator;
        if (!creatorTotals[creator.id]) {
            creatorTotals[creator.id] = {
                creatorNickname: creator.nickname,
                count: 0,
                total: 0,
            };
        }
        creatorTotals[creator.id].total += order.order_items.reduce((acc, item) => {
            return acc + Number(item.price_at_order);
        }, 0);
        creatorTotals[creator.id].count += order.order_items.reduce((acc, item) => {
            return acc + Number(item.quantity);
        }, 0);
    });

    // Convert to array
    Object.values(creatorTotals).forEach(creator => {
        creatorsRevenue.push(creator);
    });

    return {
        creatorsRevenue
    }
});
