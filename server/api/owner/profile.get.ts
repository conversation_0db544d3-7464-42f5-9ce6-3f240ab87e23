export default defineEventHandler(async (event) => {
  // Get both Directus User and Owner profile. Then Merge
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const directusUser = await dFetch('/users/me', {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    params: {
      fields: ['*','ip_owner_profile.*','ip_owner_profile.business_license.*'],
    },
  });

  return directusUser.data;

})
