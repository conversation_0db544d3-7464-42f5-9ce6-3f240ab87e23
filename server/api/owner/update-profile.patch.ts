import { z } from 'zod/v4';

const schema = z.object({
  // User fields
  first_name: z.string().optional(),
  avatar: z.string().optional(), // Directus file ID

  // Owner profile fields
  company_name: z.string().optional(),
  company_phone_number: z.string().optional(),
  company_role: z.string().optional(),
  homepage_link: z.string().optional(),
  country: z.string().optional(),
  business_license: z.string().optional(), // Base64 data for new upload
  business_license_filename: z.string().optional(), // Original filename
});

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const body = await readValidatedBody(event, schema.parse);

  console.log('🔍 DEBUG - Update profile body:', JSON.stringify(body, null, 2));

  try {
    // Get current user ID
    const currentUser = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: ['*', 'role.*'],
      },
    });

    console.log('🔍 DEBUG - Current user:', JSON.stringify({
      id: currentUser.data.id,
      email: currentUser.data.email,
      role: currentUser.data.role,
    }, null, 2));

    const userId = currentUser.data.id;

    // Upload business license if provided (similar to handleOwnerSignup)
    let businessLicenseFileObject = null;
    if (body.business_license && body.business_license.startsWith('data:')) {
      // Use original filename if provided, otherwise fallback to email-based name
      const filename = body.business_license_filename || `${currentUser.data.email}-business-license-updated`;
      businessLicenseFileObject = await uploadBase64ToDirectus(
        body.business_license,
        filename,
        UPLOAD_FOLDERS.company
      );
    }

    // Get the owner profile for this user
    console.log('🔍 DEBUG - Looking for owner profile for user ID:', userId);

    const ownerProfile = await dFetch<BackendResponse<IpOwner[]>>('/items/ip_owner', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        filter: {
          user: {
            _eq: userId,
          },
        },
        limit: 1,
      },
    });

    console.log('🔍 DEBUG - Owner profile response:', JSON.stringify(ownerProfile, null, 2));

    if (!ownerProfile.data || ownerProfile.data.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Owner profile not found',
      });
    }

    // Update user profile in Directus (basic fields)
    const userUpdateData: any = {};
    if (body.first_name !== undefined) userUpdateData.first_name = body.first_name;
    if (body.avatar !== undefined) userUpdateData.avatar = body.avatar;

    if (Object.keys(userUpdateData).length > 0) {
      await dFetch<BackendResponse<DirectusUsers>>(`/users/${userId}`, {
        method: 'PATCH',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        body: userUpdateData,
      });
    }

    // Update owner profile in Directus
    const ownerUpdateData: any = {};
    if (body.company_name !== undefined) ownerUpdateData.company_name = body.company_name;
    if (body.company_phone_number !== undefined) ownerUpdateData.company_phone_number = body.company_phone_number;
    if (body.company_role !== undefined) ownerUpdateData.company_role = body.company_role;
    if (body.homepage_link !== undefined) ownerUpdateData.homepage_link = body.homepage_link;
    if (body.country !== undefined) ownerUpdateData.country = body.country;

    // Update business license with uploaded file ID (only if new file was uploaded)
    if (businessLicenseFileObject?.id) {
      ownerUpdateData.business_license = businessLicenseFileObject.id;
    }

    // Also update name field in owner profile to keep them in sync
    if (body.first_name !== undefined) ownerUpdateData.name = body.first_name;

    console.log('🔍 DEBUG - Owner update data:', JSON.stringify(ownerUpdateData, null, 2));
    console.log('🔍 DEBUG - Owner profile ID:', ownerProfile.data[0].id);

    if (Object.keys(ownerUpdateData).length > 0) {
      console.log('🔍 DEBUG - Update owner profile...');
      console.log('🔍 DEBUG - Attempting update data:', JSON.stringify(ownerUpdateData, null, 2));
      await dFetch<BackendResponse<IpOwner>>(`/items/ip_owner/${ownerProfile.data[0].id}`, {
        method: 'PATCH',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        body: ownerUpdateData,
      });
      console.log('✅ DEBUG - Owner profile updated successfully');
    }
    
    return {
      success: true,
      message: 'Profile updated successfully',
    };
  } catch (error) {
    console.error('Failed to update owner profile:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update profile',
    });
  }
});
