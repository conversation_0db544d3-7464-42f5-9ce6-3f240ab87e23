import { z } from 'zod/v4';

// IP update schema - matches the form validation
const ipUpdateSchema = z.object({
  name: z.string().min(3),
  description: z.string().min(10),
  category: z.number().min(1),
  images: z.array(z.string()).min(1),
  keywords: z.string().optional(),
  locale: z.enum(['en', 'jp', 'kr']).default('en'),
});

export default defineEventHandler(async (event) => {
  // Check authentication
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const ipId = getRouterParam(event, 'id');
  const body = await readValidatedBody(event, ipUpdateSchema.parse);

  try {
    // Get current user to find their IP owner profile
    const currentUser = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    // Get the IP owner profile for this user
    const ownerProfile = await dFetch<BackendResponse<IpOwner[]>>('/items/ip_owner', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        filter: {
          user: {
            _eq: currentUser.data.id,
          },
        },
        limit: 1,
      },
    });

    if (!ownerProfile.data || ownerProfile.data.length === 0) {
      throw createError({
        statusCode: 403,
        statusMessage: 'IP Owner profile not found',
      });
    }

    // Get the existing IP to verify ownership
    const existingIp = await dFetch<BackendResponse<Ip>>(`/items/ip/${ipId}`, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: ['id', 'owner', 'status'],
      },
    });

    // Verify the IP belongs to this owner
    const existingIpOwnerId = typeof existingIp.data.owner === 'number' 
      ? existingIp.data.owner 
      : existingIp.data.owner?.id;

    if (existingIpOwnerId !== ownerProfile.data[0].id) {
      throw createError({
        statusCode: 403,
        statusMessage: 'You do not have permission to edit this IP',
      });
    }

    // Step 1: Update the IP basic data
    const updatedIpData = {
      category: body.category,
      keywords: body.keywords || '',
      main_image: body.images[0], // First image as main image
    };

    await elevatedFetch(`/items/ip/${ipId}`, {
      method: 'PATCH',
      body: updatedIpData,
    });

    // Step 2: Update IP translation
    // First, get existing translation for this locale
    const existingTranslations = await elevatedFetch<BackendResponse<IpTranslations[]>>('/items/ip_translations', {
      params: {
        filter: {
          ip_id: {
            _eq: ipId,
          },
          languages_id: {
            _eq: LANGUAGE_LOCAL_ID[body.locale],
          },
        },
        limit: 1,
      },
    });

    if (existingTranslations.data && existingTranslations.data.length > 0) {
      // Update existing translation
      await elevatedFetch(`/items/ip_translations/${existingTranslations.data[0].id}`, {
        method: 'PATCH',
        body: {
          name: body.name,
          description: body.description,
        },
      });
    } else {
      // Create new translation if it doesn't exist
      await elevatedFetch('/items/ip_translations', {
        method: 'POST',
        body: {
          name: body.name,
          description: body.description,
          languages_id: LANGUAGE_LOCAL_ID[body.locale],
          ip_id: parseInt(ipId!),
        },
      });
    }

    // Step 3: Update IP image relations
    // First, delete all existing image relations for this IP
    const existingImageRelations = await elevatedFetch<BackendResponse<IpFiles[]>>('/items/ip_files', {
      params: {
        filter: {
          ip_id: {
            _eq: ipId,
          },
        },
      },
    });

    // Delete existing image relations
    if (existingImageRelations.data && existingImageRelations.data.length > 0) {
      const deletePromises = existingImageRelations.data.map(relation =>
        elevatedFetch(`/items/ip_files/${relation.id}`, {
          method: 'DELETE',
        })
      );
      await Promise.all(deletePromises);
    }

    // Create new image relations for the updated images
    const imagePromises = body.images.map(imageId =>
      elevatedFetch<BackendResponse<IpFiles>>('/items/ip_files', {
        method: 'POST',
        body: {
          ip_id: parseInt(ipId!),
          directus_files_id: imageId,
        },
      })
    );

    await Promise.all(imagePromises);

    return {
      success: true,
      message: 'IP updated successfully',
    };

  } catch (error) {
    console.error('Failed to update IP:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update IP',
    });
  }
});
