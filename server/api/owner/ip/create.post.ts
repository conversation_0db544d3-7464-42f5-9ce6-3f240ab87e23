import { z } from 'zod/v4';

// IP creation schema - matches the form validation
const ipSchema = z.object({
  name: z.string().min(3),
  description: z.string().min(10),
  category: z.number().min(1),
  images: z.array(z.string()).min(1),
  keywords: z.string().optional(),
  locale: z.enum(['en', 'jp', 'kr']).default('en'),
});

export default defineEventHandler(async (event) => {
  // Check authentication
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const body = await readValidatedBody(event, ipSchema.parse);

  try {
    // Get current user to find their IP owner profile
    const currentUser = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    // Get the IP owner profile for this user
    const ownerProfile = await dFetch<BackendResponse<IpOwner[]>>('/items/ip_owner', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        filter: {
          user: {
            _eq: currentUser.data.id,
          },
        },
        limit: 1,
      },
    });

    if (!ownerProfile.data || ownerProfile.data.length === 0) {
      throw createError({
        statusCode: 403,
        statusMessage: 'IP Owner profile not found',
      });
    }

    // Prepare IP data
    const ipData = {
      category: body.category,
      keywords: body.keywords || '',
      owner: ownerProfile.data[0].id,
      status: 'published',
      main_image: body.images[0], // First image as main image (will also be images[0])
    };

    // Create IP using elevated permissions
    const createdIp = await elevatedFetch<BackendResponse<Ip>>('/items/ip', {
      method: 'POST',
      body: ipData,
    });

    console.log('Created IP:', createdIp);

    // Create IP translation
    const translation = await elevatedFetch<BackendResponse<IpTranslations>>('/items/ip_translations', {
      method: 'POST',
      body: {
        name: body.name,
        description: body.description,
        languages_id: LANGUAGE_LOCAL_ID[body.locale],
        ip_id: createdIp.data.id,
      },
    });

    console.log('Created IP translation:', translation);

    // Create IP image relations (for ALL images including main image)
    // This ensures main_image and images[0] are the same
    const imagePromises = body.images.map(imageId =>
      elevatedFetch<BackendResponse<IpFiles>>('/items/ip_files', {
        method: 'POST',
        body: {
          ip_id: createdIp.data.id,
          directus_files_id: imageId,
        },
      })
    );

    await Promise.all(imagePromises);

    return {
      success: true,
      ip: createdIp.data,
      translation: translation.data,
    };

  } catch (error) {
    console.error('Failed to create IP:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create IP',
    });
  }
});
