export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  try {
    // Fetch addresses from Directus
    const addressesResponse = await dFetch<BackendResponse<Addresses[]>>('/items/addresses', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: '*',
      },
    });

    // Get user profile to determine default address
    const userResponse = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: ['default_address'],
      },
    });

    return {
      data: addressesResponse.data || [],
      default_address: userResponse.data?.default_address || null,
    };
  } catch (error) {
    console.error('Failed to fetch addresses:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch addresses',
    });
  }
});
