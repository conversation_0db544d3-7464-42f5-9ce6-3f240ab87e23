export default defineEventHandler(async (event) => {
  // Get both Directus User and Creator profile. Then Merge
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const directusUser = await dFetch('/users/me', {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    params: {
      fields: ['*','creator_profile.*'],
    },
  });

  return directusUser.data;

})
