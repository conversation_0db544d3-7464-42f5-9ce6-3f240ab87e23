import { z } from 'zod/v4';

// Query schema for filtering orders
const querySchema = z.object({
  status: z.enum(['ongoing', 'processing', 'completed', 'all']).default('all'),
  limit: z.coerce.number().default(50),
  offset: z.coerce.number().default(0),
  fields: z.string().optional(),
});

export default defineEventHandler(async (event) => {
  // Check authentication
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  // Parse query parameters
  const query = await getValidatedQuery(event, querySchema.parse);

  try {
    // Get current user ID
    const currentUser = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: ['*'],
      },
    });

    const creatorProfile = currentUser.data.creator_profile?.[0];

    if (!creatorProfile) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User does not have a creator profile',
      });
    }

    const creatorId = currentUser.data.creator_profile[0];

    // Build filter based on status
    let statusFilter = {};
    if (query.status === 'ongoing') {
      statusFilter = {
        status: {
          _in: ['pending', 'processing', 'shipped'],
        },
      };
    } else if (query.status === 'completed') {
      statusFilter = {
        status: {
          _in: ['completed', 'cancelled', 'refunded'],
        },
      };
    } else if (query.status === 'processing') {
      statusFilter = {
        status: {
          _eq: 'processing',
        },
      };
    }

    // Fetch user's orders with related data using elevated permissions
    const ordersResponse = await elevatedFetch<BackendResponse<Orders[]>>('/items/orders', {
      params: {
        fields: [
          query.fields || '*',
          'order_items.*',
          'order_items.product.main_image',
          'order_items.product.ip.translations.*',
          'order_items.product.translations.*',
          'order_items.product.category.translations.*',
          'creator.avatar, creator.nickname',
        ],
        filter: {
          creator: {
            _eq: creatorId,
          },
          ...statusFilter,
        },
        sort: ['-date_created'],
        limit: query.limit,
        offset: query.offset,
      },
    });

    return ordersResponse.data;
  } catch (error) {
    console.error('Failed to fetch orders:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch orders',
    });
  }
});
