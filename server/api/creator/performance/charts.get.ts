import { z } from 'zod/v4';

const querySchema = z.object({
    startDate: z.string(),
    endDate: z.string(),
    ips: z.union([
        z.coerce.number().transform(val => [val]), // Single number -> array
        z.array(z.coerce.number()) // Already an array
    ]).optional(),
});

// Helper function to generate month range between two dates
function generateMonthRange(startDate: string, endDate: string): string[] {
    // Remove extra quotes if they exist
    const cleanStartDate = startDate.replace(/^"(.*)"$/, '$1');
    const cleanEndDate = endDate.replace(/^"(.*)"$/, '$1');

    const start = new Date(cleanStartDate);
    const end = new Date(cleanEndDate);
    const months: string[] = [];

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        console.error('📊 generateMonthRange - Invalid dates!');
        return [];
    }

    const current = new Date(start.getFullYear(), start.getMonth(), 1);
    const endMonth = new Date(end.getFullYear(), end.getMonth(), 1);

    while (current <= endMonth) {
        const year = current.getFullYear();
        const month = String(current.getMonth() + 1).padStart(2, '0');
        months.push(`${year}-${month}`);
        current.setMonth(current.getMonth() + 1);
    }

    return months;
}

// Helper function to format month for display
function formatMonthForDisplay(yearMonth: string): string {
    const [year, month] = yearMonth.split('-');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
}

export default defineEventHandler(async (event) => {
    const accessToken = getCookie(event, 'access_token');

    const query = await getValidatedQuery(event, querySchema.parse);

    // Build filter object - only add IP filter if ips array is provided and not empty
    const filter: any = {
        date_created: {
            _between: [query.startDate, query.endDate],
        },
    };

    // Add IP filter if ips array is provided and not empty
    if (query.ips && query.ips.length > 0) {
        filter['order_items'] = {
            product: {
                ip: {
                    id: {
                        _in: query.ips
                    }
                }
            }
        };
    } else {
        console.log('🔍 IP Filter Debug - No IP filter applied');
    }

    const orders = await dFetch<BackendResponse<Orders[]>>('/items/orders', {
        headers: {
            Authorization: `Bearer ${accessToken}`,
        },
        params: {
            limit: -1,
            fields: [
                'id',
                'total_amount_after_discount',
                'order_items.product.ip.id',
                'order_items.product.ip.translations.*',
                'order_items.quantity',
                'date_created'
            ],
            filter,
        },
    });

    // Aggregate by IP first, then year-month.
    const aggregatedData = orders.data.reduce((acc, order) => {
        const orderDate = new Date(order.date_created as string);
        const year = orderDate.getFullYear();
        const month = String(orderDate.getMonth() + 1).padStart(2, '0');
        const yearMonth = `${year}-${month}`;

        order.order_items.forEach((item) => {
            const ip = item.product.ip;
            const ipKey = ip?.id?.toString() || 'unknown';
            if (!acc[ipKey]) {
                acc[ipKey] = {
                    data: {},
                    ip: ip // Store the full IP object with translations
                };
            }
            if (!acc[ipKey].data[yearMonth]) {
                acc[ipKey].data[yearMonth] = {
                    totalAmount: 0,
                    quantity: 0,
                    count: 0,
                };
            }
            acc[ipKey].data[yearMonth].totalAmount += order.total_amount_after_discount!;
            acc[ipKey].data[yearMonth].quantity += item.quantity!;
            acc[ipKey].data[yearMonth].count++;
        });
        return acc;
    }, {} as Record<string, { data: Record<string, { totalAmount: number; quantity: number; count: number }>; ip: any }>);

    // Transform data for chart format - create separate datasets for revenue and quantity
    const revenueChartData: Record<string, any>[] = [];
    const quantityChartData: Record<string, any>[] = [];

    // Get all unique IPs
    const allIPs = Object.keys(aggregatedData);

    // Generate dynamic date range based on query parameters
    const dateRange = generateMonthRange(query.startDate, query.endDate);

    // Create data points for each month in the selected range
    dateRange.forEach(yearMonth => {
        const revenueDataPoint: Record<string, any> = {
            date: formatMonthForDisplay(yearMonth)
        };
        const quantityDataPoint: Record<string, any> = {
            date: formatMonthForDisplay(yearMonth)
        };

        // Add each IP's data for this month
        allIPs.forEach(ipKey => {
            const ipData = aggregatedData[ipKey];
            const monthData = ipData.data[yearMonth];
            revenueDataPoint[ipKey] = monthData ? monthData.totalAmount : 0;
            quantityDataPoint[ipKey] = monthData ? monthData.quantity : 0;
        });

        revenueChartData.push(revenueDataPoint);
        quantityChartData.push(quantityDataPoint);
    });

    return {
        revenueChartData,
        quantityChartData,
        ipData: allIPs.reduce((acc, ipKey) => {
            const ipData = aggregatedData[ipKey];
            acc[ipKey] = {
                ip: ipData.ip,
                color: `hsl(${Math.random() * 360}, 70%, 50%)` // Random color for each IP
            };
            return acc;
        }, {} as Record<string, { ip: any; color: string }>)
    };
});
