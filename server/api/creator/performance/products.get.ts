import { z } from 'zod/v4';
import { IpTranslations } from '~~/shared/types/directus';

const querySchema = z.object({
  startDate: z.string(),
  endDate: z.string(),
  sort: z.enum(['revenue','units_sold']).default('units_sold'),
});

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  const query = await getValidatedQuery(event, querySchema.parse);

  // Build filter object - only add IP filter if ips array is provided and not empty
  const filter: any = {
    order: {
      date_created: {
        _between: [query.startDate, query.endDate],
      },
      status: {
        _neq: 'completed', //change later
      },
    }
  }

   const result = await dFetch<BackendResponse<{ product: number; sum: { quantity: number; price_at_order: number } }[]>>('/items/order_item', {
        headers: {
            Authorization: `Bearer ${accessToken}`,
        },
        params: {
            aggregate: { sum: ['quantity','price_at_order'] },
            groupBy: ['product'],
            limit:  10000,
            filter,
            sort: query.sort === 'revenue' ? ['-sum.price_at_order'] : ['-sum.quantity'],
        },
    });

    // Extract product IDs from the aggregated results
    const productIds = result.data.map(item => item.product);

    // Fetch full product data for these IDs
    const productsResult = await dFetch<BackendResponse<{id: number; translations: ProductsTranslations[]; main_image: string; ip: { translations: IpTranslations[] } }[]>>('/items/products', {
        headers: {
            Authorization: `Bearer ${accessToken}`,
        },
        params: {
            filter: {
                id: {
                    _in: productIds
                }
            },
            fields: ['id', 'translations.*', 'main_image', 'ip.translations.*']
        },
    });

    // Create a map of product ID to product data for efficient lookup
    const productMap = new Map(productsResult.data.map(product => [product.id, product]));

    // Merge the aggregated data with full product information
    const mergedData = result.data.map(item => ({
        ...item,
        productData: productMap.get(item.product)
    }));

    return mergedData;
})