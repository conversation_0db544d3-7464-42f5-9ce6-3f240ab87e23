import { z } from 'zod/v4';

// API endpoint to get all unique IPs that belong to a specific creator
// Used for filter options in creator's product management page
const querySchema = z.object({
  creatorId: z.coerce.number(),
});

export default defineEventHandler(async (event) => {
  const query = await getValidatedQuery(event, querySchema.parse);
  const accessToken = getCookie(event, 'access_token');

  // Get all products for this creator to find unique IPs
  const products = await dFetch<BackendResponse<Products[]>>('/items/products', {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    params: {
      limit: -1,
      fields: ['ip'],
    },
  });


  // Get unique IP IDs
  const uniqueIpIds = [...new Set(products.data.map((product: any) => product.ip))];

  if (uniqueIpIds.length === 0) {
    return [];
  }

  // Fetch IP details with translations
  const ips = await dFetch<BackendResponse<Ip[]>>('/items/ip', {
    params: {
      fields: ['id', 'translations.*'],
      filter: {
        id: {
          _in: uniqueIpIds,
        },
      },
      sort: ['translations.name'],
    },
  });

  return ips.data;
});
