import { z } from 'zod/v4';

const schema = z.object({
  // User fields
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  nickname: z.string().optional(),
  avatar: z.string().optional(), // Directus file ID
  
  // Creator profile fields
  introduction: z.string().optional(),
  address: z.string().optional(), // maps to street_address_1
  city: z.string().optional(),
  country: z.string().optional(),
  postal_code: z.string().optional(),
  province: z.string().optional(),
});

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const body = await readValidatedBody(event, schema.parse);

  try {
    // Get current user and creator profile
    const currentUser = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: ['id', 'creator_profile.*'],
      },
    });

    const userId = currentUser.data.id;
    const creatorProfile = currentUser.data.creator_profile?.[0];

    if (!creatorProfile) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User does not have a creator profile',
      });
    }

    // Update user profile in Directus (basic fields)
    const userUpdateData: any = {};
    if (body.first_name !== undefined) userUpdateData.first_name = body.first_name;
    if (body.last_name !== undefined) userUpdateData.last_name = body.last_name;
    if (body.nickname !== undefined) userUpdateData.nickname = body.nickname;
    if (body.avatar !== undefined) userUpdateData.avatar = body.avatar;

    if (Object.keys(userUpdateData).length > 0) {
      await dFetch<BackendResponse<DirectusUsers>>(`/users/${userId}`, {
        method: 'PATCH',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        body: userUpdateData,
      });
    }

    // Update creator profile (creator-specific fields)
    const creatorUpdateData: any = {};
    if (body.introduction !== undefined) creatorUpdateData.introduction = body.introduction;
    if (body.address !== undefined) creatorUpdateData.street_address_1 = body.address;
    if (body.city !== undefined) creatorUpdateData.city = body.city;
    if (body.country !== undefined) creatorUpdateData.country = body.country;
    if (body.postal_code !== undefined) creatorUpdateData.postal_code = body.postal_code;
    if (body.province !== undefined) creatorUpdateData.province = body.province;
    if (body.avatar !== undefined) creatorUpdateData.avatar = body.avatar;
    
    // Also update name fields in creator profile to keep them in sync
    if (body.first_name !== undefined) creatorUpdateData.first_name = body.first_name;
    if (body.last_name !== undefined) creatorUpdateData.last_name = body.last_name;
    if (body.nickname !== undefined) creatorUpdateData.nickname = body.nickname;

    if (Object.keys(creatorUpdateData).length > 0) {
      await dFetch<BackendResponse<Creator>>(`/items/creator/${creatorProfile.id}`, {
        method: 'PATCH',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        body: creatorUpdateData,
      });
    }
    
    return {
      success: true,
      message: 'Profile updated successfully',
    };
  } catch (error) {
    console.error('Failed to update creator profile:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update profile',
    });
  }
});
