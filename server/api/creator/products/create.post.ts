import { z } from 'zod/v4';

// Product creation schema - matches the form validation
const productSchema = z.object({
  name: z.string().min(3),
  description: z.string().min(10),
  category: z.number().min(1),
  applicationType: z.number().min(1),
  images: z.array(z.string()).min(1).optional(),
  price: z.number().min(1),
  currency: z.string().min(1),
  totalStock: z.number().min(1),
  locale: z.enum(['en', 'jp', 'kr']).default('en'),
  ip: z.number().min(1),
});

export default defineEventHandler(async (event) => {
  // Check authentication
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  // Validate request body
  const body = await readValidatedBody(event, productSchema.parse);

  try {
    // Get current user and their creator profile
    const currentUser = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: ['id', 'creator_profile.*'],
      },
    });

    const creatorProfile = currentUser.data.creator_profile?.[0];

    if (!creatorProfile) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User does not have a creator profile',
      });
    }
    // Prepare product data with server-side values
    const productData = {
      category: body.category,
      product_application_category: body.applicationType, // Map applicationType to product_application_category
      price: body.price,
      base_currency: body.currency,
      main_image: body.images![0], // Set first image as main image
      status: 'pending', // Set status to pending
      creator: creatorProfile.id, // Use creator profile ID from token
      stock_total: body.totalStock, // Map totalStock to stock_total
      stock_remaining: body.totalStock, // Initialize remaining stock
      user_created: currentUser.data.id,
      keywords: body.name + ' ' + body.description,
      ip: body.ip, // Add IP field
    };
    const imagesToProcess = body.images!;

    console.log('productData', productData);

    // Create product using elevated permissions

    const createdProduct = await elevatedFetch<BackendResponse<Products>>('/items/products', {
      method: 'POST',
      body: productData,
    });

    console.log('createdProduct', createdProduct);


    // // Create Translations
    const translation = await elevatedFetch<BackendResponse<ProductsTranslations>>('/items/products_translations', {
      method: 'POST',
      body: {
        name: body.name,
        description: body.description,
        languages_id: LANGUAGE_LOCAL_ID[body.locale],
        products_id: createdProduct.data.id,
      },
    });

    // For all images, create a products_files entry
    const imagePromises = imagesToProcess.map((image) =>
      elevatedFetch<BackendResponse<DirectusFiles>>('/items/products_files', {
        method: 'POST',
        body: {
          directus_files_id: image,
          products_id: createdProduct.data.id,
        },
      }),
    );

    await Promise.all(imagePromises);

    return {
      success: true,
      data: createdProduct.data,
    };

  } catch (error) {
    console.error('Failed to create product:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create product',
    });
  }
});
