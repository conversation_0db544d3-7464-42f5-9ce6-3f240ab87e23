import { z } from 'zod/v4';
import type { Creator, IpOwner } from '../../shared/types/directus';
import { useServerStripe } from "#stripe/server";
import type { H3Event } from 'h3';

// Base schema for all roles (without role field)
const baseSignupSchema = z.object({
  avatar: z.string(),
  email: z.string().email(),
  password: z.string(),
});

// Fan-specific schema (existing fields)
const fanSignupSchema = baseSignupSchema.extend({
  role: z.literal('fan'),
  address: z.string(),
  birthday: z.string(),
  city: z.string(),
  country: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  nickname: z.string(),
  postalCode: z.string(),
  profileImgId: z.string().optional(),
  province: z.string().optional(),
});

// Creator-specific schema
const creatorSignupSchema = baseSignupSchema.extend({
  role: z.literal('creator'),
  firstName: z.string(),
  lastName: z.string(),
  nickname: z.string(),
  introduction: z.string(),
  address: z.string(),
  city: z.string(),
  country: z.string(),
  postalCode: z.string(),
  province: z.string().optional(),
});

// Owner-specific schema
const ownerSignupSchema = baseSignupSchema.extend({
  role: z.literal('owner'),
  firstName: z.string(),
  companyName: z.string(),
  companyPhoneNumber: z.string(),
  companyRole: z.string(),
  homepageLink: z.string(),
  country: z.string(),
  businessLicense: z.string(),
  businessLicenseFilename: z.string().optional(),
  phonePrefix: z.string(),
});

// Union schema that validates based on role
export const signupBodySchema = z.discriminatedUnion('role', [
  fanSignupSchema,
  creatorSignupSchema,
  ownerSignupSchema,
]);

export default defineEventHandler(async (event) => {
  // Debug: Log the raw body first
  const rawBody = await readBody(event);
  console.log('🔍 DEBUG - Raw signup body:', JSON.stringify(rawBody, null, 2));

  try {
    const body = await readValidatedBody(event, signupBodySchema.parse);
    console.log('✅ DEBUG - Validation passed, parsed body:', JSON.stringify(body, null, 2));
  } catch (validationError) {
    console.log('❌ DEBUG - Validation failed:', validationError);
    throw validationError;
  }

  const body = await readValidatedBody(event, signupBodySchema.parse);

  // Upload avatar
  const fileObject = await uploadBase64ToDirectus(body.avatar, `${body.email}-avatar`, UPLOAD_FOLDERS.user);

  // Get user ID first
  const user = await elevatedFetch<BackendResponse<DirectusUsers[]>>(`/users?filter[email][_eq]=${body.email}`);
  const userId = user.data[0].id;

  // Handle role-specific logic
  switch (body.role) {
    case 'fan':
      return await handleFanSignup(body as z.infer<typeof fanSignupSchema>, userId, fileObject, event);
    case 'creator':
      return await handleCreatorSignup(body as z.infer<typeof creatorSignupSchema>, userId, fileObject);
    case 'owner':
      return await handleOwnerSignup(body as z.infer<typeof ownerSignupSchema>, userId, fileObject);
    default:
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid role specified',
      });
  }
});

// Fan signup handler (existing logic)
async function handleFanSignup(
  body: z.infer<typeof fanSignupSchema>,
  userId: string,
  fileObject: any,
  event: H3Event
) {
  // Create Address entry in address book with more complete data
  const addressRes = await elevatedFetch<BackendResponse<Addresses>>('/items/addresses', {
    method: 'POST',
    body: {
      name: `${body.firstName} ${body.lastName}`, // Combine first and last name
      address_line_1: body.address,
      // address_line_2 is not available from signup form
      city: body.city,
      country: body.country,
      postcode: body.postalCode,
      province: body.province, // Use province instead of state for consistency
      // phone_number is not available from signup form
      user: userId,
    },
  });

  // Create Stripe customer for the user
  let stripeCustomerId = null;
  try {
    const stripe = await useServerStripe(event);
    const customer = await stripe.customers.create({
      email: body.email,
      name: `${body.firstName} ${body.lastName}`,
    });
    stripeCustomerId = customer.id;
  } catch (error) {
    console.error('Failed to create Stripe customer during signup:', error);
    // Don't fail the signup if Stripe customer creation fails
    // We can create it later when needed
  }

  // Update User with default_address set to the newly created address
  const userRes = await elevatedFetch<BackendResponse<DirectusUsers>>(`/users/${userId}`, {
    method: 'PATCH',
    body: {
      status: 'active',
      password: body.password,
      first_name: body.firstName,
      last_name: body.lastName,
      avatar: fileObject?.id,
      address: body.address,
      birthday: body.birthday,
      city: body.city,
      country: body.country,
      nickname: body.nickname,
      postcode: body.postalCode,
      province: body.province,
      stripe_customer_id: stripeCustomerId, // Add Stripe customer ID
      default_address: addressRes.data.id, // Set the newly created address as default
    },
  });

  return userRes;
}

// Creator signup handler
async function handleCreatorSignup(
  body: z.infer<typeof creatorSignupSchema>,
  userId: string,
  fileObject: any
) {
  // Update User with basic info
  const userRes = await elevatedFetch<BackendResponse<DirectusUsers>>(`/users/${userId}`, {
    method: 'PATCH',
    body: {
      status: 'active',
      password: body.password,
      first_name: body.firstName,
      last_name: body.lastName,
      avatar: fileObject?.id,
      nickname: body.nickname,
    },
  });

  // Create Creator profile
  const creatorRes = await elevatedFetch<BackendResponse<Creator>>('/items/creator', {
    method: 'POST',
    body: {
      user: userId,
      nickname: body.nickname,
      first_name: body.firstName,
      last_name: body.lastName,
      introduction: body.introduction,
      street_address_1: body.address,
      city: body.city,
      country: body.country,
      postal_code: body.postalCode,
      province: body.province,
      avatar: fileObject?.id,
      status: 'active',
    },
  });

  return { user: userRes, creator: creatorRes };
}

// Owner signup handler
async function handleOwnerSignup(
  body: z.infer<typeof ownerSignupSchema>,
  userId: string,
  fileObject: any
) {
  // Upload business license if provided
  let businessLicenseFileObject = null;
  if (body.businessLicense) {
    // Use original filename if provided, otherwise fallback to email-based name
    const filename = body.businessLicenseFilename || `${body.email}-business-license`;
    businessLicenseFileObject = await uploadBase64ToDirectus(
      body.businessLicense,
      filename,
      UPLOAD_FOLDERS.company
    );
  }

  // Update User with basic info
  const userRes = await elevatedFetch<BackendResponse<DirectusUsers>>(`/users/${userId}`, {
    method: 'PATCH',
    body: {
      password: body.password,
      avatar: fileObject?.id,
      first_name: body.firstName,
      last_name: body.companyName,
      status: 'active',
    },
  });

  // Create IP Owner profile
  const ownerRes = await elevatedFetch<BackendResponse<IpOwner>>('/items/ip_owner', {
    method: 'POST',
    body: {
      user: userId,
      name: body.firstName,
      company: body.companyName,
      company_name: body.companyName,
      company_phone_number: body.phonePrefix + body.companyPhoneNumber,
      company_role: body.companyRole,
      homepage_link: body.homepageLink || null,
      country: body.country,
      business_license: businessLicenseFileObject?.id || null,
      image: fileObject?.id
    },
  });

  return { user: userRes, owner: ownerRes };
}
