import { z } from 'zod/v4';
import { useServerStripe } from "#stripe/server";

// Schema for payment intent creation - same as order submission but with additional fields
const paymentIntentSchema = z.object({
  items: z.array(
    z.object({
      id: z.coerce.number(),
      quantity: z.coerce.number().min(1),
      message: z.coerce.string().optional(),
    }),
  ),
  shipping_address_id: z.coerce.number().optional(), // For future use
  payment_method_id: z.string().optional(), // Stripe payment method ID
});

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const body = await readValidatedBody(event, paymentIntentSchema.parse);

  try {
    // Get current user
    const currentUser = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
      headers: {
        Authorization: `Bear<PERSON> ${accessToken}`,
      },
      params: {
        fields: ['id', 'stripe_customer_id', 'email', 'first_name', 'last_name'],
      },
    });

    const userId = currentUser.data.id;
    let customerId = currentUser.data.stripe_customer_id;

    // Initialize Stripe
    const stripe = await useServerStripe(event);

    // Create Stripe customer if doesn't exist
    if (!customerId) {
      try {
        const customer = await stripe.customers.create({
          email: currentUser.data.email!,
          name: `${currentUser.data.first_name} ${currentUser.data.last_name}`,
        });

        // Store customer ID in Directus
        await elevatedFetch<BackendResponse<DirectusUsers>>(`/users/${userId}`, {
          method: 'PATCH',
          body: {
            stripe_customer_id: customer.id,
          },
        });

        customerId = customer.id;
      } catch (error) {
        throw createError({
          statusCode: 500,
          statusMessage: 'Failed to create Stripe customer',
        });
      }
    }

    // Fetch product details to validate and calculate totals
    const productIds = body.items.map((item) => item.id);
    const productsResponse = await dFetch<BackendResponse<Products[]>>('/items/products', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        filter: {
          id: {
            _in: productIds,
          },
        },
        fields: [
          'id',
          'creator',
          'stock_remaining',
          'translations.name',
          'price', // USD price - this is what we'll use for Stripe
          'price_jpy',
          'price_krw',
          'base_currency',
        ],
      },
    });

    const products = productsResponse.data;

    // Validate that all products belong to the same creator
    const creators = [...new Set(products.map((p) => p.creator))];
    if (creators.length > 1) {
      throw createError({
        statusCode: 400,
        statusMessage: 'All products in an order must be from the same creator',
      });
    }

    const creatorId = creators[0];
    if (!creatorId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Products must have a valid creator',
      });
    }

    // Validate stock availability
    for (const item of body.items) {
      const product = products.find((p) => p.id === item.id);
      if (!product) {
        throw createError({
          statusCode: 404,
          statusMessage: `Product ${item.id} not found`,
        });
      }

      if (
        product.stock_remaining !== null &&
        product.stock_remaining !== undefined &&
        product.stock_remaining < item.quantity
      ) {
        throw createError({
          statusCode: 400,
          statusMessage: `Insufficient stock for product ${item.id}`,
        });
      }
    }

    // Calculate total amount in USD (using product.price field)
    let totalAmountUSD = 0;

    body.items.forEach((item) => {
      const product = products.find((p) => p.id === item.id);
      if (!product) return;

      // Always use the USD price field for Stripe payments
      const itemPriceUSD = Number(product.price) || 0;
      totalAmountUSD += itemPriceUSD * item.quantity;
    });

    // Convert to cents for Stripe (Stripe expects amounts in smallest currency unit)
    const amountInCents = Math.round(totalAmountUSD * 100);

    if (amountInCents <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid order total amount',
      });
    }

    // Create Payment Intent
    const paymentIntentData: any = {
      amount: amountInCents,
      currency: 'usd', // Always USD as specified
      customer: customerId,
      // Cards only - includes all card authentication (3D Secure, SMS, app notifications)
      automatic_payment_methods: {
        enabled: true,
        allow_redirects: 'never' // Only card-based methods, no redirect payment methods
      },
      metadata: {
        user_id: userId.toString(),
        creator_id: creatorId.toString(),
        item_count: body.items.length.toString(),
        shipping_address_id: body.shipping_address_id?.toString() || '',
        // Store order items for later order creation
        order_items: JSON.stringify(body.items),
      },
    };

    // If payment method is specified, attach it but don't auto-confirm
    // We'll confirm on the client side
    if (body.payment_method_id) {
      paymentIntentData.payment_method = body.payment_method_id;
    }

    const paymentIntent = await stripe.paymentIntents.create(paymentIntentData);

    return {
      success: true,
      client_secret: paymentIntent.client_secret,
      payment_intent_id: paymentIntent.id,
      amount_usd: totalAmountUSD,
      amount_cents: amountInCents,
      creator_id: creatorId,
      item_count: body.items.length,
    };

  } catch (error) {
    console.error('Failed to create payment intent:', error);
    
    // Handle Stripe-specific errors
    if (error && typeof error === 'object' && 'type' in error) {
      throw createError({
        statusCode: 400,
        statusMessage: `Payment error: ${(error as any).message || 'Unknown payment error'}`,
      });
    }
    
    // Re-throw our custom errors
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create payment intent',
    });
  }
});
