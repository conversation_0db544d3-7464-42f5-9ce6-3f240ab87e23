import { useServerStripe } from "#stripe/server";

export default defineEventHandler(async (event) => {
  // GOAL: Create stripe customer

  const config = useRuntimeConfig();

  // Get customer profile
  const accessToken = getCookie(event, 'access_token');

  const user = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    params: {
      fields: ['*.*'],
    },
  });

  // Create stripe customer
  const stripe = await useServerStripe(event);

  const customer = await stripe.customers.create({
    email: user.data.email!,
    name: user.data.first_name + ' ' + user.data.last_name,
  });


  // Store customer ID in Directus
  await elevatedFetch<BackendResponse<DirectusUsers>>(`/users/${user.data.id}`, {
    method: 'PATCH',
    body: {
      stripe_customer_id: customer.id,
    },
  });

  return customer;

})
