import { useServerStripe } from "#stripe/server";

export default defineEventHandler(async (event) => {
  // Create a Setup Intent for saving payment methods without charging
  // This is used when users want to add a new payment method to their account

  // Get stripe customer id from directus user
  const accessToken = getCookie(event, 'access_token');

  const user = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
    headers: {
      Authorization: 'Bearer ' + accessToken,
    },
    params: {
      fields: ['stripe_customer_id', 'email', 'first_name', 'last_name', 'id', 'role.name'],
    },
  });

  const stripe = await useServerStripe(event);

  let customerId = user.data.stripe_customer_id;

  // If user doesn't have a Stripe customer ID, create one (for non-fan users who might need it)
  if (!customerId) {
    try {
      const customer = await stripe.customers.create({
        email: user.data.email!,
        name: `${user.data.first_name} ${user.data.last_name}`,
      });

      // Store customer ID in Directus
      await elevatedFetch<BackendResponse<DirectusUsers>>(`/users/${user.data.id}`, {
        method: 'PATCH',
        body: {
          stripe_customer_id: customer.id,
        },
      });

      customerId = customer.id;
    } catch (error) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to create Stripe customer',
      });
    }
  }
  
  try {
    const setupIntent = await stripe.setupIntents.create({
      customer: customerId,
      payment_method_types: ['card'],
      usage: 'off_session', // For future payments
    });

    return {
      clientSecret: setupIntent.client_secret,
      error: null,
    };
  } catch (e) {
    return {
      clientSecret: null,
      error: e,
    };
  }
});
