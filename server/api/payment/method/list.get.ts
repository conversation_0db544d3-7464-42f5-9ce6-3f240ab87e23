import { useServerStripe } from "#stripe/server";

export default defineEventHandler(async (event) => {
  // List Payment Methods
  // Purpose: Retrieves saved cards for display in checkout or card management UI.

  // Get stripe customer id from directus user
  const accessToken = getCookie(event, 'access_token');

  const user = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
    headers: {
      Authorization: 'Bearer ' + accessToken,
    },
    params: {
      fields: ['stripe_customer_id', 'email', 'first_name', 'last_name', 'id'],
    },
  });

  const stripe = await useServerStripe(event);

  let customerId = user.data.stripe_customer_id;

  // If user doesn't have a Stripe customer ID, create one (for non-fan users who might need it)
  if (!customerId) {
    try {
      const customer = await stripe.customers.create({
        email: user.data.email!,
        name: `${user.data.first_name} ${user.data.last_name}`,
      });

      // Store customer ID in Directus
      await elevatedFetch<BackendResponse<DirectusUsers>>(`/users/${user.data.id}`, {
        method: 'PATCH',
        body: {
          stripe_customer_id: customer.id,
        },
      });

      customerId = customer.id;
    } catch (error) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to create Stripe customer',
      });
    }
  }

  const paymentMethods = await stripe.paymentMethods.list({
    customer: customerId,
    type: 'card',
  });

  // Get customer's default payment method
  const customer = await stripe.customers.retrieve(customerId);
  const defaultPaymentMethodId = (customer as any).invoice_settings?.default_payment_method;

  return {
    ...paymentMethods,
    default_payment_method: defaultPaymentMethodId,
  };
})
