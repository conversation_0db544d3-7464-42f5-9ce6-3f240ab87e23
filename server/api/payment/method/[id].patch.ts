import { useServerStripe } from "#stripe/server";

export default defineEventHandler(async (event) => {
  // Update payment method billing details (only name is editable)
  
  const paymentMethodId = getRouterParam(event, 'id');
  const body = await readBody(event);
  
  if (!paymentMethodId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Payment method ID is required',
    });
  }

  if (!body.billing_details?.name) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Cardholder name is required',
    });
  }

  // Get stripe customer id from directus user
  const accessToken = getCookie(event, 'access_token');

  const user = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
    headers: {
      Authorization: 'Bearer ' + accessToken,
    },
    params: {
      fields: ['stripe_customer_id'],
    },
  });

  const stripe = await useServerStripe(event);

  if (!user.data.stripe_customer_id) {
    throw createError({
      statusCode: 400,
      statusMessage: 'No stripe customer id found',
    });
  }
  
  try {
    // Update the payment method billing details
    const updatedPaymentMethod = await stripe.paymentMethods.update(paymentMethodId, {
      billing_details: {
        name: body.billing_details.name,
      },
    });

    return {
      success: true,
      data: updatedPaymentMethod,
      error: null,
    };
  } catch (e) {
    return {
      success: false,
      error: e,
    };
  }
});
