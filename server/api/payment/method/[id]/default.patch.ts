import { useServerStripe } from "#stripe/server";

export default defineEventHandler(async (event) => {
  // Set a payment method as the default for the customer
  
  const paymentMethodId = getRouterParam(event, 'id');
  
  if (!paymentMethodId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Payment method ID is required',
    });
  }

  // Get stripe customer id from directus user
  const accessToken = getCookie(event, 'access_token');

  const user = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
    headers: {
      Authorization: 'Bearer ' + accessToken,
    },
    params: {
      fields: ['stripe_customer_id'],
    },
  });

  const stripe = await useServerStripe(event);

  if (!user.data.stripe_customer_id) {
    throw createError({
      statusCode: 400,
      statusMessage: 'No stripe customer id found',
    });
  }
  
  try {
    // Update the customer's default payment method
    await stripe.customers.update(user.data.stripe_customer_id, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    });

    return {
      success: true,
      error: null,
    };
  } catch (e) {
    return {
      success: false,
      error: e,
    };
  }
});
