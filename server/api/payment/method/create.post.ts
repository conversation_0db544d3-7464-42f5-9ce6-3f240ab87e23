import { useServerStripe } from "#stripe/server";
import z from 'zod/v4';

const schema = z.object({
  number: z.string().min(16).max(16),
  exp_month: z.number().min(1).max(12),
  exp_year: z.number().min(2023).max(2030),
  cvc: z.string().min(3).max(4),
});

export default defineEventHandler(async (event) => {
  // Create Payment Method
  // Purpose: Adds a new card via Setup Intent.

  // Get user
  const accessToken = getCookie(event, 'access_token');

  const user = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
    headers: {
      Authorization: 'Bearer ' + accessToken,
    },
    params: {
      fields: ['stripe_customer_id'],
    },
  });

  if( !user.data.stripe_customer_id){
    throw createError({
      statusCode: 400,
      statusMessage: 'No stripe customer id found',
    });
  }

  const stripe = await useServerStripe(event);

  const paymentMethod = await stripe.paymentMethods.create({
    type: 'card',
    card: {
      number: '****************',
      exp_month: 12,
      exp_year: 2025,
      cvc: '123',
    },
  });

  await stripe.paymentMethods.attach(
    paymentMethod.id,
    { customer: user.data.stripe_customer_id }
  );

  return paymentMethod;
})
