import z from 'zod/v4';

const schema = z.object({
  email: z.string().email(),
  roleName: z.enum(['fan', 'creator', 'owner']),
});

export default defineEventHandler(async (event) => {
  const { email, roleName } = await readValidatedBody(event, schema.parse);

  // Validate required fields
  if (!email) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing required fields: email',
    });
  }

  {
    try {
      const id = crypto.randomUUID();

      // Directus Register user first.
      const userRes = await elevatedFetch<BackendResponse<DirectusUsers[]>>('/users', {
        method: 'POST',
        body: {
          id,
          email,
          role: roleNameIdMap[roleName],
          status: 'inactive',
          password: 'UNVERIFIED_USER',
          password_migrated: true,
          provider: 'default',
        },
      });

      // Update user info with correct roles and status
    } catch (e: any) {
      console.warn('signup verification: ', e.message);
      // check is user is active
      const users = await elevatedFetch<BackendResponse<DirectusUsers[]>>(`/users?filter[email][_eq]=${email}`);
      console.warn('users', users);

      if (users.data[0]?.status === 'active') {
        throw createError({
          statusCode: 400,
          statusMessage: 'User already exists',
        });
      }
    }
  }

  const data = await $fetch('/api/emails/send-verification-code', {
    method: 'POST',
    body: {
      email,
      mode: 'signup',
      role: roleName,
    },
  });
  return data;
});
