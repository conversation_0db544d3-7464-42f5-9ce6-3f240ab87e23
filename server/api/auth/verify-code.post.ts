export default defineEventHandler(async (event) => {
  const body = await readBody(event);
  const { email, verificationCode } = body;

  // Validate required fields
  if (!email || !verificationCode) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing required fields: email, verificationCode',
    });
  }

  // Get the code (Directus API)
  const users = await elevatedFetch<BackendResponse<DirectusUsers[]>>(`/users?filter[email][_eq]=${email}`);
  const userData = users.data[0];
  
  // Verify the code
  if (verificationCode == userData?.verification_code) {
    // Clear the verification code in db
    console.warn('id', userData.id)
    await elevatedFetch(`/users/${userData.id}`, {
      method: 'PATCH',
      body: {
        verification_code: null,
      },
    });

    return {
      success: true,
    };
  } else {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid verification code',
    });
  }
});
