import { z } from 'zod/v4';

const schema = z.object({
  email: z.string(),
  password: z.string(),
  mode: z.enum(['fan', 'creator', 'owner']).default('fan'),
});

interface OldLoginResponse {
  access_token: string;
  refresh_token: string;
  at_expires_in: number;
  token_type: string;
  username: string;
}

const ROLES = {
  owner: 'ROLE_OWNER',
  creator: 'ROLE_CREATOR',
  fan: 'ROLE_FAN',
} as const;

export interface DirectusLoginResponse {
  expires: number;
  refresh_token: string;
  access_token: string;
}

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig();
  const body = await readValidatedBody(event, schema.parse);

  try {
    // Try Directus login first
    try {
      const loginRes = await dFetch<BackendResponse<DirectusLoginResponse>>('/auth/login', {
        method: 'POST',
        body: {
          email: body.email,
          password: body.password,
        },
      }).catch((err)=>{
        console.error('Failed to login with Directus:', err);
        throw err;
      })

      // If successful, set cookies and return response
      setCookie(event, 'access_token', loginRes.data.access_token, { maxAge: loginRes.data.expires / 1000 });
      return loginRes.data;
    } catch (directusError) {
      // Fallback to old backend login
      console.warn('Failed to login with Directus, falling back to old backend');
      console.warn('Old backend API:', config.old.backendApi);
      const data = await $fetch<BackendResponse<OldLoginResponse>>(config.old.backendApi + '/auth/login' , {
        method: 'POST',
        body: {
          username: body.email,
          password: body.password,
          roleCode: ROLES[body.mode],

        },
      });
      console.log('Old acount valid:', !data.data.access_token === false);

      if (!data.data.access_token) {
        throw createError({
          statusCode: 401,
          statusMessage: 'Unauthorized',
        });
      }

      // If credentials are valid with old backend, update the user's password in directus
      // 1. get user from directus by email
      const user = await elevatedFetch<BackendResponse<DirectusUsers[]>>(`/users?filter[email][_eq]=${body.email}`);

      if (user.data[0] && !user.data[0].password_migrated) {
        const userId = user.data[0].id;
        // 2. update user's password
        await elevatedFetch(`/users/${userId}`, {
          method: 'PATCH',
          body: {
            password: body.password,
          },
        });
      }

      // 3. Try Directus login again after password update
      const loginRes = await dFetch<BackendResponse<DirectusLoginResponse>>('/auth/login', {
        method: 'POST',
        body: {
          email: body.email,
          password: body.password,
        },
      });

      setCookie(event, 'access_token', loginRes.data.access_token, { maxAge: loginRes.data.expires / 1000 });
      setCookie(event, 'refresh_token', loginRes.data.refresh_token, {
        maxAge: 60 * 60 * 24 * 7,
      });
      return loginRes.data;
    }
  } catch (error) {
    console.error('Authentication failed completely:', error);
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }
});
