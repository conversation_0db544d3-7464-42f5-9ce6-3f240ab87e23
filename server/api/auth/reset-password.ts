// Role ID to role name mapping
const roleIdToNameMap = {
  '3d2684a9-7271-4df8-be37-6da1c9aa120b': 'fan',
  '7daf2614-50ed-42de-a3ca-fc4185305eab': 'creator',
  '15fa6fb9-5517-468f-92b6-f78fd6559a16': 'owner',
} as const;

export interface ResetPasswordResponse {
  success: boolean;
  role: 'fan' | 'creator' | 'owner';
  email: string;
  accessToken: string;
}

export default defineEventHandler(async (event) => {
  const body = await readBody(event);
  const { email, password } = body;

  // Validate required fields
  if (!email || !password) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing required fields: email, password',
    });
  }

  try {
    // Get user with role information
    const users = await elevatedFetch<BackendResponse<DirectusUsers[]>>(`/users?filter[email][_eq]=${email}&fields=id,role,email`);

    if (!users.data[0]) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found',
      });
    }

    const user = users.data[0];
    const userId = user.id;
    const roleId = user.role as string;

    // Update user's password
    await elevatedFetch(`/users/${userId}`, {
      method: 'PATCH',
      body: {
        password,
      },
    });

    // Automatically log the user in after password reset
    const loginResponse = await dFetch<BackendResponse<{access_token: string, refresh_token: string, expires: number}>>('/auth/login', {
      method: 'POST',
      body: {
        email,
        password,
      },
    });

    // Set authentication cookies (same as login endpoint)
    setCookie(event, 'access_token', loginResponse.data.access_token, {
      maxAge: loginResponse.data.expires / 1000
    });

    // Map role ID to role name
    const roleName = roleIdToNameMap[roleId as keyof typeof roleIdToNameMap] || 'fan';

    return {
      success: true,
      role: roleName,
      email: email,
      accessToken: loginResponse.data.access_token,
    } as ResetPasswordResponse;

  } catch (error: any) {
    console.error('Failed to reset password:', error);

    // If it's already a createError, re-throw it
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to reset password',
    });
  }
});
