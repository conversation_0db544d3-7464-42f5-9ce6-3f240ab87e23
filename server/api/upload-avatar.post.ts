import { z } from 'zod/v4';

const schema = z.object({
  avatar: z.string(), // base64 data
  filename: z.string().optional(),
});

export default defineEventHandler(async (event) => {
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  const body = await readValidatedBody(event, schema.parse);

  try {
    // Upload avatar to Directus
    const fileObject = await uploadBase64ToDirectus(
      body.avatar, 
      body.filename || `avatar-${Date.now()}`
    );

    return {
      fileId: fileObject?.id,
      success: true,
    };
  } catch (error) {
    console.error('Failed to upload avatar:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to upload avatar',
    });
  }
});
