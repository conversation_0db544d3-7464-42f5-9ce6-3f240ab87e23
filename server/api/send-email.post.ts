import { z } from 'zod/v4';

const emailSchema = z.object({
  to: z.string().email().or(z.array(z.string().email())),
  subject: z.string(),
  message: z.string(),
  type: z.enum(['text', 'html']).default('text'),
  from: z.string().email().optional(),
  replyTo: z.string().email().optional(),
});

export default defineEventHandler(async (event) => {
  try {
    const body = await readValidatedBody(event, emailSchema.parse);
    const { to, subject, message, type = 'text', from, replyTo } = body;

    // Validate required fields
    if (!to || !subject || !message) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields: to, subject, message',
      });
    }

    let result;

    // Send email based on type
    switch (type) {
      case 'html':
        result = await sendHtmlEmail(to, subject, message);
        break;
      case 'text':
      default:
        result = await sendSimpleEmail(to, subject, message);
        break;
    }

    if (!result.success) {
      throw createError({
        statusCode: 500,
        statusMessage: `Failed to send email: ${result.error}`,
      });
    }

    return {
      success: true,
      messageId: result.messageId,
      message: 'Email sent successfully',
    };
  } catch (error: any) {
    console.error('Email API error:', error);

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
    });
  }
});
