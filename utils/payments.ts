// Unified payment utilities for card brands and payment method handling

export interface CardBrand {
  name: string;
  displayName: string;
  logo: string;
}

// All supported card brands with their logos and display names
export const CARD_BRANDS: CardBrand[] = [
  {
    name: 'visa',
    displayName: 'Visa',
    logo: '/images/card-visa.svg'
  },
  {
    name: 'mastercard',
    displayName: 'Mastercard',
    logo: '/images/card-mastercard.svg'
  },
  {
    name: 'amex',
    displayName: 'American Express',
    logo: '/images/card-amex.svg'
  },
  {
    name: 'discover',
    displayName: 'Discover',
    logo: '/images/card-default.svg'
  },
  {
    name: 'jcb',
    displayName: 'JCB',
    logo: '/images/card-jcb.svg'
  },
  {
    name: 'diners',
    displayName: 'Diners Club',
    logo: '/images/card-diners.svg'
  },
  {
    name: 'unionpay',
    displayName: 'UnionPay',
    logo: '/images/card-unionpay.svg'
  }
];

// Get card brand display name
export const getCardBrandDisplay = (brand?: string): string => {
  if (!brand) return 'Card';
  
  const cardBrand = CARD_BRANDS.find(b => b.name === brand);
  return cardBrand?.displayName || brand.charAt(0).toUpperCase() + brand.slice(1);
};

// Get card brand logo path
export const getCardBrandLogo = (brand?: string): string => {
  if (!brand) return '/images/card-default.svg';
  
  const cardBrand = CARD_BRANDS.find(b => b.name === brand);
  return cardBrand?.logo || '/images/card-default.svg';
};

// Get all card brands for display (useful for payment method modal)
export const getAllCardBrands = (): CardBrand[] => {
  return CARD_BRANDS;
};

// Get major card brands only (for compact displays like payment method modal)
export const getMajorCardBrands = (): CardBrand[] => {
  return CARD_BRANDS.filter(brand =>
    ['visa', 'mastercard', 'amex', 'jcb'].includes(brand.name)
  );
};

// Simple card brand detection based on card number patterns (fallback for manual detection)
export const detectCardBrand = (cardNumber: string): string => {
  // Remove spaces and non-digits
  const cleanNumber = cardNumber.replace(/\D/g, '');
  
  // Card brand patterns (simplified)
  if (cleanNumber.startsWith('4')) return 'visa';
  if (cleanNumber.startsWith('5') || cleanNumber.startsWith('2')) return 'mastercard';
  if (cleanNumber.startsWith('34') || cleanNumber.startsWith('37')) return 'amex';
  if (cleanNumber.startsWith('6')) return 'discover';
  if (cleanNumber.startsWith('35')) return 'jcb';
  if (cleanNumber.startsWith('30') || cleanNumber.startsWith('38')) return 'diners';
  if (cleanNumber.startsWith('62')) return 'unionpay';
  
  return '';
};
