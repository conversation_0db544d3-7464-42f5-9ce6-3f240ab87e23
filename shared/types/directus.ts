export type AboutPage = {
  date_created?: string | null;
  date_updated?: string | null;
  id: number;
  translations: any[] | AboutPageTranslations[];
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type AboutPageTranslations = {
  about_page_id?: number | AboutPage | null;
  html?: string | null;
  id: number;
  languages_id?: number | Languages | null;
};

export type Addresses = {
  address_line_1?: string | null;
  address_line_2?: string | null;
  city?: string | null;
  country?: string | null;
  date_created?: string | null;
  date_updated?: string | null;
  id: number;
  name?: string | null;
  phone_number?: string | null;
  postcode?: string | null;
  province?: string | null;
  user?: string | DirectusUsers | null;
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type Cart = {
  date_created?: string | null;
  date_updated?: string | null;
  id: number;
  quantity?: number | null;
  status: string;
  user?: string | DirectusUsers | null;
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type Companies = {
  contact: string;
  country?: string | null;
  date_created?: string | null;
  date_updated?: string | null;
  id: number;
  image?: string | DirectusFiles | null;
  name?: string | null;
  status: string;
  url?: string | null;
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type Creator = {
  address: string;
  avatar?: string | DirectusFiles | null;
  city?: string | null;
  country?: string | null;
  date_created?: string | null;
  date_updated?: string | null;
  first_name?: string | null;
  id: number;
  introduction?: string | null;
  last_name?: string | null;
  nickname?: string | null;
  orders: any[] | Orders[];
  postal_code?: string | null;
  products: any[] | Products[];
  province?: string | null;
  sort?: number | null;
  status: string;
  street_address_1?: string | null;
  street_address_2?: string | null;
  user?: string | DirectusUsers | null;
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type DirectusAccess = {
  id: string;
  policy: string | DirectusPolicies;
  role?: string | DirectusRoles | null;
  sort?: number | null;
  user?: string | DirectusUsers | null;
};

export type DirectusActivity = {
  action: string;
  collection: string;
  id: number;
  ip?: string | null;
  item: string;
  origin?: string | null;
  revisions: any[] | DirectusRevisions[];
  timestamp: string;
  user?: string | DirectusUsers | null;
  user_agent?: string | null;
};

export type DirectusCollections = {
  accountability?: string | null;
  archive_app_filter: boolean;
  archive_field?: string | null;
  archive_value?: string | null;
  collapse: string;
  collection: string;
  color?: string | null;
  display_template?: string | null;
  group?: string | DirectusCollections | null;
  hidden: boolean;
  icon?: string | null;
  item_duplication_fields?: unknown | null;
  note?: string | null;
  preview_url?: string | null;
  singleton: boolean;
  sort?: number | null;
  sort_field?: string | null;
  translations?: unknown | null;
  unarchive_value?: string | null;
  versioning: boolean;
};

export type DirectusComments = {
  collection: string | DirectusCollections;
  comment: string;
  date_created?: string | null;
  date_updated?: string | null;
  id: string;
  item: string;
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type DirectusDashboards = {
  color?: string | null;
  date_created?: string | null;
  icon: string;
  id: string;
  name: string;
  note?: string | null;
  panels: any[] | DirectusPanels[];
  user_created?: string | DirectusUsers | null;
};

export type DirectusExtensions = {
  bundle?: string | null;
  enabled: boolean;
  folder: string;
  id: string;
  source: string;
};

export type DirectusFields = {
  collection: string | DirectusCollections;
  conditions?: unknown | null;
  display?: string | null;
  display_options?: unknown | null;
  field: string;
  group?: string | DirectusFields | null;
  hidden: boolean;
  id: number;
  interface?: string | null;
  note?: string | null;
  options?: unknown | null;
  readonly: boolean;
  required?: boolean | null;
  sort?: number | null;
  special?: unknown | null;
  translations?: unknown | null;
  validation?: unknown | null;
  validation_message?: string | null;
  width?: string | null;
};

export type DirectusFiles = {
  charset?: string | null;
  created_on: string;
  description?: string | null;
  duration?: number | null;
  embed?: string | null;
  filename_disk?: string | null;
  filename_download: string;
  filesize?: number | null;
  focal_point_x?: number | null;
  focal_point_y?: number | null;
  folder?: string | DirectusFolders | null;
  height?: number | null;
  id: string;
  location?: string | null;
  metadata?: unknown | null;
  modified_by?: string | DirectusUsers | null;
  modified_on: string;
  storage: string;
  tags?: unknown | null;
  title?: string | null;
  tus_data?: unknown | null;
  tus_id?: string | null;
  type?: string | null;
  uploaded_by?: string | DirectusUsers | null;
  uploaded_on?: string | null;
  width?: number | null;
};

export type DirectusFlows = {
  accountability?: string | null;
  color?: string | null;
  date_created?: string | null;
  description?: string | null;
  icon?: string | null;
  id: string;
  name: string;
  operation?: string | DirectusOperations | null;
  operations: any[] | DirectusOperations[];
  options?: unknown | null;
  status: string;
  trigger?: string | null;
  user_created?: string | DirectusUsers | null;
};

export type DirectusFolders = {
  id: string;
  name: string;
  parent?: string | DirectusFolders | null;
};

export type DirectusMigrations = {
  name: string;
  timestamp?: string | null;
  version: string;
};

export type DirectusNotifications = {
  collection?: string | null;
  id: number;
  item?: string | null;
  message?: string | null;
  recipient: string | DirectusUsers;
  sender?: string | DirectusUsers | null;
  status?: string | null;
  subject: string;
  timestamp?: string | null;
};

export type DirectusOperations = {
  date_created?: string | null;
  flow: string | DirectusFlows;
  id: string;
  key: string;
  name?: string | null;
  options?: unknown | null;
  position_x: number;
  position_y: number;
  reject?: string | DirectusOperations | null;
  resolve?: string | DirectusOperations | null;
  type: string;
  user_created?: string | DirectusUsers | null;
};

export type DirectusPanels = {
  color?: string | null;
  dashboard: string | DirectusDashboards;
  date_created?: string | null;
  height: number;
  icon?: string | null;
  id: string;
  name?: string | null;
  note?: string | null;
  options?: unknown | null;
  position_x: number;
  position_y: number;
  show_header: boolean;
  type: string;
  user_created?: string | DirectusUsers | null;
  width: number;
};

export type DirectusPermissions = {
  action: string;
  collection: string;
  fields?: unknown | null;
  id: number;
  permissions?: unknown | null;
  policy: string | DirectusPolicies;
  presets?: unknown | null;
  validation?: unknown | null;
};

export type DirectusPolicies = {
  admin_access: boolean;
  app_access: boolean;
  description?: string | null;
  enforce_tfa: boolean;
  icon: string;
  id: string;
  ip_access?: unknown | null;
  name: string;
  permissions: any[] | DirectusPermissions[];
  roles: any[] | DirectusAccess[];
  users: any[] | DirectusAccess[];
};

export type DirectusPresets = {
  bookmark?: string | null;
  collection?: string | null;
  color?: string | null;
  filter?: unknown | null;
  icon?: string | null;
  id: number;
  layout?: string | null;
  layout_options?: unknown | null;
  layout_query?: unknown | null;
  refresh_interval?: number | null;
  role?: string | DirectusRoles | null;
  search?: string | null;
  user?: string | DirectusUsers | null;
};

export type DirectusRelations = {
  id: number;
  junction_field?: string | null;
  many_collection: string;
  many_field: string;
  one_allowed_collections?: unknown | null;
  one_collection?: string | null;
  one_collection_field?: string | null;
  one_deselect_action: string;
  one_field?: string | null;
  sort_field?: string | null;
};

export type DirectusRevisions = {
  activity: number | DirectusActivity;
  collection: string;
  data?: unknown | null;
  delta?: unknown | null;
  id: number;
  item: string;
  parent?: number | DirectusRevisions | null;
  version?: string | DirectusVersions | null;
};

export type DirectusRoles = {
  children: any[] | DirectusRoles[];
  description?: string | null;
  icon: string;
  id: string;
  name: string;
  parent?: string | DirectusRoles | null;
  policies: any[] | DirectusAccess[];
  users: any[] | DirectusUsers[];
  users_group: string;
};

export type DirectusSessions = {
  expires: string;
  ip?: string | null;
  next_token?: string | null;
  origin?: string | null;
  share?: string | DirectusShares | null;
  token: string;
  user?: string | DirectusUsers | null;
  user_agent?: string | null;
};

export type DirectusSettings = {
  accepted_terms?: boolean | null;
  auth_login_attempts?: number | null;
  auth_password_policy?: string | null;
  basemaps?: unknown | null;
  command_palette_settings?: unknown | null;
  custom_aspect_ratios?: unknown | null;
  custom_css?: string | null;
  default_appearance: string;
  default_language: string;
  default_theme_dark?: string | null;
  default_theme_light?: string | null;
  id: number;
  mapbox_key?: string | null;
  module_bar?: unknown | null;
  project_color: string;
  project_descriptor?: string | null;
  project_id?: string | null;
  project_logo?: string | DirectusFiles | null;
  project_name: string;
  project_url?: string | null;
  public_background?: string | DirectusFiles | null;
  public_favicon?: string | DirectusFiles | null;
  public_foreground?: string | DirectusFiles | null;
  public_note?: string | null;
  public_registration: boolean;
  public_registration_email_filter?: unknown | null;
  public_registration_role?: string | DirectusRoles | null;
  public_registration_verify_email: boolean;
  report_bug_url?: string | null;
  report_error_url?: string | null;
  report_feature_url?: string | null;
  storage_asset_presets?: unknown | null;
  storage_asset_transform?: string | null;
  storage_default_folder?: string | DirectusFolders | null;
  theme_dark_overrides?: unknown | null;
  theme_light_overrides?: unknown | null;
  theming_group: string;
  visual_editor_urls?: unknown | null;
};

export type DirectusShares = {
  collection: string | DirectusCollections;
  date_created?: string | null;
  date_end?: string | null;
  date_start?: string | null;
  id: string;
  item: string;
  max_uses?: number | null;
  name?: string | null;
  password?: string | null;
  role?: string | DirectusRoles | null;
  times_used?: number | null;
  user_created?: string | DirectusUsers | null;
};

export type DirectusTranslations = {
  id: string;
  key: string;
  language: string;
  value: string;
};

export type DirectusUsers = {
  address?: string | null;
  addresses: any[] | Addresses[];
  appearance?: string | null;
  auth_data?: unknown | null;
  avatar?: string | DirectusFiles | null;
  birthday?: string | null;
  city?: string | null;
  country?: string | null;
  creator_profile: any[] | Creator[];
  currency_preference?: string | null;
  default_address?: number | Addresses | null;
  description?: string | null;
  email?: string | null;
  email_notifications?: boolean | null;
  external_identifier?: string | null;
  favorites: any[] | JunctionDirectusUsersProducts[];
  first_name?: string | null;
  id: string;
  ip_owner_profile: any[] | IpOwner[];
  language?: string | null;
  language_preference?: string | null;
  last_access?: string | null;
  last_name?: string | null;
  last_page?: string | null;
  location?: string | null;
  nickname?: string | null;
  orders: any[] | Orders[];
  password?: string | null;
  password_migrated?: boolean | null;
  policies: any[] | DirectusAccess[];
  postcode?: string | null;
  provider: string;
  province?: string | null;
  role?: string | DirectusRoles | null;
  status: string;
  stripe_customer_id?: string | null;
  tags?: unknown | null;
  tfa_secret?: string | null;
  theme_dark?: string | null;
  theme_dark_overrides?: unknown | null;
  theme_light?: string | null;
  theme_light_overrides?: unknown | null;
  title?: string | null;
  token?: string | null;
  verification_code?: string | null;
};

export type DirectusVersions = {
  collection: string | DirectusCollections;
  date_created?: string | null;
  date_updated?: string | null;
  delta?: unknown | null;
  hash?: string | null;
  id: string;
  item: string;
  key: string;
  name?: string | null;
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type DirectusWebhooks = {
  actions: unknown;
  collections: unknown;
  data: boolean;
  headers?: unknown | null;
  id: number;
  method: string;
  migrated_flow?: string | DirectusFlows | null;
  name: string;
  status: string;
  url: string;
  was_active_before_deprecation: boolean;
};

export type Ip = {
  category: number | IpCategories;
  date_created?: string | null;
  date_updated?: string | null;
  id: number;
  images: any[] | IpFiles[];
  is_featured?: boolean | null;
  keywords?: string | null;
  main_image?: string | DirectusFiles | null;
  old_id?: string | null;
  owner?: number | IpOwner | null;
  products: any[] | Products[];
  sort?: number | null;
  status: string;
  translations: any[] | IpTranslations[];
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type IpCategories = {
  code: string;
  date_created?: string | null;
  date_updated?: string | null;
  id: number;
  sort?: number | null;
  status: string;
  translations: any[] | IpCategoriesTranslations[];
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type IpCategoriesTranslations = {
  id: number;
  ip_categories_id?: number | IpCategories | null;
  languages_id?: number | Languages | null;
  name?: string | null;
};

export type IpFiles = {
  directus_files_id?: string | DirectusFiles | null;
  id: number;
  ip_id?: number | Ip | null;
};

export type IpOwner = {
  business_license?: string | DirectusFiles | null;
  company: string;
  company_name?: string | null;
  company_phone_number?: string | null;
  company_role?: string | null;
  country?: string | null;
  date_created?: string | null;
  date_updated?: string | null;
  homepage_link?: string | null;
  id: number;
  image?: string | DirectusFiles | null;
  ips: any[] | Ip[];
  name?: string | null;
  status: string;
  user?: string | DirectusUsers | null;
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type IpTranslations = {
  description?: string | null;
  id: number;
  ip_id?: number | Ip | null;
  languages_id?: number | Languages | null;
  name?: string | null;
  refresh?: boolean | null;
};

export type JunctionDirectusUsersProducts = {
  directus_users_id?: string | DirectusUsers | null;
  id: number;
  products_id?: number | Products | null;
};

export type Languages = {
  code: string;
  direction: string;
  id: number;
  name: string;
};

export type Messages = {
  date_created?: string | null;
  date_updated?: string | null;
  emoji?: string | null;
  id: number;
  order?: number | Orders | null;
  status: string;
  text?: string | null;
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type OrderItem = {
  date_created?: string | null;
  date_updated?: string | null;
  id: number;
  message?: string | null;
  order?: number | Orders | null;
  price_at_order?: number | null;
  price_jpy_at_order?: number | null;
  price_krw_at_order?: number | null;
  product?: number | Products | null;
  product_name_at_order?: string | null;
  quantity?: number | null;
  status: string;
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type Orders = {
  base_tax_amount?: number | null;
  base_total_amount?: number | null;
  base_total_amount_after_discount?: number | null;
  Chat: string;
  chat_messages: any[] | Messages[];
  creator?: number | Creator | null;
  currency_code?: string | null;
  date_created?: string | null;
  date_updated?: string | null;
  id: number;
  order_items: any[] | OrderItem[];
  payment_id?: string | null;
  reviews: any[] | Review[];
  shipping_address_line_1?: string | null;
  shipping_address_line_2?: string | null;
  shipping_city?: string | null;
  shipping_country?: string | null;
  shipping_name?: string | null;
  shipping_phone_number?: string | null;
  shipping_postcode?: string | null;
  shipping_province?: string | null;
  status: string;
  total_amount?: number | null;
  total_amount_after_discount?: number | null;
  user?: string | DirectusUsers | null;
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type ProductApplicationCategory = {
  code?: string | null;
  date_created?: string | null;
  date_updated?: string | null;
  id: number;
  name?: string | null;
  status: string;
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type ProductCategories = {
  code?: string | null;
  date_created?: string | null;
  date_updated?: string | null;
  id: number;
  sort?: number | null;
  status: string;
  translations: any[] | ProductCategoriesTranslations[];
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type ProductCategoriesTranslations = {
  id: number;
  languages_id?: number | Languages | null;
  name?: string | null;
  product_categories_id?: number | ProductCategories | null;
};

export type Products = {
  approval_comment?: string | null;
  base_currency?: string | null;
  category?: number | ProductCategories | null;
  creator?: number | Creator | null;
  date_created?: string | null;
  date_updated?: string | null;
  discount?: number | null;
  discount_percentage?: number | null;
  id: number;
  images: any[] | ProductsFiles[];
  inventory: string;
  ip?: number | Ip | null;
  is_new?: boolean | null;
  keywords?: string | null;
  main_image?: string | DirectusFiles | null;
  metrics: string;
  old_id?: string | null;
  price?: number | null;
  price_jpy?: number | null;
  price_krw?: number | null;
  pricing: string;
  product_application_category?: number | ProductApplicationCategory | null;
  revenue_total?: number | null;
  reviews: any[] | Review[];
  sort?: number | null;
  status: string;
  stock_remaining?: number | null;
  stock_total?: number | null;
  translations: any[] | ProductsTranslations[];
  units_sold?: number | null;
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type ProductsFiles = {
  directus_files_id?: string | DirectusFiles | null;
  id: number;
  products_id?: number | Products | null;
};

export type ProductsTranslations = {
  description?: string | null;
  id: number;
  languages_id?: number | Languages | null;
  name?: string | null;
  products_id?: number | Products | null;
  test?: boolean | null;
};

export type Review = {
  date_created?: string | null;
  date_updated?: string | null;
  dislike_count?: number | null;
  id: number;
  like_count?: number | null;
  order?: number | Orders | null;
  product?: number | Products | null;
  rating?: number | null;
  status: string;
  text?: string | null;
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type WebsiteSettings = {
  bypass_key?: string | null;
  date_created?: string | null;
  date_updated?: string | null;
  exchange_rates?: unknown | null;
  fan_site: string;
  forex: string;
  id: number;
  is_fan_maintenance?: boolean | null;
  jpy_exchange_rate?: number | null;
  krw_exchange_rate?: number | null;
  open_exchange_rate_app_id?: string | null;
  public_creator_domain?: string | null;
  public_fan_domain?: string | null;
  public_owner_domain?: string | null;
  usd_exchange_rate?: number | null;
  user_created?: string | DirectusUsers | null;
  user_updated?: string | DirectusUsers | null;
};

export type CustomDirectusTypes = {
  about_page: AboutPage;
  about_page_translations: AboutPageTranslations[];
  addresses: Addresses[];
  cart: Cart[];
  companies: Companies[];
  creator: Creator[];
  directus_access: DirectusAccess[];
  directus_activity: DirectusActivity[];
  directus_collections: DirectusCollections[];
  directus_comments: DirectusComments[];
  directus_dashboards: DirectusDashboards[];
  directus_extensions: DirectusExtensions[];
  directus_fields: DirectusFields[];
  directus_files: DirectusFiles[];
  directus_flows: DirectusFlows[];
  directus_folders: DirectusFolders[];
  directus_migrations: DirectusMigrations[];
  directus_notifications: DirectusNotifications[];
  directus_operations: DirectusOperations[];
  directus_panels: DirectusPanels[];
  directus_permissions: DirectusPermissions[];
  directus_policies: DirectusPolicies[];
  directus_presets: DirectusPresets[];
  directus_relations: DirectusRelations[];
  directus_revisions: DirectusRevisions[];
  directus_roles: DirectusRoles[];
  directus_sessions: DirectusSessions[];
  directus_settings: DirectusSettings;
  directus_shares: DirectusShares[];
  directus_translations: DirectusTranslations[];
  directus_users: DirectusUsers[];
  directus_versions: DirectusVersions[];
  directus_webhooks: DirectusWebhooks[];
  ip: Ip[];
  ip_categories: IpCategories[];
  ip_categories_translations: IpCategoriesTranslations[];
  ip_files: IpFiles[];
  ip_owner: IpOwner[];
  ip_translations: IpTranslations[];
  junction_directus_users_products: JunctionDirectusUsersProducts[];
  languages: Languages[];
  messages: Messages[];
  order_item: OrderItem[];
  orders: Orders[];
  product_application_category: ProductApplicationCategory[];
  product_categories: ProductCategories[];
  product_categories_translations: ProductCategoriesTranslations[];
  products: Products[];
  products_files: ProductsFiles[];
  products_translations: ProductsTranslations[];
  review: Review[];
  website_settings: WebsiteSettings;
};
