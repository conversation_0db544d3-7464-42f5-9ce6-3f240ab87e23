export type ProductCategoryCode = 'APP' | 'HOME' | 'TECH' | 'ART' | 'TOYS' | 'LIT' | 'SPORT' | 'COLLAB' | 'OTH' | '';

export const PRODUCT_CATEGORIES: { value: ProductCategoryCode; label: string }[] = [
  { value: 'APP', label: 'global.apparel_and_accessories' },
  { value: 'HOME', label: 'global.home_and_lifestyle' },
  { value: 'TECH', label: 'global.tech_and_gadgets' },
  { value: 'ART', label: 'global.art_and_crafts' },
  { value: 'TOYS', label: 'global.toys_and_games' },
  { value: 'LIT', label: 'global.literature_and_media' },
  { value: 'SPORT', label: 'global.sports_and_fitness' },
  { value: 'COLLAB', label: 'global.collaboration_goods' },
  { value: 'OTH', label: 'global.others' },
];

export const IP_CATEGORIES = [
  { value: '', label: 'global.all' },
  { value: 'game', label: 'global.game' },
  { value: 'animation', label: 'global.animation' },
  { value: 'novel', label: 'global.novel' },
  { value: 'fantasy', label: 'global.fantasy' },
  { value: 'drama', label: 'global.drama' },
  { value: 'celebrity', label: 'global.celebrity' },
];

export const CURRENCIES = [
  { value: 'USD', label: 'USD $' },
  { value: 'JPY', label: 'JPY ¥' },
  { value: 'KRW', label: 'KRW ₩' },
];

export const UPLOAD_FOLDERS = {
  user: 'e15ab896-96c2-4886-9071-c93e0529ff78',
  product: 'fe1d3239-bc75-42cc-b937-7fe23a4e43e8',
  ip: '8036543e-dce2-4c84-9ee7-c53273ffb2ce',
  company: '3cb8e671-e115-4332-896d-2bb38604c66c'
}

export const LANGUAGE_LOCAL_ID = {
  en: 1,
  jp: 2,
  kr: 3,
}

// Locale-specific price ranges for filtering
export const PRICE_RANGES = {
  en: [
    { min: '', max: '25', label: '~$25' },
    { min: '25', max: '100', label: '$25-$100' },
    { min: '100', max: '500', label: '$100-$500' },
    { min: '500', max: '', label: '$500+' },
  ],
  jp: [
    { min: '', max: '3000', label: '~¥3,000' },
    { min: '3000', max: '12000', label: '¥3,000-¥12,000' },
    { min: '12000', max: '60000', label: '¥12,000-¥60,000' },
    { min: '60000', max: '', label: '¥60,000+' },
  ],
  kr: [
    { min: '', max: '35000', label: '~₩35,000' },
    { min: '35000', max: '140000', label: '₩35,000-₩140,000' },
    { min: '140000', max: '700000', label: '₩140,000-₩700,000' },
    { min: '700000', max: '', label: '₩700,000+' },
  ],
}

// Get price field name based on locale
export const getPriceFieldByLocale = (locale: string): string => {
  switch (locale) {
    case 'jp':
      return 'price_jpy';
    case 'kr':
      return 'price_krw';
    case 'en':
    default:
      return 'price';
  }
}

export const BUSINESS_ROLES = [
  {
    value: 'ceo_founder',
    label: 'global.ceo_founder',
  },
  { value: 'mngr_tm', label: 'global.mngr_tm' },
  { value: 'sn_sc_cl', label: 'global.sn_sc_cl' },
  { value: 'an_asc', label: 'global.an_asc' },
  { value: 'soft_en_dev', label: 'global.soft_en_dev' },
  { value: 'sales_acc_exec', label: 'global.sales_acc_exec' },
  { value: 'marketing_specialist', label: 'global.marketing_specialist' },
  { value: 'cs_sr', label: 'global.cs_sr' },
  { value: 'hr_talent', label: 'global.hr_talent' },
  { value: 'admin_as', label: 'global.admin_as' },
];

export const PHONE_PREFIXES = [
  { value: '+1', label: '+1 (USA/Canada)' },
  { value: '+44', label: '+44 (UK)' },
  { value: '+91', label: '+91 (India)' },
  { value: '+81', label: '+81 (Japan)' },
  { value: '+82', label: '+82 (South Korea)' },
  { value: '+86', label: '+86 (China)' },
  { value: '+852', label: '+852 (Hong Kong)' },
  { value: '+853', label: '+853 (Macau)' },
  { value: '+60', label: '+60 (Malaysia)' },
  { value: '+65', label: '+65 (Singapore)' },
  { value: '+66', label: '+66 (Thailand)' },
  { value: '+62', label: '+62 (Indonesia)' },
  { value: '+63', label: '+63 (Philippines)' },
  { value: '+886', label: '+886 (Taiwan)' },
  { value: '+84', label: '+84 (Vietnam)' },
  { value: '+95', label: '+95 (Myanmar)' },
  { value: '+673', label: '+673 (Brunei)' },
  { value: '+974', label: '+974 (Qatar)' },
  { value: '+968', label: '+968 (Oman)' },
];