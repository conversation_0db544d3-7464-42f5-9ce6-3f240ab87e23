/**
 * UI utility functions for common interface elements
 */

// Type for Nuxt UI badge colors
export type BadgeColor = 'error' | 'success' | 'warning' | 'primary' | 'neutral' | 'secondary' | 'info';

/**
 * Get badge color based on product status
 * @param status - Product status string
 * @returns Badge color for Nuxt UI components
 */
export const getStatusColor = (status: string): BadgeColor => {
  const statusColors: Record<string, BadgeColor> = {
    approved: 'success',
    completed: 'success',
    pending: 'warning',
    processing: 'warning',
    published: 'success',
    rejected: 'error',
    refunded: 'error',
    archived: 'neutral',
    shipped: 'info',
    unpublished: 'neutral'
  };
  return statusColors[status] || 'neutral';
};
