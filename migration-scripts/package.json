{"name": "ipgo-migration-scripts", "version": "1.0.0", "description": "Migration scripts for IPGO project", "private": true, "type": "module", "engines": {"node": ">=22.0.0"}, "scripts": {"migrate:users": "node --loader tsx migrate-users.ts", "migrate:ips": "node --loader tsx migrate-ips.ts", "migrate:products": "node --loader tsx migrate-products.ts", "directus:migrate": "node directus-migrator.js", "codegen:source": "kysely-codegen --dialect mysql --out-file source-db.ts --env-file .env.source", "codegen:target": "kysely-codegen --dialect postgres --out-file target-db.ts --env-file .env.target"}, "dependencies": {"@directus/sdk": "^19.1.0", "bottleneck": "^2.19.5", "cross-fetch": "^4.1.0", "kysely": "^0.28.2", "mysql2": "^3.14.1", "pg": "^8.16.0"}, "devDependencies": {"@types/node": "^22.0.0", "kysely-codegen": "^0.18.5", "tsx": "^4.0.0", "typescript": "^5.0.0"}}