import { <PERSON>ysely, PostgresDial<PERSON>t, MysqlDialect, sql } from 'kysely';
import { Pool } from 'pg';
import { createPool } from 'mysql2';
import type { DB as sDB } from './source-db';
import type { DB } from './target-db';
import { createDirectus, authentication, rest, importFile, updateItem } from '@directus/sdk';
import { config } from 'dotenv';
import Bottleneck from 'bottleneck';

// Load environment variables
config();

// Configuration with environment variable validation
interface MigrationConfig {
  targetDirectusUrl: string;
  directusEmail: string;
  directusPassword: string;
  productImagesFolderId: string;
  defaultCategoryId: number;
  geminiApiKey: string;
  databaseUrl: string;
  sourceDatabaseUrl: string;
}

function validateAndGetConfig(): MigrationConfig {
  const requiredEnvVars = [
    'DATABASE_URL',
    'SOURCE_DATABASE_URL',
    'DIRECTUS_URL',
    'DIRECTUS_EMAIL',
    'DIRECTUS_PASSWORD',
    'PRODUCT_IMAGES_FOLDER_ID',
    'GEMINI_API_KEY'
  ];

  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  return {
    targetDirectusUrl: process.env.DIRECTUS_URL!,
    directusEmail: process.env.DIRECTUS_EMAIL!,
    directusPassword: process.env.DIRECTUS_PASSWORD!,
    productImagesFolderId: process.env.PRODUCT_IMAGES_FOLDER_ID!,
    defaultCategoryId: parseInt(process.env.DEFAULT_CATEGORY_ID || '21'),
    geminiApiKey: process.env.GEMINI_API_KEY!,
    databaseUrl: process.env.DATABASE_URL!,
    sourceDatabaseUrl: process.env.SOURCE_DATABASE_URL!
  };
}

// Validate configuration
const config_vars = validateAndGetConfig();

const targetDirectus = createDirectus(config_vars.targetDirectusUrl).with(rest()).with(authentication());

// Rate limiter: <15 requests per minute to respect API limits
const rateLimiter = new Bottleneck({
  minTime: 100, // 3.5 seconds between requests (<15 per minute)
  maxConcurrent: 10 // Process one at a time
});

// Login to Directus
try {
  await targetDirectus.login(config_vars.directusEmail, config_vars.directusPassword);
  console.log('✅ Successfully connected to Directus');
} catch (error) {
  console.error('❌ Failed to connect to Directus:', error);
  process.exit(1);
}

// Target database (PostgreSQL - Directus)
export const db = new Kysely<DB>({
  dialect: new PostgresDialect({
    pool: new Pool({
      connectionString: config_vars.databaseUrl,
      ssl: {
        rejectUnauthorized: false,
        checkServerIdentity: () => undefined
      }
    }),
  }),
});

// Source database (MySQL - Legacy system)
const sourceDb = new Kysely<sDB>({
  dialect: new MysqlDialect({
    pool: createPool({
      uri: config_vars.sourceDatabaseUrl,
    }),
  }),
});

export interface SourceProduct {
  id: string;
  user_id: string;
  ip_id: string;
  application_type_id: string;
  product_name_1: string;
  product_name_2: string;
  product_name_3: string;
  product_name_4: string;
  product_name_5: string;
  product_desc_1: string;
  product_desc_2: string;
  product_desc_3: string;
  product_desc_4: string;
  product_desc_5: string;
  base_currency: string;
  product_price_jpy: string;
  product_price_krw: string;
  product_price: string;
  total_stock: number;
  unit_sold: number;
  rem_stock: number;
  total_rev_gen: string;
  product_status: string;
  applied_dt: Date;
  product_sale_status: string;
  product_discount: string;
  is_new: string;
  is_approve_reject: string;
  created_by: string;
  updated_by: string;
  created_dt: Date;
  updated_dt: Date;
  version: null;
  tenant_code: string;
  creator_id: string;
  creator_nickname: string;
  creator_email: string;
  avatar_file_id: string;
  avatar_url: string;
  app_type_name: string;
  app_type_code: string;
  product_file_ids: string;
  product_image_paths: string;
  product_image_filenames: string;
  product_cat_codes: string;
  feedback_desc: string;
}

async function main() {
  try {
    console.log('🚀 Starting Product migration with direct database access...');

    // Get product data with joins for creator, images, application type, and feedback
    const currentProducts = await sql<SourceProduct>`
        SELECT p.*,
               u.id as creator_id, u.nickname as creator_nickname, u.avatar_file_id, u.email as creator_email,
               f.path as avatar_url,
               at.app_type_name, at.app_type_code,
               fb.feedback_desc,
               GROUP_CONCAT(DISTINCT pf.file_id ORDER BY pf.created_dt ASC) as product_file_ids,
               GROUP_CONCAT(DISTINCT tf.path ORDER BY pf.created_dt ASC) as product_image_paths,
               GROUP_CONCAT(DISTINCT tf.filename ORDER BY pf.created_dt ASC) as product_image_filenames,
               GROUP_CONCAT(DISTINCT pc.product_cat_code) as product_cat_codes
        FROM t_product p
        LEFT JOIN t_user u ON p.user_id = u.id
        LEFT JOIN t_file f ON u.avatar_file_id = f.id
        LEFT JOIN t_application_type at ON p.application_type_id = at.id
        LEFT JOIN t_feedback fb ON p.id = fb.product_id
        LEFT JOIN t_product_img pf ON p.id = pf.product_id
        LEFT JOIN t_file tf ON pf.file_id = tf.id
        LEFT JOIN t_prod_prod_cat ppc ON p.id = ppc.prod_id
        LEFT JOIN t_product_cat pc ON ppc.prod_cat_id = pc.id
        GROUP BY p.id`.execute(sourceDb);

    if (currentProducts.rows.length === 0) {
      console.warn('⚠️ No products to process, exiting...');
      return;
    }

    console.log(`📋 Processing ${currentProducts.rows.length} products...`);

    // Migration summary tracking
    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    // Process products with rate limiting
    for (const product of currentProducts.rows) {
      await rateLimiter.schedule(async () => {
        if (!product) {
          console.log('Product is undefined');
          skipCount++;
          return;
        }

        // Basic validation
        if (!product.id || !product.creator_email) {
          console.warn(`⚠️ Skipping product with missing required data: id=${product.id}, creatorEmail=${product.creator_email}`);
          skipCount++;
          return;
        }

        if (!product.product_name_1 && !product.product_name_2 && !product.product_name_3) {
          console.warn(`⚠️ Skipping product with no name in any language: ${product.id}`);
          skipCount++;
          return;
        }

        // check if old_id already exist skip
        const productExists = await db
          .selectFrom('products')
          .where('old_id', '=', product.id)
          .select('id')
          .executeTakeFirst();
        console.log('productExists', productExists);
        if (productExists) {
          console.log('Product already exists', productExists.id);
          skipCount++;
          return;
        }

        console.log(`🔄 Processing Product: ${product.id} - ${product.product_name_1 || product.product_name_2 || product.product_name_3}`);

        try {

          // merge product image paths and filenames
          const images = [];
          if (product.product_image_paths) {
            const paths = product.product_image_paths.split(',');
            const filenames = product.product_image_filenames ? product.product_image_filenames.split(',') : [];

            for (let i = 0; i < paths.length; i++) {
              images.push({
                filename_download: filenames[i] || `image_${i}.jpg`, // Fallback filename if missing
                url: paths[i],
              });
            }
          }

          let uploadedImageIds: string[] = [];
          for (const image of images) {
            if (!image.url) {
              console.log('Image is undefined');
              continue;
            }
            try {
              // Check if file already exists in Directus by filename (if filename exists)
              let existingFile = null;
              if (image.filename_download) {
                existingFile = await db
                  .selectFrom('directus_files')
                  .select('id')
                  .where('filename_download', '=', image.filename_download)
                  .where('folder', '=', config_vars.productImagesFolderId)
                  .executeTakeFirst();
              }

              if (existingFile) {
                console.log(`📁 File already exists in Directus: ${image.filename_download}, using existing file ${existingFile.id}`);
                uploadedImageIds.push(existingFile.id);
              } else {
                console.log('inserting image', image.url);
                // Import file to Directus using image URL
                const fileRes = await targetDirectus.request(
                  importFile(image.url, { folder: config_vars.productImagesFolderId }),
                );
                console.log(`image uploaded ${fileRes.id}`);
                uploadedImageIds.push(fileRes.id);
              }
            } catch (e) {
              console.error('Failed to upload image:', e);
            }
          }

    // Get the Product Category ID
    const productCatIdResult = await db
      .selectFrom('product_categories')
      .where('code', '=', product.product_cat_codes)
      .select('id')
      .executeTakeFirst();
    const productCatId = productCatIdResult?.id;
    console.log('productCatId got', productCatId);

    // Get product_application_category by code
    const productAppCatIdResult = await db
      .selectFrom('product_application_category')
      .where('code', '=', product.app_type_code)
      .select('id')
      .executeTakeFirst();
    const productAppCatId = productAppCatIdResult?.id;
    console.log('productAppCatId got', productAppCatId);

    // Get new IP id by old_id
    const ipIdResult = await db.selectFrom('ip').where('old_id', '=', product.ip_id).select('id').executeTakeFirst();
    const newIpId = ipIdResult?.id;
    console.log('newIpId got', newIpId);

    // A field with precision 10, scale 2 must round to an absolute value less than 10^8."
    const roundDownToTwoDecimalPlaces = (jsDecimal: string): string => {
      // Parse the string to a number, defaulting to 0 if invalid
      const num = parseFloat(jsDecimal || '0');

      // Ensure the number is within the acceptable range (less than 10^8)
      const MAX_VALUE = 99999999.99;
      const clampedNum = Math.min(Math.abs(num), MAX_VALUE);

      // Return as string with exactly 2 decimal places
      return clampedNum.toFixed(2);
    };

    // For whole number currencies (JPY, KRW) - no decimals
    const roundToWholeNumber = (jsDecimal: string): string => {
      // Parse the string to a number, defaulting to 0 if invalid
      const num = parseFloat(jsDecimal || '0');

      // Ensure the number is within the acceptable range (less than 10^8)
      const MAX_VALUE = 99999999;
      const clampedNum = Math.min(Math.abs(num), MAX_VALUE);

      // Return as whole number string
      return Math.round(clampedNum).toString();
    };

          // Create Product
          // 1. Get the Product Creator ID by looking up the user by email first
          const directusUserResult = await db
            .selectFrom('directus_users')
            .where('email', '=', product.creator_email)
            .select('id')
            .executeTakeFirst();

          if (!directusUserResult) {
            console.error(`❌ Directus user not found for email: ${product.creator_email}, skipping Product: ${product.id}`);
            skipCount++;
            return;
          }

          const productOwnerIdResult = await db
            .selectFrom('creator')
            .where('user', '=', directusUserResult.id)
            .select('id')
            .executeTakeFirst();
          const productOwnerId = productOwnerIdResult?.id;
          console.log('productOwnerId got', productOwnerId);

          if (!productOwnerId) {
            console.error(`❌ Product Creator not found for user: ${directusUserResult.id} (email: ${product.creator_email}), skipping Product: ${product.id}`);
            skipCount++;
            return;
          }

          // 2. Create keywords from all translated names and descriptions
          const keywords: string[] = [];

          // Add all names (filter out empty ones)
          [product.product_name_1, product.product_name_2, product.product_name_3, product.product_name_4, product.product_name_5]
            .filter(name => name && name.trim())
            .forEach(name => keywords.push(name.trim()));

          // Add all descriptions (filter out empty ones)
          [product.product_desc_1, product.product_desc_2, product.product_desc_3, product.product_desc_4, product.product_desc_5]
            .filter(desc => desc && desc.trim())
            .forEach(desc => keywords.push(desc.trim()));

          // Join all keywords with commas
          const keywordsString = keywords.join(', ');

          // 3. Map product status from old codes to new status
          const statusMapping: Record<string, string> = {
            '00': 'draft',
            '01': 'pending',
            '02': 'rejected',
            '03': 'unpublished',
            '04': 'published'
          };
          const mappedStatus = statusMapping[product.product_status] || 'draft';
          console.log(`📋 Mapping status: ${product.product_status} -> ${mappedStatus}`);

          // 4. Create the Product with additional fields
          const mainImageId = uploadedImageIds.length > 0 ? uploadedImageIds[0] : null;
          if (!mainImageId) {
            console.warn(`⚠️ No images uploaded for Product: ${product.id}, proceeding without main image`);
          }

          const productInsertRes = await db
            .insertInto('products')
            .values({
              status: mappedStatus,
              category: productCatId ?? config_vars.defaultCategoryId,
              product_application_category: productAppCatId,
              main_image: mainImageId,
              user_created: directusUserResult.id,
              creator: productOwnerId,
              date_created: product.created_dt,
              base_currency: product.base_currency,
              price: roundDownToTwoDecimalPlaces(product.product_price ?? '0'),
              price_jpy: roundToWholeNumber(product.product_price_jpy ?? '0'),
              price_krw: roundToWholeNumber(product.product_price_krw ?? '0'),
              discount: roundDownToTwoDecimalPlaces(product.product_discount ?? '0'),
              // Stock and inventory fields
              stock_total: product.total_stock || 0,
              stock_remaining: product.rem_stock || 0,
              units_sold: product.unit_sold || 0,
              // Revenue field
              revenue_total: parseFloat(product.total_rev_gen || '0'),
              // Additional fields
              is_new: product.is_new === 'Y',
              approval_comment: product.feedback_desc || null,
              ip: newIpId,
              old_id: product.id,
              keywords: keywordsString,
            })
            .returning('id')
            .executeTakeFirst();
          console.log(productInsertRes);
          const productId = productInsertRes?.id;
          console.log('productId created', productId);

          if (!productId) {
            console.error(`❌ Failed to create Product: ${product.id}, skipping...`);
            errorCount++;
            errors.push(`Failed to create Product: ${product.id}`);
            return;
          }

          // At this point productId is guaranteed to be a number
          const confirmedProductId: number = productId;

          // update product files with duplicate protection
          if (uploadedImageIds.length > 0) {
            // Check existing files for this product
            const existingFiles = await db
              .selectFrom('products_files')
              .select('directus_files_id')
              .where('products_id', '=', confirmedProductId)
              .execute();

            const existingFileIds = new Set(existingFiles.map(f => f.directus_files_id));

            const payloadProductFiles: { products_id: number; directus_files_id: string }[] = [];
            uploadedImageIds.forEach((fileId) => {
              if (!existingFileIds.has(fileId)) {
                payloadProductFiles.push({
                  products_id: confirmedProductId,
                  directus_files_id: fileId,
                });
              }
            });

            console.log('payloadProductFiles', payloadProductFiles);

            if (payloadProductFiles.length > 0) {
              try {
                await db.insertInto('products_files').values(payloadProductFiles).execute();
                console.log(`✅ Inserted ${payloadProductFiles.length} file records for Product: ${product.id}`);
              } catch (e) {
                console.error('Failed to insert product files', e);
              }
            } else {
              console.log(`ℹ️ All files already exist for Product: ${product.id}`);
            }
          }

      // update ip translations
      // Check if we need to generate translations using Gemini API
      // Language ID mappings: 1 = English, 2 = Japanese, 3 = Korean
      const hasEnglishContent = product.product_name_1 && product.product_desc_1;
      const hasJapaneseContent = product.product_name_2 && product.product_desc_2;
      const hasKoreanContent = product.product_name_3 && product.product_desc_3;

      console.log('Content availability:', { hasEnglishContent, hasJapaneseContent, hasKoreanContent });

      // Generate missing translations if needed
      if (hasEnglishContent && (!hasJapaneseContent || !hasKoreanContent)) {
        console.log('Generating missing translations from English using Gemini API...');
        try {
          if (!hasJapaneseContent) {
            console.log('Translating English to Japanese...');
            const japaneseTranslation = await translateWithGemini(
              product.product_name_1,
              product.product_desc_1,
              'English',
              'Japanese',
            );
            product.product_name_2 = japaneseTranslation.name;
            product.product_desc_2 = japaneseTranslation.description;
            console.log('Generated Japanese translation');
          }

          if (!hasKoreanContent) {
            console.log('Translating English to Korean...');
            const koreanTranslation = await translateWithGemini(
              product.product_name_1,
              product.product_desc_1,
              'English',
              'Korean',
            );
            product.product_name_3 = koreanTranslation.name;
            product.product_desc_3 = koreanTranslation.description;
            console.log('Generated Korean translation');
          }
        } catch (error) {
          console.error('Error generating translations:', error);
        }
      } else if (hasKoreanContent && (!hasEnglishContent || !hasJapaneseContent)) {
        console.log('Generating missing translations from Korean using Gemini API...');
        try {
          if (!hasEnglishContent) {
            console.log('Translating Korean to English...');
            const englishTranslation = await translateWithGemini(
              product.product_name_3,
              product.product_desc_3,
              'Korean',
              'English',
            );
            product.product_name_1 = englishTranslation.name;
            product.product_desc_1 = englishTranslation.description;
            console.log('Generated English translation');
          }

          if (!hasJapaneseContent) {
            console.log('Translating Korean to Japanese...');
            const japaneseTranslation = await translateWithGemini(
              product.product_name_3,
              product.product_desc_3,
              'Korean',
              'Japanese',
            );
            product.product_name_2 = japaneseTranslation.name;
            product.product_desc_2 = japaneseTranslation.description;
            console.log('Generated Japanese translation');
          }
        } catch (error) {
          console.error('Error generating translations:', error);
        }
      } else if (hasJapaneseContent && (!hasEnglishContent || !hasKoreanContent)) {
        console.log('Generating missing translations from Japanese using Gemini API...');
        try {
          if (!hasEnglishContent) {
            console.log('Translating Japanese to English...');
            const englishTranslation = await translateWithGemini(
              product.product_name_2,
              product.product_desc_2,
              'Japanese',
              'English',
            );
            product.product_name_1 = englishTranslation.name;
            product.product_desc_1 = englishTranslation.description;
            console.log('Generated English translation');
          }

          if (!hasKoreanContent) {
            console.log('Translating Japanese to Korean...');
            const koreanTranslation = await translateWithGemini(
              product.product_name_2,
              product.product_desc_2,
              'Japanese',
              'Korean',
            );
            product.product_name_3 = koreanTranslation.name;
            product.product_desc_3 = koreanTranslation.description;
            console.log('Generated Korean translation');
          }
        } catch (error) {
          console.error('Error generating translations:', error);
        }
      }

          // Language ID mappings for database: 1 = English, 2 = Japanese, 3 = Korean
          // Check existing translations to prevent duplicates
          const existingTranslations = await db
            .selectFrom('products_translations')
            .select(['languages_id'])
            .where('products_id', '=', confirmedProductId)
            .execute();

          const existingLanguageIds = new Set(existingTranslations.map(t => t.languages_id));

          const payloadProductTranslations: { products_id: number; languages_id: number; name: string; description: string }[] = [];

          // Only add translations that don't exist and have content
          if (!existingLanguageIds.has(1) && product.product_name_1 && product.product_desc_1) {
            payloadProductTranslations.push({
              products_id: confirmedProductId,
              languages_id: 1, // English
              name: product.product_name_1,
              description: product.product_desc_1,
            });
          }

          if (!existingLanguageIds.has(2) && product.product_name_2 && product.product_desc_2) {
            payloadProductTranslations.push({
              products_id: confirmedProductId,
              languages_id: 2, // Japanese
              name: product.product_name_2,
              description: product.product_desc_2,
            });
          }

          if (!existingLanguageIds.has(3) && product.product_name_3 && product.product_desc_3) {
            payloadProductTranslations.push({
              products_id: confirmedProductId,
              languages_id: 3, // Korean
              name: product.product_name_3,
              description: product.product_desc_3,
            });
          }

          console.log('payloadProductTranslations', payloadProductTranslations);

          if (payloadProductTranslations.length > 0) {
            try {
              const translationIds: any = await db
                .insertInto('products_translations')
                .values(payloadProductTranslations)
                .returning('id')
                .execute();

              console.log(`✅ Inserted ${translationIds.length} translations for Product: ${product.id}`);

              // Trigger a translation update via directus sdk as well
              if (translationIds.length > 0) {
                await targetDirectus.request(
                  updateItem('products_translations', translationIds[0].id, {
                    refresh: true,
                  }),
                );
              }
            } catch (e) {
              console.error('Failed to insert product translations', e);
            }
          } else {
            console.log(`ℹ️ All translations already exist for Product: ${product.id}`);
          }

          // Product successfully processed
          successCount++;
          console.log(`✅ Successfully processed Product: ${product.id}`);

        } catch (error) {
          errorCount++;
          const errorMsg = `Error processing Product ${product.id}: ${error}`;
          console.error(`❌ ${errorMsg}`);
          errors.push(errorMsg);
          // Continue with next product instead of exiting
        }
        }); // Close rate limiter schedule
    }

    // Print migration summary
    console.log('\n📊 Migration Summary:');
    console.log(`✅ Successfully processed: ${successCount} Products`);
    console.log(`⚠️ Skipped: ${skipCount} Products`);
    console.log(`❌ Errors: ${errorCount} Products`);

    if (errors.length > 0) {
      console.log('\n❌ Error details:');
      errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    console.log('\n🎉 Product migration completed');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }

}

/**
 * Translates text using the Gemini API
 * @param name The name to translate
 * @param description The description to translate
 * @param sourceLanguage The source language
 * @param targetLanguage The target language
 * @returns The translated name and description
 */
/**
 * Verifies if text is likely in the expected language
 * This is a simple check and not 100% accurate
 */
function verifyLanguage(text: string, targetLanguage: string): boolean {
  // Simple language verification based on character sets and common words
  if (targetLanguage === 'Korean') {
    // Check for Korean characters (Hangul)
    return /[\uAC00-\uD7AF\u1100-\u11FF\u3130-\u318F\uA960-\uA97F\uD7B0-\uD7FF]/.test(text);
  } else if (targetLanguage === 'Japanese') {
    // Check for Japanese characters (Hiragana, Katakana, and some Kanji)
    return /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF]/.test(text);
  } else if (targetLanguage === 'English') {
    // Check for primarily Latin characters and common English words
    return (
      /^[\p{Script=Latin}\d\s\p{P}]*$/u.test(text) &&
      !/[\uAC00-\uD7AF\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF]/.test(text)
    );
  }
  return true; // Default to true for unsupported language checks
}

// Rate limiting for Gemini API (15 calls per minute)
let lastApiCall = 0;
const API_RATE_LIMIT_MS = 0; // 4 seconds between calls

async function translateWithGemini(
  name: string,
  description: string,
  sourceLanguage: string,
  targetLanguage: string,
): Promise<{ name: string; description: string }> {
  // Rate limiting - wait if needed
  const now = Date.now();
  const timeSinceLastCall = now - lastApiCall;
  if (timeSinceLastCall < API_RATE_LIMIT_MS) {
    const waitTime = API_RATE_LIMIT_MS - timeSinceLastCall;
    console.log(`⏳ Rate limiting: waiting ${Math.round(waitTime / 1000)}s before next API call...`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }
  lastApiCall = Date.now();

  // Use validated API key from configuration
  const apiKey = config_vars.geminiApiKey;
  const apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

  const prompt = `You are a professional translator. Translate the following text from ${sourceLanguage} to ${targetLanguage}. Be accurate and maintain the original meaning and style.

Name: ${name}
Description: ${description}

You MUST respond ONLY in ${targetLanguage}. Your response MUST follow this EXACT format:
Name: [translated name in ${targetLanguage}]
Description: [translated description in ${targetLanguage}]`;

  try {
    const response = await fetch(`${apiUrl}?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt,
              },
            ],
          },
        ],
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('Gemini API error:', data);
      throw new Error(`Gemini API error: ${data.error?.message || 'Unknown error'}`);
    }

    const generatedText = data.candidates[0].content.parts[0].text;

    console.log('Translation response:', generatedText);

    // Extract the translated name and description from the response
    const nameMatch = generatedText.match(/Name:\s*(.+?)(?:\n|$)/);
    const descriptionMatch = generatedText.match(/Description:\s*([\s\S]+)$/);

    // Basic language verification
    const extractedName = nameMatch ? nameMatch[1].trim() : '';
    const extractedDesc = descriptionMatch ? descriptionMatch[1].trim() : '';

    // Verify language is correct based on target language
    const isCorrectLanguage = verifyLanguage(extractedName + ' ' + extractedDesc, targetLanguage);

    if (!isCorrectLanguage) {
      console.warn(`Warning: Translation may not be in ${targetLanguage}. Attempting one more time...`);
      // You could implement retry logic here if needed
    }

    return {
      name: extractedName,
      description: extractedDesc,
    };
  } catch (error) {
    console.error('Error calling Gemini API:', error);
    throw error;
  }
}

await main();
