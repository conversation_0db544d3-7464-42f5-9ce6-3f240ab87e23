[{"productId": "0043aef9-a42c-40b6-a17d-5186df60a6d0", "productName1": "", "productName2": "", "productName3": "혼부「유명의 고륜」", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "동방프로젝트에 나오는 [콘파쿠 요유무]캐릭의 필살기(분신술 스펠카드) 魂符「幽明の苦輪」를 형상화한 디자인 입니다.\n일정상 전시만 신청합니다.\n스케일은 약 1/7정도로 생각하고 있습니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "USD", "productPrice": {"usd": 1.0, "jpy": 147.71, "krw": 1418.5}, "totalStock": 1, "unitSold": 0, "remStock": 1, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "03", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-12T09:56:39.379Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "a2c637bd-1a09-4078-9840-51bd74c32a5e", "nickname": "2500398455", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/Logo_back.png"}, "ipId": "e26c92f8-204c-4422-ba80-9a11a28e574d", "ipName1": "", "ipName2": "", "ipName3": "동방프로젝트 / 東方Project", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/01.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/02.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/03.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/04.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/05.jpg"], "fileNames": ["01.jpg", "02.jpg", "03.jpg", "04.jpg", "05.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "01495376-66b4-4a1b-a15d-7aeacf411e07", "productName1": "", "productName2": "", "productName3": "라이트 헬로", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "데포르메 스타일의 라이트 헬로입니다.\n조립 및 도색이 필요한 레진 킷 형태로 제작될 예정입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "USD", "productPrice": {"usd": 90000.0, "jpy": 12937005.0, "krw": 123651599.13}, "totalStock": 5, "unitSold": 0, "remStock": 5, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-27T09:24:37.543Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "4193d6ef-5c82-4934-a150-9da29a3ab499", "nickname": "2951729673", "avatarUrl": null}, "ipId": "8aedb9bb-5c66-4bc7-9623-2483659c8769", "ipName1": "", "ipName2": "", "ipName3": "Umamusume: Pretty Derby / ウマ娘 プリティーダービー", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%82%AC%EB%B3%B8%20-20250523_224953.jpg"], "fileNames": ["사본 -20250523_224953.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "083c6257-b1be-4cb9-a0a5-924928e0f01d", "productName1": "", "productName2": "", "productName3": "림버스 컴퍼니 로슈 4번째 성냥불 EGO", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "게임 림버스 컴퍼니에 수감자 로슈의 EGO인 4번째 성냥불 입니다.\n1/6 피규어, 배경까지 합쳐서 30cm로 제작 예정 입니다.\n일러스트를 그대로 표현하도록 제작 하였습니다.\n\n전시 목적으로 제작 하였으며 채색 되지 않고 전시, 채색은 눈, 한자 특정 부위에만 진행할 예정입니다. 전체 채색은 sns 계정으로 볼수 있도록 업로드 할예정입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 290.38, "jpy": 41740.93, "krw": 400000.0}, "totalStock": 1, "unitSold": 0, "remStock": 1, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-21T14:54:04.936Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "bff71c4c-aa4b-4abe-86d2-a54e2455d3e5", "nickname": "5699202771", "avatarUrl": null}, "ipId": "ed4b8b9e-9198-4d45-a3c6-d45bf281c0bb", "ipName1": "", "ipName2": "", "ipName3": "『Limbus Company』료슈 E.G.O 「4번째 성냥불」[『Limbus Company』 Ryōshū E.G.O 「4th Match Flame」]", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%B0%B8%EA%B3%A0%EC%9D%B4%EB%AF%B8%EC%A7%80.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%A0%84%EC%8B%9C.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%B1%84%EC%83%89.png"], "fileNames": ["참고이미지.jpg", "전시.png", "채색.png"], "owner": {"id": "8c37e142-c761-41ec-bb01-e1b0d5d3565e", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "(주)프로젝트문"}}, {"productId": "0b4610f2-9421-4972-8b27-0f0b4d2ceb27", "productName1": "", "productName2": "완다 & 리셋 1/6 피규어 세트", "productName3": "", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "각 캐릭터의 상징적인 하이테크 수트 디테일과 시그니처 포즈(완다의 레이저 블래스터 자세 / 리셋의 퀀텀점프 준비 동작)를 중심으로 조형하였으며,\n발랄한 에너지가 느껴지는 완다와, 차분한 카리스마를 가진 리셋의 대비적인 매력을 동시에 표현하는 데에 중점을 두었습니다.\n네온 계열의 공식 컬러와 반투명 파츠(버블실드, 네코부스트 효과)를 활용해 입체적이고 생동감 있는 디오라마 형태로 제작 예정입니다.", "productDesc3": "", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 66.22, "jpy": 9455.77, "krw": 95000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-04-24T06:30:17.951Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "49f8ab43-ba78-4ac1-abac-ec5aafd2f1bc", "nickname": "원더페스티벌 한국 실행위원회 ", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/%E1%84%89%E1%85%B3%E1%84%8F%E1%85%B3%E1%84%85%E1%85%B5%E1%86%AB%E1%84%89%E1%85%A3%E1%86%BA%202025-03-27%20%E1%84%8B%E1%85%A9%E1%84%92%E1%85%AE%2012.51.36.png"}, "ipId": "4215a41a-2cef-455d-bfcf-1103fcec656a", "ipName1": "", "ipName2": "", "ipName3": "Wonder Festival 2025 Korea 완다 & 리셋", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20200118_003-DSC00693.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20200118_004-DSC00701.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20200118_005-DSC00707.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20200118_001-191220-047.jpg"], "fileNames": ["20200118_003-DSC00693.jpg", "20200118_004-DSC00701.jpg", "20200118_005-DSC00707.jpg", "20200118_001-191220-047.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "0e9c59be-e7bb-431c-8233-fdb81d5f0280", "productName1": "", "productName2": "", "productName3": "니케 헬름", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "헬름 미도색레진킷", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 116.87, "jpy": 16675.36, "krw": 160000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-27T02:52:37.769Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "91f1e4cc-34d1-4565-84a6-ea01e84379dc", "nickname": "7402346441", "avatarUrl": null}, "ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%5B%ED%81%AC%EA%B8%B0%EB%B3%80%ED%99%98%5D1111KakaoTalk_20250326_100513843_03.png"], "fileNames": ["[크기변환]1111KakaoTalk_20250326_100513843_03.png"], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"productId": "15580998-b803-4c2a-aa20-c0fc7ab99f4b", "productName1": "", "productName2": "", "productName3": "다이와 스칼렛", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "- 우마무스메/다이와 스칼렛\n- 논스케일 sd피규어\n- 4.5cm 이하\n- 다이와 스칼렛의 귀엽고 아름다움을 작게 데포르메해서 표현했 \n  습니다.\n  이쁘게 달리는 모습이나 1착승리 포즈로 만들예정입니다.\n", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 33.03, "jpy": 4701.28, "krw": 45000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-26T03:16:42.239Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "99fe152c-4d3d-4fdf-8e39-6ad21dd10b37", "nickname": "1807316093", "avatarUrl": null}, "ipId": "8aedb9bb-5c66-4bc7-9623-2483659c8769", "ipName1": "", "ipName2": "", "ipName3": "Umamusume: Pretty Derby / ウマ娘 プリティーダービー", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/daiwa.jpg"], "fileNames": ["daiwa.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "1e9da470-a776-49dc-a509-d76d902b611c", "productName1": "", "productName2": "", "productName3": "점토 피규어 - 소닉 더 헤지혹", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "오로지 전시 목적으로만 제작된, 점토 수제 피규어입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "USD", "productPrice": {"usd": 1.0, "jpy": 142.78, "krw": 1365.73}, "totalStock": 1, "unitSold": 0, "remStock": 1, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-26T00:41:47.686Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "9b831113-44fc-4796-a06f-eec6583abca6", "nickname": "8238076822", "avatarUrl": null}, "ipId": "2d229ede-a15a-491c-b36d-0794a8aaa884", "ipName1": "", "ipName2": "", "ipName3": "소닉 더 헤지혹 (<PERSON> the Hedgehog)", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "ART", "productCatName1": "Art & Crafts", "productCatName2": "アート・クラフト", "productCatName3": "아트/공예", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20250428_230612.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20250428_232656.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20250428_232800%20%281%29.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20250428_232751.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20250428_232738%20%281%29.jpg"], "fileNames": ["20250428_230612.jpg", "20250428_232656.jpg", "20250428_232800 (1).jpg", "20250428_232751.jpg", "20250428_232738 (1).jpg"], "owner": {"id": "0e6f62ad-5bbb-4e2e-9970-970216285f33", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "SEGA"}}, {"productId": "1faba6cc-811a-4a41-b163-a6b1c767c0d9", "productName1": "", "productName2": "", "productName3": "테스트", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "테스트", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 3.91, "jpy": 558.31, "krw": 5555.0}, "totalStock": 5, "unitSold": 0, "remStock": 5, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-04-30T11:05:40.802Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "49f8ab43-ba78-4ac1-abac-ec5aafd2f1bc", "nickname": "원더페스티벌 한국 실행위원회 ", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/%E1%84%89%E1%85%B3%E1%84%8F%E1%85%B3%E1%84%85%E1%85%B5%E1%86%AB%E1%84%89%E1%85%A3%E1%86%BA%202025-03-27%20%E1%84%8B%E1%85%A9%E1%84%92%E1%85%AE%2012.51.36.png"}, "ipId": "4215a41a-2cef-455d-bfcf-1103fcec656a", "ipName1": "", "ipName2": "", "ipName3": "Wonder Festival 2025 Korea 완다 & 리셋", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "APP", "productCatName1": "Apparel & Accessories", "productCatName2": "アパレル・アクセサリー", "productCatName3": "의류/액세서리", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/KakaoTalk_Photo_2025-04-24-15-18-33%20001.jpeg"], "fileNames": ["KakaoTalk_Photo_2025-04-24-15-18-33 001.jpeg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "2f5c4611-0f39-4ba2-8a24-29933fecf5f5", "productName1": "", "productName2": "", "productName3": "페이트 그랜드 오더 어벤져 스페이스 이슈타르", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "모바일 게임 페이트 그랜드 오더의 어벤져 클래스의 스페이스 이슈타르 입니다.\n공식 일러스트를 배경으로 작업 하였고 포즈만 변경 하였습니다.\n전시목적이며 전시시 채색은 눈 정도만 되어 있는 상태로 전시 예정 입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 280.15, "jpy": 40083.24, "krw": 400000.0}, "totalStock": 1, "unitSold": 0, "remStock": 1, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "02", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-01T04:52:24.686Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "bff71c4c-aa4b-4abe-86d2-a54e2455d3e5", "nickname": "5699202771", "avatarUrl": null}, "ipId": "4215a41a-2cef-455d-bfcf-1103fcec656a", "ipName1": "", "ipName2": "", "ipName3": "Wonder Festival 2025 Korea 완다 & 리셋", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%A0%84%EC%8B%9C.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%A0%84%EC%8B%9C_2.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%A0%84%EC%8B%9C_3.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%B1%84%EC%83%89.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/NewScene%281%29.png"], "fileNames": ["전시.png", "전시_2.png", "전시_3.png", "채색.png", "NewScene(1).png"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "35c1d79a-5b53-4c19-870b-34eb3440915c", "productName1": "Wanda & Reset 1/6 Figure Set", "productName2": "", "productName3": "", "productName4": "", "productName5": "", "productDesc1": "Each character is sculpted with a focus on their iconic high-tech suit details and signature poses — <PERSON> with her laser blaster stance, and <PERSON><PERSON> in her quantum jump preparation pose.\nThe design emphasizes the contrast between the characters: <PERSON>’s playful energy and <PERSON><PERSON>’s calm charisma, showcasing their distinct charm side by side.\nThe figures will be crafted in a dynamic diorama format, using neon-themed official colors and semi-transparent parts (such as the bubble shield and <PERSON><PERSON><PERSON> effects) to create a three-dimensional and vibrant presentation.", "productDesc2": "", "productDesc3": "", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 59.25, "jpy": 8460.43, "krw": 85000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-04-24T06:51:29.265Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "49f8ab43-ba78-4ac1-abac-ec5aafd2f1bc", "nickname": "원더페스티벌 한국 실행위원회 ", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/%E1%84%89%E1%85%B3%E1%84%8F%E1%85%B3%E1%84%85%E1%85%B5%E1%86%AB%E1%84%89%E1%85%A3%E1%86%BA%202025-03-27%20%E1%84%8B%E1%85%A9%E1%84%92%E1%85%AE%2012.51.36.png"}, "ipId": "4215a41a-2cef-455d-bfcf-1103fcec656a", "ipName1": "", "ipName2": "", "ipName3": "Wonder Festival 2025 Korea 완다 & 리셋", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%8B%E1%85%AA%E1%86%AB%E1%84%83%E1%85%A1%2C%E1%84%85%E1%85%B5%E1%84%89%E1%85%A6%E1%86%BA.png"], "fileNames": ["완다,리셋.png"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "3bed26bc-6146-4626-acfa-c392023e7453", "productName1": "", "productName2": "ワンダ＆リセット 1/6スケールフィギュアセット", "productName3": "", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "各キャラクターの象徴的なハイテクスーツのディテールとシグネチャーポーズ（ワンダのレーザーブラスター構え／リセットのクアンタムジャンプ準備動作）を中心に造形しました。\n\n元気いっぱいのワンダと、落ち着いたカリスマ性を持つリセットという対照的な魅力を同時に表現することに重点を置いています。\nネオン系の公式カラーと半透明パーツ（バブルシールド、ネコブーストエフェクト）を活用し、立体的で臨場感のあるジオラマ形式での制作を予定しています。", "productDesc3": "", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 59.25, "jpy": 8460.43, "krw": 85000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-04-24T06:53:29.579Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "49f8ab43-ba78-4ac1-abac-ec5aafd2f1bc", "nickname": "원더페스티벌 한국 실행위원회 ", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/%E1%84%89%E1%85%B3%E1%84%8F%E1%85%B3%E1%84%85%E1%85%B5%E1%86%AB%E1%84%89%E1%85%A3%E1%86%BA%202025-03-27%20%E1%84%8B%E1%85%A9%E1%84%92%E1%85%AE%2012.51.36.png"}, "ipId": "4215a41a-2cef-455d-bfcf-1103fcec656a", "ipName1": "", "ipName2": "", "ipName3": "Wonder Festival 2025 Korea 완다 & 리셋", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%8B%E1%85%AA%E1%86%AB%E1%84%83%E1%85%A1%2C%E1%84%85%E1%85%B5%E1%84%89%E1%85%A6%E1%86%BA.png"], "fileNames": ["완다,리셋.png"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "4586f9fa-344f-4c5c-b318-ad02c51c3635", "productName1": "", "productName2": "", "productName3": "림버스 컴퍼니 로슈 4번째 성냥불 EGO", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "게임 림버스 컴퍼니에 수감자 로슈의 EGO인 4번째 성냥불 입니다.\n1/6 피규어, 배경까지 합쳐서 30cm로 제작 예정 입니다.\n일러스트를 그대로 표현하도록 제작 하였습니다.\n\n전시 목적으로 제작 하였으며 채색 되지 않고 전시, 채색은 눈, 한자 특정 부위에만 진행할 예정입니다. 전체 채색은 sns 계정으로 볼수 있도록 업로드 할예정입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 280.75, "jpy": 40153.35, "krw": 400000.0}, "totalStock": 1, "unitSold": 0, "remStock": 1, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "02", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-01T00:02:54.526Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "bff71c4c-aa4b-4abe-86d2-a54e2455d3e5", "nickname": "5699202771", "avatarUrl": null}, "ipId": "4215a41a-2cef-455d-bfcf-1103fcec656a", "ipName1": "", "ipName2": "", "ipName3": "Wonder Festival 2025 Korea 완다 & 리셋", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%A0%84%EC%8B%9C.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%B0%B8%EA%B3%A0%EC%9D%B4%EB%AF%B8%EC%A7%80.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%B1%84%EC%83%89.png"], "fileNames": ["전시.png", "참고이미지.jpg", "채색.png"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "464a4634-cdd6-4275-9a95-6ea5285d067b", "productName1": "", "productName2": "", "productName3": "니케 베히모스 3d 프린팅 피규어", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "니케 베히모스 캐릭터 메인일러스트를 24cm 크기 입체로 구현한 3d 프린팅 피규어\n\n비판매용으로. 전시 및 피규어 제작사 홍보목적입니다", "productDesc4": "", "productDesc5": "", "baseCurrency": "USD", "productPrice": {"usd": 1.0, "jpy": 142.78, "krw": 1365.73}, "totalStock": 1, "unitSold": 0, "remStock": 1, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-26T02:49:54.417Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "25336621-ad9e-4d51-8ae2-4ff6796360ec", "nickname": "8858585875", "avatarUrl": null}, "ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/KakaoTalk_20250526_110048204_01.png"], "fileNames": ["KakaoTalk_20250526_110048204_01.png"], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"productId": "499e1f86-af54-4b85-b723-99186a02f04a", "productName1": "", "productName2": "", "productName3": "브라운더스트2 - 빌헬미나", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "이 제품은 전시 목적의 도색 완제품 하나와 판매 목적으로 나가는 미도색 복제개러지킷으로 제작 할 예정입니다.\n\n원형 제작 대기중이며, 원형의 레퍼런스는 이미지 03의 모습을 참고 할 예정입니다.\n\n도색 완제품 : 1개 / 개러지킷 : 최대 40개", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 129.91, "jpy": 18688.82, "krw": 180000.0}, "totalStock": 40, "unitSold": 0, "remStock": 40, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-21T04:17:09.89Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "fbfc33d2-64df-45f4-a3fc-b522d33332fe", "nickname": "8381993260", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/FoxJump_LOGO1.png"}, "ipId": "e2344f89-1812-4c33-b46b-5e5a2a0c98d2", "ipName1": "", "ipName2": "", "ipName3": "브라운더스트2 (BROWN DUST2)", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "ART", "productCatName1": "Art & Crafts", "productCatName2": "アート・クラフト", "productCatName3": "아트/공예", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/01.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/02.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/03.png"], "fileNames": ["01.png", "02.png", "03.png"], "owner": {"id": "f82921f7-ee64-4418-9f7a-899435a89da3", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 네오위즈"}}, {"productId": "4d698275-0efc-4b6d-98fb-29a1230b77b7", "productName1": "", "productName2": "", "productName3": "플로렌스 -토끼 발자국 버전-", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "플로렌스의 스킨 [묘수회천] 일러스트를 기반으로 피규어를 제작할 예정입니다. 일러스트의 주변배경은 제작하지 않고 케릭터만 제작할 예정입니다.\n생산없이 전시만 할 예정이며 스케일은 약 1/7 입니다.\n", "productDesc4": "", "productDesc5": "", "baseCurrency": "USD", "productPrice": {"usd": 1.0, "jpy": 147.71, "krw": 1418.5}, "totalStock": 1, "unitSold": 0, "remStock": 1, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "03", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-12T10:06:33.586Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "a2c637bd-1a09-4078-9840-51bd74c32a5e", "nickname": "2500398455", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/Logo_back.png"}, "ipId": "5cb74c63-fd45-4480-b385-6f4f1444bd0d", "ipName1": "GIRL`S FRONTLINE: NEURAL CLOUD", "ipName2": "", "ipName3": "", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/3.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/2.jpg"], "fileNames": ["3.jpg", "2.jpg"], "owner": {"id": "83c95581-13f0-4f9a-9364-e7fceb5b19f8", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "Shanghai Yunjie Trading Co., Ltd."}}, {"productId": "4d8c432d-2de7-4874-86fd-78eb4d321de2", "productName1": "베히모스", "productName2": "", "productName3": "", "productName4": "", "productName5": "", "productDesc1": "15cm sd 개러지킷 및 전시", "productDesc2": "", "productDesc3": "", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 65.47, "jpy": 9437.42, "krw": 90000.0}, "totalStock": 3, "unitSold": 0, "remStock": 3, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-27T14:40:57.796Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "25336621-ad9e-4d51-8ae2-4ff6796360ec", "nickname": "8858585875", "avatarUrl": null}, "ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/ZBrush%20Document.png"], "fileNames": ["ZBrush Document.png"], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"productId": "4f77d397-8a05-4ac3-9309-2e25a421f189", "productName1": "", "productName2": "", "productName3": "비앙키 슈퍼 피스타 프레임셋 1/6 스케일 모형", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "\"Bianchi(비앙키)\"는 동호인들로부터 '감성'으로 통하는 멋진 자전거를 제조하는 이탈리아의 회사명입니다.(국내 정식수입사는 '대진인터내셔널')\n\"Super Pistar(슈퍼 피스타)\"는 트렉 선수를 위한 훈련 연습용 픽시자전거로 나왔지만, 어린 학생들 부터 청년층까지 모두 좋아하고 타고싶어하는 자전거입니다.\n\n저는 이 자전거의 프레임셋을 기존 자전거 피규어나 장난감의 수준으로부터 디테일업한 모형으로 제작하려합니다.\n회전부는 모두 실제 미니어쳐 베어링을 적용 예정이고, 얇은 부품(체인링, 체인 등)은 에칭으로 제작 예정입니다.\n모형의 원형사인 본인은 실제 자전거 제조업체의 자전거 설계자 출신으로, 리얼함을 추구합니다.\n\n실제 브랜드 로고 및 상표를 정식으로 사용하여 만든 모형은, 한국의 유명 웹툰(윈드 브레이커)의 케릭터(셸리 스콧)와 콜라보하여, 각 브랜드별 홍보효과의 시너지를 상승시킬 예정입니다.\n\nIP판권에 해당하는 부분별로 나누어(프레임셋/휠셋/케릭터) 신청 및 판매할 예정이지만, 합본판매 조율이 된다면, 합본으로 판매하고 싶습니다.\n\nIP와 관련된 부분은 다음과 같습니다.\n1. 비앙키 슈퍼 피스타 자전거 외관 형상\n2. 이탈리아 왕가를 상징하는 비앙키 로고 문양 데칼\n3. Bianchi 브랜드명: 다운튜브 좌/우, 싯튜브 좌/우 데칼\n4. Super Pista 상표명: 탑튜브 좌/우 데칼\n5. 판매제품은 미도색 레진이지만, 전시샘플은 비앙키의 체레스터 색상 적용 예정.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 71.6, "jpy": 10465.91, "krw": 100000.0}, "totalStock": 50, "unitSold": 0, "remStock": 50, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "02", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-14T13:39:03.513Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "7c793c6e-f65a-47a8-b64a-c78268ea516f", "nickname": "5883640081", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/%EA%B9%80%ED%99%8D%EC%A4%80%EB%8B%98%20%EB%A1%9C%EA%B3%A0.png"}, "ipId": "4215a41a-2cef-455d-bfcf-1103fcec656a", "ipName1": "", "ipName2": "", "ipName3": "Wonder Festival 2025 Korea 완다 & 리셋", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1.%20Super%20Pistar%20Frameset%203D.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/2.%20Other%20parts%20assembly%20examples.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/3.%20Bearing%20and%20etching%20applications.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/4.%20Bianchi%20logo%20and%20trademark%20decals.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/5.%20Real%20bicycle%20example.jpg"], "fileNames": ["1. Super Pistar Frameset 3D.jpg", "2. Other parts assembly examples.jpg", "3. Bearing and etching applications.jpg", "4. <PERSON><PERSON><PERSON> logo and trademark decals.jpg", "5. Real bicycle example.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "501046df-bf13-4ab5-bee9-ff7f9436ed72", "productName1": "", "productName2": "", "productName3": "신비한 골드쉽", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "NON SCALE의 SD 골드쉽 피규어 레진캐스트 키트", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 36.34, "jpy": 5243.39, "krw": 50000.0}, "totalStock": 20, "unitSold": 0, "remStock": 20, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-28T08:10:31.757Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "1198d9cd-f8c7-4752-8e2f-0813508b8950", "nickname": "5886553682", "avatarUrl": null}, "ipId": "8aedb9bb-5c66-4bc7-9623-2483659c8769", "ipName1": "", "ipName2": "", "ipName3": "Umamusume: Pretty Derby / ウマ娘 プリティーダービー", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "ART", "productCatName1": "Art & Crafts", "productCatName2": "アート・クラフト", "productCatName3": "아트/공예", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/178cf1d602e1b8.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1794ac097aa1b8.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1794ac084c51b8.jpg"], "fileNames": ["178cf1d602e1b8.jpg", "1794ac097aa1b8.jpg", "1794ac084c51b8.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "50cb9c4c-db04-46a0-b6ef-21817dd687cf", "productName1": "완다 & 리셋 1/6 피규어 세트", "productName2": "", "productName3": "", "productName4": "", "productName5": "", "productDesc1": "각 캐릭터의 상징적인 하이테크 수트 디테일과 시그니처 포즈(완다의 레이저 블래스터 자세 / 리셋의 퀀텀점프 준비 동작)를 중심으로 조형하였으며,\n발랄한 에너지가 느껴지는 완다와, 차분한 카리스마를 가진 리셋의 대비적인 매력을 동시에 표현하는 데에 중점을 두었습니다.\n네온 계열의 공식 컬러와 반투명 파츠(버블실드, 네코부스트 효과)를 활용해 입체적이고 생동감 있는 디오라마 형태로 제작 예정입니다.", "productDesc2": "", "productDesc3": "", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 66.22, "jpy": 9455.77, "krw": 95000.0}, "totalStock": 5, "unitSold": 0, "remStock": 5, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-04-24T06:29:16.94Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "49f8ab43-ba78-4ac1-abac-ec5aafd2f1bc", "nickname": "원더페스티벌 한국 실행위원회 ", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/%E1%84%89%E1%85%B3%E1%84%8F%E1%85%B3%E1%84%85%E1%85%B5%E1%86%AB%E1%84%89%E1%85%A3%E1%86%BA%202025-03-27%20%E1%84%8B%E1%85%A9%E1%84%92%E1%85%AE%2012.51.36.png"}, "ipId": "4215a41a-2cef-455d-bfcf-1103fcec656a", "ipName1": "", "ipName2": "", "ipName3": "Wonder Festival 2025 Korea 완다 & 리셋", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20200118_004-DSC00701.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20200118_003-DSC00693.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20200118_005-DSC00707.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20200118_001-191220-047.jpg"], "fileNames": ["20200118_004-DSC00701.jpg", "20200118_003-DSC00693.jpg", "20200118_005-DSC00707.jpg", "20200118_001-191220-047.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "510ecfe4-7219-43c7-a6d9-31031518b81c", "productName1": "", "productName2": "", "productName3": "완다 & 리셋 1/6 피규어 세트", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "각 캐릭터의 상징적인 하이테크 수트 디테일과 시그니처 포즈(완다의 레이저 블래스터 자세 / 리셋의 퀀텀점프 준비 동작)를 중심으로 조형하였으며,\n발랄한 에너지가 느껴지는 완다와, 차분한 카리스마를 가진 리셋의 대비적인 매력을 동시에 표현하는 데에 중점을 두었습니다.\n네온 계열의 공식 컬러와 반투명 파츠(버블실드, 네코부스트 효과)를 활용해 입체적이고 생동감 있는 디오라마 형태로 제작 예정입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 662.22, "jpy": 94557.73, "krw": 950000.0}, "totalStock": 8, "unitSold": 0, "remStock": 8, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-04-24T06:27:04.513Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "49f8ab43-ba78-4ac1-abac-ec5aafd2f1bc", "nickname": "원더페스티벌 한국 실행위원회 ", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/%E1%84%89%E1%85%B3%E1%84%8F%E1%85%B3%E1%84%85%E1%85%B5%E1%86%AB%E1%84%89%E1%85%A3%E1%86%BA%202025-03-27%20%E1%84%8B%E1%85%A9%E1%84%92%E1%85%AE%2012.51.36.png"}, "ipId": "4215a41a-2cef-455d-bfcf-1103fcec656a", "ipName1": "", "ipName2": "", "ipName3": "Wonder Festival 2025 Korea 완다 & 리셋", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20200118_001-191220-047.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20200118_005-DSC00707.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20200118_004-DSC00701.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/20200118_003-DSC00693.jpg"], "fileNames": ["20200118_001-191220-047.jpg", "20200118_005-DSC00707.jpg", "20200118_004-DSC00701.jpg", "20200118_003-DSC00693.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "51702eeb-5222-47e5-81f2-537cd1cde17f", "productName1": "", "productName2": "", "productName3": "소다", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "니케 소다 캐릭터 메인일러스트를 24cm 크기 입체로 구현한 3d 프린팅 피규어\n\n비판매용으로. 전시 및 피규어 제작사 홍보목적입니다", "productDesc4": "", "productDesc5": "", "baseCurrency": "USD", "productPrice": {"usd": 1.0, "jpy": 142.78, "krw": 1365.73}, "totalStock": 1, "unitSold": 0, "remStock": 1, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-26T02:48:41.933Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "25336621-ad9e-4d51-8ae2-4ff6796360ec", "nickname": "8858585875", "avatarUrl": null}, "ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/EmELXGsqDzsIZeoM1aumugSU1Z4wje0yyF5glmDGowkbqFQwu72JSZCfWmeD-TGreoK-9bIWzX0wFgVUjA7XBQ.png"], "fileNames": ["EmELXGsqDzsIZeoM1aumugSU1Z4wje0yyF5glmDGowkbqFQwu72JSZCfWmeD-TGreoK-9bIWzX0wFgVUjA7XBQ.png"], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"productId": "58e2f0b3-4494-4989-b764-9d12ef312527", "productName1": "", "productName2": "", "productName3": "ST-2(사티)_제1형태", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "데포르메 스타일의 ST-2(사티)의 제1형태 모습입니다.\n조립 및 도색이 필요한 레진 킷 형태로 제작 예정입니다. ", "productDesc4": "", "productDesc5": "", "baseCurrency": "USD", "productPrice": {"usd": 80000.0, "jpy": 11499560.0, "krw": 109912532.56}, "totalStock": 5, "unitSold": 0, "remStock": 5, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-27T09:33:44.998Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "4193d6ef-5c82-4934-a150-9da29a3ab499", "nickname": "2951729673", "avatarUrl": null}, "ipId": "8aedb9bb-5c66-4bc7-9623-2483659c8769", "ipName1": "", "ipName2": "", "ipName3": "Umamusume: Pretty Derby / ウマ娘 プリティーダービー", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%82%AC%EB%B3%B8%20-20250525_193351.jpg"], "fileNames": ["사본 -20250525_193351.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "6207c4ad-e438-4817-a72f-6e683e084df6", "productName1": "", "productName2": "", "productName3": "승리의 여신 : 니케 - 브래디", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "이 제품은 전시 목적의 도색 완제품 하나와 판매 목적으로 나가는 미도색 복제개러지킷으로 제작 할 예정입니다.\n\n도색 완제품 : 1개 / 개러지킷 : 최대 40개", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 129.91, "jpy": 18688.82, "krw": 180000.0}, "totalStock": 40, "unitSold": 0, "remStock": 40, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-21T04:11:57.916Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "fbfc33d2-64df-45f4-a3fc-b522d33332fe", "nickname": "8381993260", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/FoxJump_LOGO1.png"}, "ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "ART", "productCatName1": "Art & Crafts", "productCatName2": "アート・クラフト", "productCatName3": "아트/공예", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/01.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/02.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/03.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/04.png"], "fileNames": ["01.png", "02.png", "03.png", "04.png"], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"productId": "69f0190a-e637-4646-b73b-f71b4ce06d4e", "productName1": "나가: 최후의 소녀시대 Naga: Last Girlhood ", "productName2": "", "productName3": "", "productName4": "", "productName5": "", "productDesc1": "이 제품은 게임 내 스킨을 기반으로 제작된 팬 메이드 개러지 키트 피규어입니다.\n\"나가: 최후의 소녀시대 (Naga: Last Girlhood)\" 캐릭터의 특정 의상을 모티프로 하여 1/7 스케일의 레진 키트로 제작되었습니다.\n디자인 및 조형은 모두 본인이 직접 작업하였으며,\n본 제품은 양산품이 아니며 행사 현장에서만 한정 수량으로 판매될 예정입니다.\n해당 작품은 개인 창작 팬 아트로서, 상업적 목적이 아닌 행사 출품용입니다.", "productDesc2": "", "productDesc3": "", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 183.05, "jpy": 26095.03, "krw": 250000.0}, "totalStock": 30, "unitSold": 0, "remStock": 30, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-25T12:02:35.43Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "7ea30ff1-1fe8-4501-bb1a-9ee3ec846bc4", "nickname": "9117454930", "avatarUrl": null}, "ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/2-Naga%20Last%20Girlhood%20%285%29.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/2-Naga%20Last%20Girlhood%20%281%29.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/2-Naga%20Last%20Girlhood%20%282%29.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/2-Naga%20Last%20Girlhood%20%283%29.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/2-Naga%20Last%20Girlhood%20%284%29.png"], "fileNames": ["2-<PERSON>ga Last Girlhood (5).png", "2-Naga Last Girlhood (1).png", "2-Naga Last Girlhood (2).png", "2-<PERSON>ga Last Girlhood (3).png", "2-<PERSON>ga Last Girlhood (4).png"], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"productId": "706f54ce-d921-4320-b2dc-d8245d3c7b87", "productName1": "", "productName2": "", "productName3": "미니 오구리캡", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "- 우마무스메/오구리 캡\n- 논스케일 미니 피규어\n- 4.5cm 이하\n- 오구리 캡의 귀엽고 아름다움을 작게 데포르메해서 표현했 \n  습니다.\n  오구리의 특징인 많이 먹는모습을 만들예정입니다..", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 33.03, "jpy": 4701.28, "krw": 45000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-26T03:24:44.049Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "99fe152c-4d3d-4fdf-8e39-6ad21dd10b37", "nickname": "1807316093", "avatarUrl": null}, "ipId": "8aedb9bb-5c66-4bc7-9623-2483659c8769", "ipName1": "", "ipName2": "", "ipName3": "Umamusume: Pretty Derby / ウマ娘 プリティーダービー", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/cap.jpg"], "fileNames": ["cap.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "70ac6128-28fc-43bd-a051-9e9eccf721b2", "productName1": "길로틴 윈터 슬레이어", "productName2": "", "productName3": "", "productName4": "", "productName5": "", "productDesc1": "22cm 개러지 킷", "productDesc2": "", "productDesc3": "", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 101.63, "jpy": 14609.32, "krw": 140000.0}, "totalStock": 3, "unitSold": 0, "remStock": 3, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-21T14:44:27.335Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "25336621-ad9e-4d51-8ae2-4ff6796360ec", "nickname": "8858585875", "avatarUrl": null}, "ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/2.jpg"], "fileNames": ["1.jpg", "2.jpg"], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"productId": "71c218b0-0184-4178-997a-575cde8232ec", "productName1": "", "productName2": "ワンダ＆リセット 1/6スケールフィギュアセット", "productName3": "", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "ワンダ＆リセット 1/6スケールフィギュアセット \ntest\ntest\ntest", "productDesc3": "", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 34.87, "jpy": 5008.41, "krw": 50000.0}, "totalStock": 15, "unitSold": 0, "remStock": 15, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-04-25T06:58:39.296Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "49f8ab43-ba78-4ac1-abac-ec5aafd2f1bc", "nickname": "원더페스티벌 한국 실행위원회 ", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/%E1%84%89%E1%85%B3%E1%84%8F%E1%85%B3%E1%84%85%E1%85%B5%E1%86%AB%E1%84%89%E1%85%A3%E1%86%BA%202025-03-27%20%E1%84%8B%E1%85%A9%E1%84%92%E1%85%AE%2012.51.36.png"}, "ipId": "4215a41a-2cef-455d-bfcf-1103fcec656a", "ipName1": "", "ipName2": "", "ipName3": "Wonder Festival 2025 Korea 완다 & 리셋", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%8B%E1%85%AA%E1%86%AB%E1%84%83%E1%85%A1%2C%E1%84%85%E1%85%B5%E1%84%89%E1%85%A6%E1%86%BA.png"], "fileNames": ["완다,리셋.png"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "7494647c-d1ea-46b2-938b-42f319ddd874", "productName1": "", "productName2": "", "productName3": "ツインターボ 피규어(도색)", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "ツインターボ SD 피규어 도색 완성작입니다.\n트윈 터보의 귀여움을 담았습니다.\n키트가 아닌 도색 완성작입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 144.19, "jpy": 20701.34, "krw": 200000.0}, "totalStock": 3, "unitSold": 0, "remStock": 3, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-21T07:37:39.523Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "4a1606a4-7f21-4dae-847c-bf7c9ab4ea63", "nickname": "6615873545", "avatarUrl": null}, "ipId": "8aedb9bb-5c66-4bc7-9623-2483659c8769", "ipName1": "", "ipName2": "", "ipName3": "Umamusume: Pretty Derby / ウマ娘 プリティーダービー", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%ED%94%BC%EA%B7%9C%EC%96%B4.jpg"], "fileNames": ["피규어.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "76974983-ba54-40e2-994a-f335e5871ff6", "productName1": "", "productName2": "", "productName3": "전동 도로롱 개조 키트", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "REMOTE CONTROL Robot dog 부품과 결합하여 만드는 전동 도로롱 레진캐스트 키트", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 58.15, "jpy": 8389.41, "krw": 80000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-28T08:17:31.665Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "1198d9cd-f8c7-4752-8e2f-0813508b8950", "nickname": "5886553682", "avatarUrl": null}, "ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "ART", "productCatName1": "Art & Crafts", "productCatName2": "アート・クラフト", "productCatName3": "아트/공예", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1966203a96c1b8.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EB%8F%84%EB%A1%9C%EB%A1%B16.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EB%8F%84%EB%A1%9C%EB%A1%B13.jpg"], "fileNames": ["1966203a96c1b8.jpg", "도로롱6.jpg", "도로롱3.jpg"], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"productId": "7b2f4f73-16f6-4374-9623-d9f5a2dab84d", "productName1": "", "productName2": "", "productName3": "미니 다이와 스칼렛", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "- 우마무스메/다이와 스칼렛\n- 논스케일 미니 피규어\n- 4.5cm 이하\n- 다이와 스칼렛의 귀엽고 아름다움을 작게 데포르메해서 표현했 \n  습니다.\n  이쁘게 달리는 모습이나 1착승리 포즈로 만들예정입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 33.03, "jpy": 4701.28, "krw": 45000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-26T03:20:16.794Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "99fe152c-4d3d-4fdf-8e39-6ad21dd10b37", "nickname": "1807316093", "avatarUrl": null}, "ipId": "8aedb9bb-5c66-4bc7-9623-2483659c8769", "ipName1": "", "ipName2": "", "ipName3": "Umamusume: Pretty Derby / ウマ娘 プリティーダービー", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/daiwa.jpg"], "fileNames": ["daiwa.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "7f5e5cf1-e91e-44a2-9d6f-b647646e771b", "productName1": "앵커 이노센트 메이드", "productName2": "", "productName3": "", "productName4": "", "productName5": "", "productDesc1": "14cm sd 개러지 킷", "productDesc2": "", "productDesc3": "", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 58.08, "jpy": 8348.18, "krw": 80000.0}, "totalStock": 3, "unitSold": 0, "remStock": 3, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-21T14:49:09.403Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "25336621-ad9e-4d51-8ae2-4ff6796360ec", "nickname": "8858585875", "avatarUrl": null}, "ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/3.jpg"], "fileNames": ["3.jpg"], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"productId": "81940f34-7e83-412f-a787-23f8c2274075", "productName1": "", "productName2": "", "productName3": "킷삼이 줄자", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "나나링 팬덤 캐릭터 ‘킷삼이’ 모양의 150cm 줄자입니다. 나나링의 키인 148cm를 정확히 잴 수 있도록 맞춤 제작되었으며, 킷삼이의 머리를 잡아당기면 줄자가 나오는 구조입니다. 등쪽의 버튼을 누르면 줄자가 자동으로 되감깁니다. PLA 소재로 제작되었으며, 멀티컬러 3D 프린터를 활용해 제작된 팬덤 굿즈입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 21.93, "jpy": 3119.63, "krw": 30000.0}, "totalStock": 20, "unitSold": 0, "remStock": 20, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-27T04:21:17.774Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "3bfa08a0-2fd9-4ce5-9326-b758df9df0a6", "nickname": "8990641148", "avatarUrl": null}, "ipId": "f0a7696e-03c1-431f-b1a0-027250301551", "ipName1": "", "ipName2": "", "ipName3": "나나링 ", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "HOME", "productCatName1": "Home & Lifestyle", "productCatName2": "ホーム・ライフスタイル", "productCatName3": "홈/라이프스타일", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/KitsamRuler%20%281%29.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/KitsamRuler%20%282%29.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/KitsamRuler%20%283%29.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/KitsamRuler%20%284%29.jpg"], "fileNames": ["KitsamRuler (1).jpg", "KitsamRuler (2).jpg", "KitsamRuler (3).jpg", "KitsamRuler (4).jpg"], "owner": {"id": "79a0151b-aba1-4363-ba0c-abf350aaf796", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "나나링"}}, {"productId": "86c12b1f-1805-4f8a-84bd-9e83111dad85", "productName1": "", "productName2": "", "productName3": "하루우라라 [새해의 화창함♪ 활짝 핀 벚꽃]", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "데포르메 스타일의 하루우라라입니다.\n조립 및 도색이 필요한 레진 킷 형태로 제작될 예정입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "USD", "productPrice": {"usd": 80000.0, "jpy": 11499560.0, "krw": 109912532.56}, "totalStock": 5, "unitSold": 0, "remStock": 5, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-27T09:37:24.184Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "4193d6ef-5c82-4934-a150-9da29a3ab499", "nickname": "2951729673", "avatarUrl": null}, "ipId": "8aedb9bb-5c66-4bc7-9623-2483659c8769", "ipName1": "", "ipName2": "", "ipName3": "Umamusume: Pretty Derby / ウマ娘 プリティーダービー", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%82%AC%EB%B3%B8%20-20250526_001709.jpg"], "fileNames": ["사본 -20250526_001709.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "8e7c644d-dae9-4794-92d6-8357ade366ae", "productName1": "", "productName2": "", "productName3": "니케 레드후드", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "레드후드 미도색 레진킷", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 146.08, "jpy": 20844.21, "krw": 200000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-27T02:51:41.289Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "91f1e4cc-34d1-4565-84a6-ea01e84379dc", "nickname": "7402346441", "avatarUrl": null}, "ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%5B%ED%8F%AC%EB%A7%B7%EB%B3%80%ED%99%98%5DKakaoTalk_20240705_125020353.jpg"], "fileNames": ["[포맷변환]KakaoTalk_20240705_125020353.jpg"], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"productId": "912607c2-52fe-421c-80c8-34be38761b7c", "productName1": "", "productName2": "", "productName3": "마빅 엘립스 트렉용 프론트 휠셋 및 삼발이 카본 리어 휠셋 1/6 스케일 모형", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "\"MAVIC(마빅)\"은 많은 자전거 동호인들이 즐겨타고 좋아하는, 프랑스 자전거 휠셋 제조 명가입니다.\n\"ELLIPSE(엘립스)\"는 픽시 자전거를 탄다면 누구나 써보고 싶어하는 인기 휠셋입니다.\n\n저는 이 자전거의 휠셋을 기존 자전거 피규어나 장난감의 수준으로부터 디테일업한 모형으로 제작하려합니다.\n모형의 원형사인 본인은 실제 자전거 제조업체의 자전거 설계자 출신으로, 리얼함을 추구합니다.\n회전부는 모두 실제 미니어쳐 베어링을 적용 예정이고, 코그(스프라켓)는 에칭으로 제작 예정입니다. 휠스포크는 황동선이나 스틸선을 적용할 예정입니다.\n다만, 실제 엘립스는 클린처(타이어 안에 튜브를 집어넣는 구조)림이지만, 모형은 제조편의상 튜블러 타입의 림 휠로 디자인을 변경하였고, 타이어는 1/6 스케일에 맞는 오링을 적용할 예정입니다.\n\n실제 브랜드 로고 및 상표를 정식으로 사용하여 만든 모형은, 한국의 유명 웹툰(윈드 브레이커)의 케릭터(셸리 스콧)와 콜라보하여, 각 브랜드별 홍보효과의 시너지를 상승시킬 예정입니다.\n\nIP판권에 해당하는 부분별로 나누어(프레임셋/휠셋/케릭터) 신청 및 판매할 예정이지만, 합본판매 조율이 된다면, 합본으로 판매하고 싶습니다.\n\nIP와 관련된 부분은 다음과 같습니다.\n1. 프론트 휠 외관 형상은 마빅 엘립스 휠셋 형상 차용.\n2. 노란색 MAVIC 브랜드명: 프론트휠 림 좌/우 3포인트 부분과 허브중앙에 데칼, \n3. ELLIPSE 상표명: 프론트휠 림 좌/우 3포인트 부분에 데칼\n\n삼발이 카본 리어 휠셋은 비브랜드명인 HED.3 데칼을 스포크 좌/우 한포인트씩 적용 예정.\n판매품은 모두 미도색 레진이고, 전시샘플만 도색 예정.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 71.6, "jpy": 10465.91, "krw": 100000.0}, "totalStock": 50, "unitSold": 0, "remStock": 50, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "02", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-14T13:46:16.422Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "7c793c6e-f65a-47a8-b64a-c78268ea516f", "nickname": "5883640081", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/%EA%B9%80%ED%99%8D%EC%A4%80%EB%8B%98%20%EB%A1%9C%EA%B3%A0.png"}, "ipId": "4215a41a-2cef-455d-bfcf-1103fcec656a", "ipName1": "", "ipName2": "", "ipName3": "Wonder Festival 2025 Korea 완다 & 리셋", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1.%203D%20images%20of%20wheelsets.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/2.%20Other%20parts%28Frameset%29%20assembly%20examples.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/3.%20Miniature%20bearing%20applications.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/4.%20MAVIC%20logo%20and%20trademark%20decals.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/5.%20Real%20bicycle%20example.jpg"], "fileNames": ["1. 3D images of wheelsets.jpg", "2. Other parts(Frameset) assembly examples.jpg", "3. Miniature bearing applications.jpg", "4. MAVIC logo and trademark decals.jpg", "5. Real bicycle example.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "973af7a5-906b-49a2-b94e-7ff0fc8bffb8", "productName1": "", "productName2": "", "productName3": "승리의 여신: 니케 - 릴리바이스", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "전시 목적으로 제작하는 [승리의 여신: 니케 - 릴리바이스 피규어]\n첨부된 이미지는 렌더링 이미지이며, 실제 피규어는 제작중입니다.\n(판매가와 관련없습니다.)", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 0.0, "jpy": 0.1, "krw": 1.0}, "totalStock": 1, "unitSold": 0, "remStock": 1, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-21T04:01:53.6Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "fbfc33d2-64df-45f4-a3fc-b522d33332fe", "nickname": "8381993260", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/FoxJump_LOGO1.png"}, "ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "ART", "productCatName1": "Art & Crafts", "productCatName2": "アート・クラフト", "productCatName3": "아트/공예", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EB%A6%B4%EB%A6%AC%EB%B0%94%EC%9D%B4%EC%8A%A401.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EB%A6%B4%EB%A6%AC%EB%B0%94%EC%9D%B4%EC%8A%A402.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EB%A6%B4%EB%A6%AC%EB%B0%94%EC%9D%B4%EC%8A%A403.jpg"], "fileNames": ["릴리바이스01.jpg", "릴리바이스02.jpg", "릴리바이스03.jpg"], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"productId": "9bf26317-00b1-41ec-8ad2-dfdd9afb1142", "productName1": "", "productName2": "", "productName3": "비상천칙", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "비상천칙의 데포르메 가동형 피규어의 레진 킷입니다.\n조립 및 도색이 필요한 레진킷 형태로 제작 예정입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 70.22, "jpy": 10400.34, "krw": 100000.0}, "totalStock": 5, "unitSold": 0, "remStock": 5, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "03", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-13T13:15:17.584Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "4193d6ef-5c82-4934-a150-9da29a3ab499", "nickname": "2951729673", "avatarUrl": null}, "ipId": "e26c92f8-204c-4422-ba80-9a11a28e574d", "ipName1": "", "ipName2": "", "ipName3": "동방프로젝트 / 東方Project", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/3804hg.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/-gmbu38.jpg"], "fileNames": ["3804hg.jpg", "-gmbu38.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "9cadc467-c460-433c-aa45-90139f4c8fce", "productName1": "", "productName2": "", "productName3": "데미안전기", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "데미안전기의 이사벨라 피규어.\n현재 캐릭터 일러스트를 타겟으로 타겟모델링을 진행하고 있습니다. SLA 프린터를 활용해서 높이기준 20cm 가량의 피규어로 출력하고 가공하여 도색된 피규어로 완성하려 합니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 581.46, "jpy": 83894.19, "krw": 800000.0}, "totalStock": 1, "unitSold": 0, "remStock": 1, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-28T07:23:59.512Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "32bdb93a-f6e3-43bb-8639-e189fc5ee8a3", "nickname": "1055323191", "avatarUrl": null}, "ipId": "5abf6ebe-034d-4773-9334-fabc63c1d237", "ipName1": "", "ipName2": "", "ipName3": "데미안 전기", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "ART", "productCatName1": "Art & Crafts", "productCatName2": "アート・クラフト", "productCatName3": "아트/공예", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%ED%99%94%EB%A9%B4%20%EC%BA%A1%EC%B2%98%202025-05-28%20162040.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/KakaoTalk_20250527_173124328_02.jpg"], "fileNames": ["화면 캡처 2025-05-28 162040.jpg", "KakaoTalk_20250527_173124328_02.jpg"], "owner": {"id": "26ca2b66-a47e-434f-9984-314ad5704246", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "(주)해긴"}}, {"productId": "a1cd88c5-bea9-48fa-8e02-b28902309843", "productName1": "", "productName2": "", "productName3": "유니", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "데포르메 스타일의 유니입니다.\n조립 및 도색이 필요한 레진 킷 형태로 제작될 예정입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "USD", "productPrice": {"usd": 50000.0, "jpy": 7187225.0, "krw": 68695332.85}, "totalStock": 5, "unitSold": 0, "remStock": 5, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-27T09:42:44.997Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "4193d6ef-5c82-4934-a150-9da29a3ab499", "nickname": "2951729673", "avatarUrl": null}, "ipId": "3a1bff4a-3f89-4dc3-9231-484b68ee2976", "ipName1": "", "ipName2": "", "ipName3": "Princess Connect! Re: Dive / プリンセスコネクト！ Re:Dive", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/qoai8u.jpg"], "fileNames": ["qoai8u.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "a96c8bec-92cd-4df6-be8f-ef15998b08aa", "productName1": "", "productName2": "", "productName3": "신비한 캬루", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "NON SCALE의 SD 캬루 피규어 레진캐스트 키트", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 29.07, "jpy": 4194.71, "krw": 40000.0}, "totalStock": 20, "unitSold": 0, "remStock": 20, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-28T08:06:16.348Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "1198d9cd-f8c7-4752-8e2f-0813508b8950", "nickname": "5886553682", "avatarUrl": null}, "ipId": "3a1bff4a-3f89-4dc3-9231-484b68ee2976", "ipName1": "", "ipName2": "", "ipName3": "Princess Connect! Re: Dive / プリンセスコネクト！ Re:Dive", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "ART", "productCatName1": "Art & Crafts", "productCatName2": "アート・クラフト", "productCatName3": "아트/공예", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/177ccfb6cdd1b8.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/177ccfb5c911b8.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%BA%AC%EB%A3%A8%20%EC%B8%A1%EB%A9%B4.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%BA%AC%EB%A3%A8%20%ED%9B%84%EB%A9%B4.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/177ccfb97f41b8.jpg"], "fileNames": ["177ccfb6cdd1b8.jpg", "177ccfb5c911b8.jpg", "캬루 측면.jpg", "캬루 후면.jpg", "177ccfb97f41b8.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "aae42e94-ab7d-420f-9d55-b9ffa35c44e7", "productName1": "", "productName2": "", "productName3": "크리에이터 테스트 신청", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "크리에이터 테스트 신청", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 55.8, "jpy": 8013.47, "krw": 80000.0}, "totalStock": 5, "unitSold": 0, "remStock": 5, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-04-25T06:56:23.755Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "f81afec3-8434-48c0-ba16-6c0135097ac3", "nickname": "WF크리에이터", "avatarUrl": null}, "ipId": "4215a41a-2cef-455d-bfcf-1103fcec656a", "ipName1": "", "ipName2": "", "ipName3": "Wonder Festival 2025 Korea 완다 & 리셋", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%8B%E1%85%AA%E1%86%AB%E1%84%83%E1%85%A1%2C%E1%84%85%E1%85%B5%E1%84%89%E1%85%A6%E1%86%BA.png"], "fileNames": ["완다,리셋.png"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "adf3555e-b087-44c7-b4dd-91eb21a04905", "productName1": "", "productName2": "", "productName3": "ツインターボ SD 피규어(키트)", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "ツインターボ SD 피규어 키트입니다.\n트윈 터보의 귀여움을 담았습니다.\n도색 완성작이 아닌 키트입니다.\n직접 칠해야 합니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 39.65, "jpy": 5692.87, "krw": 55000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-21T07:42:53.56Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "4a1606a4-7f21-4dae-847c-bf7c9ab4ea63", "nickname": "6615873545", "avatarUrl": null}, "ipId": "8aedb9bb-5c66-4bc7-9623-2483659c8769", "ipName1": "", "ipName2": "", "ipName3": "Umamusume: Pretty Derby / ウマ娘 プリティーダービー", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%ED%94%BC%EA%B7%9C%EC%96%B4.jpg"], "fileNames": ["피규어.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "ae2cb810-e747-47e7-bd6b-b301ac7a96ab", "productName1": "", "productName2": "", "productName3": "승리의 여신: 니케 - 브래디", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "이 제품은 전시 목적의 도색 완제품 하나와 판매 목적으로 나가는 미도색 복제개러지킷으로 제작 할 예정입니다.\n\n도색 완제품 : 1개 / 개러지킷 : 최대 40개\n\n홈페이지 이미지 버그로 인하여 재업로드합니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 129.91, "jpy": 18688.82, "krw": 180000.0}, "totalStock": 40, "unitSold": 0, "remStock": 40, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-21T04:25:45.56Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "fbfc33d2-64df-45f4-a3fc-b522d33332fe", "nickname": "8381993260", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/FoxJump_LOGO1.png"}, "ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "ART", "productCatName1": "Art & Crafts", "productCatName2": "アート・クラフト", "productCatName3": "아트/공예", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/01.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/02.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/03.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/04.png"], "fileNames": ["01.png", "02.png", "03.png", "04.png"], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"productId": "b31e2366-e7ea-4984-9eb8-23e11d8bac65", "productName1": "", "productName2": "", "productName3": "캬루베로스", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "NON SCALE의 SD 캬루 피규어 레진캐스트 키트", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 29.07, "jpy": 4194.71, "krw": 40000.0}, "totalStock": 20, "unitSold": 0, "remStock": 20, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-28T08:08:33.887Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "1198d9cd-f8c7-4752-8e2f-0813508b8950", "nickname": "5886553682", "avatarUrl": null}, "ipId": "3a1bff4a-3f89-4dc3-9231-484b68ee2976", "ipName1": "", "ipName2": "", "ipName3": "Princess Connect! Re: Dive / プリンセスコネクト！ Re:Dive", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "ART", "productCatName1": "Art & Crafts", "productCatName2": "アート・クラフト", "productCatName3": "아트/공예", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/17359df90e61b8.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/17359e166ab1b8.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/17359df8d2a1b8.jpg"], "fileNames": ["17359df90e61b8.jpg", "17359e166ab1b8.jpg", "17359df8d2a1b8.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "bc79c093-7159-411d-a85c-be537e5e80c0", "productName1": "", "productName2": "", "productName3": "미니 멘하탄 카페", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "- 우마무스메/다이와 스칼렛\n- 논스케일 미니 피규어\n- 4.5cm 이하\n- 다이와 스칼렛의 귀엽고 아름다움을 작게 데포르메해서 표현했 \n  습니다.\n 멘하탄 카페의 수줍어하고있는 포즈를 만들예정입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 33.03, "jpy": 4701.28, "krw": 45000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-26T03:23:08.261Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "99fe152c-4d3d-4fdf-8e39-6ad21dd10b37", "nickname": "1807316093", "avatarUrl": null}, "ipId": "8aedb9bb-5c66-4bc7-9623-2483659c8769", "ipName1": "", "ipName2": "", "ipName3": "Umamusume: Pretty Derby / ウマ娘 プリティーダービー", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/cafe.jpg"], "fileNames": ["cafe.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "c00bb61f-d2ee-4978-b647-1d0d75d2d478", "productName1": "", "productName2": "", "productName3": "다이와 스칼렛 수영복", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "- 우마무스메/다이와 스칼렛\n- 1/6스케일 피규어 \n- 20cm 이하\n- 다이와 스칼렛의 건강미과 익살스러운 성격을 표현했습니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 0.0, "jpy": 0.1, "krw": 1.0}, "totalStock": 1, "unitSold": 0, "remStock": 1, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-26T07:01:03.143Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "99fe152c-4d3d-4fdf-8e39-6ad21dd10b37", "nickname": "1807316093", "avatarUrl": null}, "ipId": "8aedb9bb-5c66-4bc7-9623-2483659c8769", "ipName1": "", "ipName2": "", "ipName3": "Umamusume: Pretty Derby / ウマ娘 プリティーダービー", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/daiwa_03.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/daiwa_04.jpg"], "fileNames": ["daiwa_03.jpg", "daiwa_04.jpg"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "c1e26810-f2e6-4317-ac5b-3b3d2e2a2c7f", "productName1": "", "productName2": "", "productName3": "완다 & 리셋 1/6 피규어 세트", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "각 캐릭터의 상징적인 하이테크 수트 디테일과 시그니처 포즈(완다의 레이저 블래스터 자세 / 리셋의 퀀텀점프 준비 동작)를 중심으로 조형하였으며,발랄한 에너지가 느껴지는 완다와, 차분한 카리스마를 가진 리셋의 대비적인 매력을 동시에 표현하는 데에 중점을 두었습니다.\n\n네온 계열의 공식 컬러와 반투명 파츠(버블실드, 네코부스트 효과)를 활용해 입체적이고 생동감 있는 디오라마 형태로 제작 예정입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 61.34, "jpy": 8759.03, "krw": 88000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "03", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-04-24T06:54:29.363Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "49f8ab43-ba78-4ac1-abac-ec5aafd2f1bc", "nickname": "원더페스티벌 한국 실행위원회 ", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/%E1%84%89%E1%85%B3%E1%84%8F%E1%85%B3%E1%84%85%E1%85%B5%E1%86%AB%E1%84%89%E1%85%A3%E1%86%BA%202025-03-27%20%E1%84%8B%E1%85%A9%E1%84%92%E1%85%AE%2012.51.36.png"}, "ipId": "4215a41a-2cef-455d-bfcf-1103fcec656a", "ipName1": "", "ipName2": "", "ipName3": "Wonder Festival 2025 Korea 완다 & 리셋", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%8B%E1%85%AA%E1%86%AB%E1%84%83%E1%85%A1%2C%E1%84%85%E1%85%B5%E1%84%89%E1%85%A6%E1%86%BA.png"], "fileNames": ["완다,리셋.png"], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"productId": "c615fc52-30a4-4499-8904-cb1482e0d3cc", "productName1": "소다: 트윙클링 바니 (Soda: <PERSON><PERSON> Bunny)", "productName2": "", "productName3": "", "productName4": "", "productName5": "", "productDesc1": "이 제품은 Soda: Twinkling Bunny 캐릭터를 바탕으로 한 개러지 키트 피규어입니다.\n원더 페스티벌 코리아에서 전시 및 한정 판매를 목적으로 제작된 팬 메이드 1/7 스케일 레진 모델 키트입니다.\n디자인 및 조형은 전부 본인이 직접 작업하였습니다.\n본 제품은 양산하지 않으며, 행사 현장에서만 한정 수량으로 판매될 예정입니다.", "productDesc2": "", "productDesc3": "", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 146.44, "jpy": 20876.03, "krw": 200000.0}, "totalStock": 30, "unitSold": 0, "remStock": 30, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-25T11:59:07.5Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "7ea30ff1-1fe8-4501-bb1a-9ee3ec846bc4", "nickname": "9117454930", "avatarUrl": null}, "ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1-Soda%20Twinkling%20Bunny%20%281%29.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1-Soda%20Twinkling%20Bunny%20%282%29.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1-Soda%20Twinkling%20Bunny%20%283%29.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1-Soda%20Twinkling%20Bunny%20%284%29.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1-Soda%20Twinkling%20Bunny%20%285%29.png"], "fileNames": ["1-<PERSON><PERSON> (1).png", "1-<PERSON><PERSON> (2).png", "1-<PERSON><PERSON> (3).png", "1-<PERSON><PERSON> (4).png", "1-<PERSON><PERSON> (5).png"], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"productId": "cefc27f3-5bc7-4b26-afdd-5b938fa7d559", "productName1": "", "productName2": "", "productName3": "初音ミク-cattleya.ver", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "하츠네 미쿠의 2차창작, 카틀레야.ver 피규어 레진킷입니다.\n\n22cm, 한국의 전통 의상 한복을 입고 카틀레야 장식을 달고 있습니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 144.95, "jpy": 20756.09, "krw": 200000.0}, "totalStock": 3, "unitSold": 0, "remStock": 3, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-22T05:54:56.788Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "4a1606a4-7f21-4dae-847c-bf7c9ab4ea63", "nickname": "6615873545", "avatarUrl": null}, "ipId": "94aa41c5-0684-469f-b5b4-0b3257202ceb", "ipName1": "", "ipName2": "ｷｬﾗｸﾀｰ･ﾎﾞｰｶﾙ･ｼﾘｰｽﾞ01　初音ミク", "ipName3": "", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%ED%95%9C%EB%B3%B5%EB%AF%B8%EC%BF%A0.jpg"], "fileNames": ["한복미쿠.jpg"], "owner": {"id": "5cb2393a-c71b-4261-98d3-c3b1801168a9", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "Crypton Future Media, INC."}}, {"productId": "ec27c46b-87e6-4830-a64c-2bb9da00b664", "productName1": "", "productName2": "", "productName3": "스프링필드", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "스프링필드의 카페 일러스트를 기반으로 오리지널 포즈로 제작예정입니다.\n전시만 할 예정으로 스케일은 약1/8정도 입니다.\n", "productDesc4": "", "productDesc5": "", "baseCurrency": "USD", "productPrice": {"usd": 1.0, "jpy": 147.71, "krw": 1418.5}, "totalStock": 1, "unitSold": 0, "remStock": 1, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "03", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-12T10:39:21.342Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "a2c637bd-1a09-4078-9840-51bd74c32a5e", "nickname": "2500398455", "avatarUrl": "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/avatar/Logo_back.png"}, "ipId": "f0edc8b0-9e4d-4472-9499-59496a80559b", "ipName1": "GIRL`S FRONTLINE", "ipName2": "", "ipName3": "", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%98%A4%EB%A6%AC%EC%A7%80%EB%84%90%20%EC%9D%BC%EB%9F%AC.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/5555.JPG"], "fileNames": ["오리지널 일러.jpg", "5555.JPG"], "owner": {"id": "83c95581-13f0-4f9a-9364-e7fceb5b19f8", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "Shanghai Yunjie Trading Co., Ltd."}}, {"productId": "f31d487f-96f4-463e-bc0a-b28c8df0c310", "productName1": "", "productName2": "", "productName3": "하츠네미쿠 3cm 열쇠고리", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "휴대가 용이한 하츠네미쿠 미니키링 피규어입니다. 작은 크기 안에서 최대한의 디테일을 담기 위해 노력했습니다.\n실리콘틀을 이용해 우레탄으로 원형을 복제하고,\n아크릴물감과 붓을 사용해 색을 칠합니다.\n피막강도가 약하다고 판단되면 유광마감제로 마감합니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 14.48, "jpy": 2075.45, "krw": 20000.0}, "totalStock": 30, "unitSold": 0, "remStock": 30, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-22T07:55:11.168Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "4a1606a4-7f21-4dae-847c-bf7c9ab4ea63", "nickname": "6615873545", "avatarUrl": null}, "ipId": "94aa41c5-0684-469f-b5b4-0b3257202ceb", "ipName1": "", "ipName2": "ｷｬﾗｸﾀｰ･ﾎﾞｰｶﾙ･ｼﾘｰｽﾞ01　初音ミク", "ipName3": "", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/KakaoTalk_20250522_162549652_01.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/KakaoTalk_20250522_162549652.png"], "fileNames": ["KakaoTalk_20250522_162549652_01.jpg", "KakaoTalk_20250522_162549652.png"], "owner": {"id": "5cb2393a-c71b-4261-98d3-c3b1801168a9", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "Crypton Future Media, INC."}}, {"productId": "f3353783-8aed-4b12-ae26-53491d221e29", "productName1": "", "productName2": "", "productName3": "니케 레드후드", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "레드후드 레진킷", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 127.03, "jpy": 18835.82, "krw": 180000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "02", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-13T01:33:45.77Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "91f1e4cc-34d1-4565-84a6-ea01e84379dc", "nickname": "7402346441", "avatarUrl": null}, "ipId": "fe6878a8-3792-4e1f-a395-e6686797c7fa", "ipName1": "", "ipName2": "", "ipName3": "오즈 리:라이트'(OZ Re:write) - 엔젤", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%5B%ED%8F%AC%EB%A7%B7%EB%B3%80%ED%99%98%5DKakaoTalk_20240705_125020353.jpg"], "fileNames": ["[포맷변환]KakaoTalk_20240705_125020353.jpg"], "owner": {"id": "d9377607-6f94-49f3-b9f8-611504b9bc0e", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 마코빌"}}, {"productId": "fa7cceaf-a8ca-4232-99d3-b16d62f6a2d1", "productName1": "", "productName2": "", "productName3": "하츠네미쿠 교복.ver 피규어", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "유튜브채널 \"Hiiragi Magnetite(柊マグネタイト)\"님의 \"잡어(雑魚)\" MV에 출연한 교복버전의 하츠네미쿠입니다.\n허리에 묶고있는 분홍색 가디건과 커다란 빨간색 리본, 곱창 머리끈과 머리핀을 착용한 것이 특징입니다. \n베이스 제외 13cm\n완도색 수제 피규어", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 35.48, "jpy": 5084.87, "krw": 49000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-22T07:16:49.709Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "4a1606a4-7f21-4dae-847c-bf7c9ab4ea63", "nickname": "6615873545", "avatarUrl": null}, "ipId": "94aa41c5-0684-469f-b5b4-0b3257202ceb", "ipName1": "", "ipName2": "ｷｬﾗｸﾀｰ･ﾎﾞｰｶﾙ･ｼﾘｰｽﾞ01　初音ミク", "ipName3": "", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/KakaoTalk_20250522_161040378.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/KakaoTalk_20250522_161040378_01.png"], "fileNames": ["KakaoTalk_20250522_161040378.jpg", "KakaoTalk_20250522_161040378_01.png"], "owner": {"id": "5cb2393a-c71b-4261-98d3-c3b1801168a9", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "Crypton Future Media, INC."}}, {"productId": "fcdb6b4b-a1f5-4395-ad1a-d05b30024c1e", "productName1": "마스트 로망틱 메이드", "productName2": "", "productName3": "", "productName4": "", "productName5": "", "productDesc1": "14cm sd 개러지 킷", "productDesc2": "", "productDesc3": "", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 58.08, "jpy": 8348.18, "krw": 80000.0}, "totalStock": 5, "unitSold": 0, "remStock": 5, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "01", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-21T14:48:24.137Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "25336621-ad9e-4d51-8ae2-4ff6796360ec", "nickname": "8858585875", "avatarUrl": null}, "ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/4.jpg"], "fileNames": ["4.jpg"], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"productId": "fd84925e-b6ff-46ca-bbeb-1268958438f8", "productName1": "", "productName2": "", "productName3": "소녀전선 디너게이트 RO 피규어 키트", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "이벤트에서 등장했었던 ro635의 디너게이트 버전을 모티브로 조형했습니다.\n\n다리는 가동 설계가 되어 간단한 액션이 가능합니다.\n눈의 렌즈는 투명 파츠로 제공됩니다. 머리 안쪽에 공간을 두어서 원한다면 별도 작업으로 LED파츠를 삽입할 수 도 있습니다.\n", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 31.79, "jpy": 4698.4, "krw": 45000.0}, "totalStock": 2, "unitSold": 0, "remStock": 2, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "03", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-13T07:13:06.026Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "ba8f105b-09c2-4aef-b85c-4820350ec5d6", "nickname": "3477372140", "avatarUrl": null}, "ipId": "f0edc8b0-9e4d-4472-9499-59496a80559b", "ipName1": "GIRL`S FRONTLINE", "ipName2": "", "ipName3": "", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/2.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/4.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/3.PNG"], "fileNames": ["2.jpg", "1.jpg", "4.jpg", "3.P<PERSON>"], "owner": {"id": "83c95581-13f0-4f9a-9364-e7fceb5b19f8", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "Shanghai Yunjie Trading Co., Ltd."}}, {"productId": "fdd32787-8651-4a22-8a6c-741e2a4700b9", "productName1": "", "productName2": "", "productName3": "소녀전선 케르베로스 8cm피규어", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "철혈공조 케르베로스의 앉은 자세 일러스트를 모티브로 제작했습니다.\n비가동형 완전도색 완성품 피규어로 판매예정입니다. 눈의 렌즈는 클리어 파츠입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 84.79, "jpy": 12529.07, "krw": 120000.0}, "totalStock": 10, "unitSold": 0, "remStock": 10, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "03", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-13T08:16:06.557Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "ba8f105b-09c2-4aef-b85c-4820350ec5d6", "nickname": "3477372140", "avatarUrl": null}, "ipId": "f0edc8b0-9e4d-4472-9499-59496a80559b", "ipName1": "GIRL`S FRONTLINE", "ipName2": "", "ipName3": "", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EB%B9%85%EB%8C%95%EC%9D%B41.PNG", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EB%B9%85%EB%8C%95%EC%9D%B42.PNG"], "fileNames": ["빅댕이1.PNG", "빅댕이2.PNG"], "owner": {"id": "83c95581-13f0-4f9a-9364-e7fceb5b19f8", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "Shanghai Yunjie Trading Co., Ltd."}}, {"productId": "fe462411-e864-47fb-8166-bd226052986c", "productName1": "", "productName2": "", "productName3": "소녀전선 케르베로스 4cm피규어", "productName4": "", "productName5": "", "productDesc1": "", "productDesc2": "", "productDesc3": "철혈공조 케르베로스의 앉은 자세 일러스트를 모티브로 제작했습니다.\n비가동형 완전도색 완성품 피규어로 판매예정입니다. 눈의 렌즈는 클리어 파츠입니다.", "productDesc4": "", "productDesc5": "", "baseCurrency": "KRW", "productPrice": {"usd": 28.26, "jpy": 4176.36, "krw": 40000.0}, "totalStock": 20, "unitSold": 0, "remStock": 20, "totalRevGen": {"usd": 0.0, "jpy": 0.0, "krw": 0.0}, "productStatus": "03", "productSaleStatus": "P00", "productDiscount": 0.0, "createdDate": "2025-05-13T08:13:30.82Z", "appTypeName": null, "appTypeCode": "WFKPA", "creator": {"id": "ba8f105b-09c2-4aef-b85c-4820350ec5d6", "nickname": "3477372140", "avatarUrl": null}, "ipId": "f0edc8b0-9e4d-4472-9499-59496a80559b", "ipName1": "GIRL`S FRONTLINE", "ipName2": "", "ipName3": "", "ipName4": "", "ipName5": "", "categories": [{"productCatCode": "TOYS", "productCatName1": "Toys & Games", "productCatName2": "おもちゃ・ゲーム", "productCatName3": "장난감/게임", "productCatName4": "", "productCatName5": ""}], "productImages": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EB%B9%85%EB%8C%95%EC%9D%B41.PNG", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EB%B9%85%EB%8C%95%EC%9D%B42.PNG"], "fileNames": ["빅댕이1.PNG", "빅댕이2.PNG"], "owner": {"id": "83c95581-13f0-4f9a-9364-e7fceb5b19f8", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "Shanghai Yunjie Trading Co., Ltd."}}]