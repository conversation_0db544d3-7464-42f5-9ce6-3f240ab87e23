/**
 * This file was generated by kysely-codegen.
 * Please do not edit it manually.
 */

import type { ColumnType } from "kysely";

export type Generated<T> = T extends ColumnType<infer S, infer I, infer U>
  ? ColumnType<S, I | undefined, U>
  : ColumnType<T, T | undefined, T>;

export type Int8 = ColumnType<string, bigint | number | string, bigint | number | string>;

export type Json = JsonValue;

export type JsonArray = JsonValue[];

export type JsonObject = {
  [x: string]: JsonValue | undefined;
};

export type JsonPrimitive = boolean | number | string | null;

export type JsonValue = JsonArray | JsonObject | JsonPrimitive;

export type Numeric = ColumnType<string, number | string, number | string>;

export type Timestamp = ColumnType<Date, Date | string, Date | string>;

export interface AboutPage {
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  id: Generated<number>;
  user_created: string | null;
  user_updated: string | null;
}

export interface AboutPageTranslations {
  about_page_id: number | null;
  html: string | null;
  id: Generated<number>;
  languages_id: number | null;
}

export interface Addresses {
  address_line_1: Generated<string | null>;
  address_line_2: Generated<string | null>;
  city: Generated<string | null>;
  country: Generated<string | null>;
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  id: Generated<number>;
  name: Generated<string | null>;
  phone_number: Generated<string | null>;
  postcode: Generated<string | null>;
  province: string | null;
  user: string | null;
  user_created: string | null;
  user_updated: string | null;
}

export interface Cart {
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  id: Generated<number>;
  quantity: number | null;
  status: Generated<string>;
  user: string | null;
  user_created: string | null;
  user_updated: string | null;
}

export interface Companies {
  country: string | null;
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  id: Generated<number>;
  image: string | null;
  name: string | null;
  status: Generated<string>;
  url: string | null;
  user_created: string | null;
  user_updated: string | null;
}

export interface Creator {
  avatar: string | null;
  city: string | null;
  country: string | null;
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  first_name: string | null;
  id: Generated<number>;
  introduction: string | null;
  last_name: string | null;
  nickname: string | null;
  postal_code: Generated<string | null>;
  province: string | null;
  sort: number | null;
  status: Generated<string>;
  street_address_1: Generated<string | null>;
  street_address_2: Generated<string | null>;
  user: string | null;
  user_created: string | null;
  user_updated: string | null;
}

export interface DirectusAccess {
  id: string;
  policy: string;
  role: string | null;
  sort: number | null;
  user: string | null;
}

export interface DirectusActivity {
  action: string;
  collection: string;
  id: Generated<number>;
  ip: string | null;
  item: string;
  origin: string | null;
  timestamp: Generated<Timestamp>;
  user: string | null;
  user_agent: string | null;
}

export interface DirectusCollections {
  accountability: Generated<string | null>;
  archive_app_filter: Generated<boolean>;
  archive_field: string | null;
  archive_value: string | null;
  collapse: Generated<string>;
  collection: string;
  color: string | null;
  display_template: string | null;
  group: string | null;
  hidden: Generated<boolean>;
  icon: string | null;
  item_duplication_fields: Json | null;
  note: string | null;
  preview_url: string | null;
  singleton: Generated<boolean>;
  sort: number | null;
  sort_field: string | null;
  translations: Json | null;
  unarchive_value: string | null;
  versioning: Generated<boolean>;
}

export interface DirectusComments {
  collection: string;
  comment: string;
  date_created: Generated<Timestamp | null>;
  date_updated: Generated<Timestamp | null>;
  id: string;
  item: string;
  user_created: string | null;
  user_updated: string | null;
}

export interface DirectusDashboards {
  color: string | null;
  date_created: Generated<Timestamp | null>;
  icon: Generated<string>;
  id: string;
  name: string;
  note: string | null;
  user_created: string | null;
}

export interface DirectusExtensions {
  bundle: string | null;
  enabled: Generated<boolean>;
  folder: string;
  id: string;
  source: string;
}

export interface DirectusFields {
  collection: string;
  conditions: Json | null;
  display: string | null;
  display_options: Json | null;
  field: string;
  group: string | null;
  hidden: Generated<boolean>;
  id: Generated<number>;
  interface: string | null;
  note: string | null;
  options: Json | null;
  readonly: Generated<boolean>;
  required: Generated<boolean | null>;
  sort: number | null;
  special: string | null;
  translations: Json | null;
  validation: Json | null;
  validation_message: string | null;
  width: Generated<string | null>;
}

export interface DirectusFiles {
  charset: string | null;
  created_on: Generated<Timestamp>;
  description: string | null;
  duration: number | null;
  embed: string | null;
  filename_disk: string | null;
  filename_download: string;
  filesize: Int8 | null;
  focal_point_x: number | null;
  focal_point_y: number | null;
  folder: string | null;
  height: number | null;
  id: string;
  location: string | null;
  metadata: Json | null;
  modified_by: string | null;
  modified_on: Generated<Timestamp>;
  storage: string;
  tags: string | null;
  title: string | null;
  tus_data: Json | null;
  tus_id: string | null;
  type: string | null;
  uploaded_by: string | null;
  uploaded_on: Timestamp | null;
  width: number | null;
}

export interface DirectusFlows {
  accountability: Generated<string | null>;
  color: string | null;
  date_created: Generated<Timestamp | null>;
  description: string | null;
  icon: string | null;
  id: string;
  name: string;
  operation: string | null;
  options: Json | null;
  status: Generated<string>;
  trigger: string | null;
  user_created: string | null;
}

export interface DirectusFolders {
  id: string;
  name: string;
  parent: string | null;
}

export interface DirectusMigrations {
  name: string;
  timestamp: Generated<Timestamp | null>;
  version: string;
}

export interface DirectusNotifications {
  collection: string | null;
  id: Generated<number>;
  item: string | null;
  message: string | null;
  recipient: string;
  sender: string | null;
  status: Generated<string | null>;
  subject: string;
  timestamp: Generated<Timestamp | null>;
}

export interface DirectusOperations {
  date_created: Generated<Timestamp | null>;
  flow: string;
  id: string;
  key: string;
  name: string | null;
  options: Json | null;
  position_x: number;
  position_y: number;
  reject: string | null;
  resolve: string | null;
  type: string;
  user_created: string | null;
}

export interface DirectusPanels {
  color: string | null;
  dashboard: string;
  date_created: Generated<Timestamp | null>;
  height: number;
  icon: Generated<string | null>;
  id: string;
  name: string | null;
  note: string | null;
  options: Json | null;
  position_x: number;
  position_y: number;
  show_header: Generated<boolean>;
  type: string;
  user_created: string | null;
  width: number;
}

export interface DirectusPermissions {
  action: string;
  collection: string;
  fields: string | null;
  id: Generated<number>;
  permissions: Json | null;
  policy: string;
  presets: Json | null;
  validation: Json | null;
}

export interface DirectusPolicies {
  admin_access: Generated<boolean>;
  app_access: Generated<boolean>;
  description: string | null;
  enforce_tfa: Generated<boolean>;
  icon: Generated<string>;
  id: string;
  ip_access: string | null;
  name: string;
}

export interface DirectusPresets {
  bookmark: string | null;
  collection: string | null;
  color: string | null;
  filter: Json | null;
  icon: Generated<string | null>;
  id: Generated<number>;
  layout: Generated<string | null>;
  layout_options: Json | null;
  layout_query: Json | null;
  refresh_interval: number | null;
  role: string | null;
  search: string | null;
  user: string | null;
}

export interface DirectusRelations {
  id: Generated<number>;
  junction_field: string | null;
  many_collection: string;
  many_field: string;
  one_allowed_collections: string | null;
  one_collection: string | null;
  one_collection_field: string | null;
  one_deselect_action: Generated<string>;
  one_field: string | null;
  sort_field: string | null;
}

export interface DirectusRevisions {
  activity: number;
  collection: string;
  data: Json | null;
  delta: Json | null;
  id: Generated<number>;
  item: string;
  parent: number | null;
  version: string | null;
}

export interface DirectusRoles {
  description: string | null;
  icon: Generated<string>;
  id: string;
  name: string;
  parent: string | null;
}

export interface DirectusSessions {
  expires: Timestamp;
  ip: string | null;
  next_token: string | null;
  origin: string | null;
  share: string | null;
  token: string;
  user: string | null;
  user_agent: string | null;
}

export interface DirectusSettings {
  accepted_terms: Generated<boolean | null>;
  auth_login_attempts: Generated<number | null>;
  auth_password_policy: string | null;
  basemaps: Json | null;
  command_palette_settings: Generated<Json | null>;
  custom_aspect_ratios: Json | null;
  custom_css: string | null;
  default_appearance: Generated<string>;
  default_language: Generated<string>;
  default_theme_dark: string | null;
  default_theme_light: string | null;
  id: Generated<number>;
  mapbox_key: string | null;
  module_bar: Json | null;
  project_color: Generated<string>;
  project_descriptor: string | null;
  project_id: string | null;
  project_logo: string | null;
  project_name: Generated<string>;
  project_url: string | null;
  public_background: string | null;
  public_favicon: string | null;
  public_foreground: string | null;
  public_note: string | null;
  public_registration: Generated<boolean>;
  public_registration_email_filter: Json | null;
  public_registration_role: string | null;
  public_registration_verify_email: Generated<boolean>;
  report_bug_url: string | null;
  report_error_url: string | null;
  report_feature_url: string | null;
  storage_asset_presets: Json | null;
  storage_asset_transform: Generated<string | null>;
  storage_default_folder: string | null;
  theme_dark_overrides: Json | null;
  theme_light_overrides: Json | null;
  visual_editor_urls: Json | null;
}

export interface DirectusShares {
  collection: string;
  date_created: Generated<Timestamp | null>;
  date_end: Timestamp | null;
  date_start: Timestamp | null;
  id: string;
  item: string;
  max_uses: number | null;
  name: string | null;
  password: string | null;
  role: string | null;
  times_used: Generated<number | null>;
  user_created: string | null;
}

export interface DirectusTranslations {
  id: string;
  key: string;
  language: string;
  value: string;
}

export interface DirectusUsers {
  address: string | null;
  appearance: string | null;
  auth_data: Json | null;
  avatar: string | null;
  birthday: Timestamp | null;
  city: string | null;
  country: string | null;
  currency_preference: Generated<string | null>;
  default_address: number | null;
  description: string | null;
  email: string | null;
  email_notifications: Generated<boolean | null>;
  external_identifier: string | null;
  first_name: string | null;
  id: string;
  language: Generated<string | null>;
  language_preference: Generated<string | null>;
  last_access: Timestamp | null;
  last_name: string | null;
  last_page: string | null;
  location: string | null;
  nickname: string | null;
  password: string | null;
  password_migrated: Generated<boolean | null>;
  postcode: string | null;
  provider: Generated<string>;
  province: string | null;
  role: string | null;
  status: Generated<string>;
  stripe_customer_id: string | null;
  tags: Json | null;
  tfa_secret: string | null;
  theme_dark: string | null;
  theme_dark_overrides: Json | null;
  theme_light: string | null;
  theme_light_overrides: Json | null;
  title: string | null;
  token: string | null;
  verification_code: string | null;
}

export interface DirectusVersions {
  collection: string;
  date_created: Generated<Timestamp | null>;
  date_updated: Generated<Timestamp | null>;
  delta: Json | null;
  hash: string | null;
  id: string;
  item: string;
  key: string;
  name: string | null;
  user_created: string | null;
  user_updated: string | null;
}

export interface DirectusWebhooks {
  actions: string;
  collections: string;
  data: Generated<boolean>;
  headers: Json | null;
  id: Generated<number>;
  method: Generated<string>;
  migrated_flow: string | null;
  name: string;
  status: Generated<string>;
  url: string;
  was_active_before_deprecation: Generated<boolean>;
}

export interface Ip {
  category: number;
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  id: Generated<number>;
  is_featured: Generated<boolean | null>;
  keywords: string | null;
  main_image: string | null;
  old_id: string | null;
  owner: number | null;
  sort: number | null;
  status: Generated<string>;
  user_created: string | null;
  user_updated: string | null;
}

export interface IpCategories {
  code: Generated<string>;
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  id: Generated<number>;
  sort: number | null;
  status: Generated<string>;
  user_created: string | null;
  user_updated: string | null;
}

export interface IpCategoriesTranslations {
  id: Generated<number>;
  ip_categories_id: number | null;
  languages_id: number | null;
  name: Generated<string | null>;
}

export interface IpFiles {
  directus_files_id: string | null;
  id: Generated<number>;
  ip_id: number | null;
}

export interface IpOwner {
  business_license: string | null;
  company_name: Generated<string | null>;
  company_phone_number: string | null;
  company_role: string | null;
  country: string | null;
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  homepage_link: string | null;
  id: Generated<number>;
  image: string | null;
  name: string | null;
  status: Generated<string>;
  user: string | null;
  user_created: string | null;
  user_updated: string | null;
}

export interface IpTranslations {
  description: string | null;
  id: Generated<number>;
  ip_id: number | null;
  languages_id: number | null;
  name: Generated<string | null>;
  refresh: Generated<boolean | null>;
}

export interface JunctionDirectusUsersProducts {
  directus_users_id: string | null;
  id: Generated<number>;
  products_id: number | null;
}

export interface Languages {
  code: Generated<string>;
  direction: Generated<string>;
  id: Generated<number>;
  name: Generated<string>;
}

export interface Messages {
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  emoji: Generated<string | null>;
  id: Generated<number>;
  order: number | null;
  status: Generated<string>;
  text: Generated<string | null>;
  user_created: string | null;
  user_updated: string | null;
}

export interface OrderItem {
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  id: Generated<number>;
  message: string | null;
  order: number | null;
  price_at_order: Generated<Numeric | null>;
  price_jpy_at_order: Generated<Numeric | null>;
  price_krw_at_order: Generated<Numeric | null>;
  product: number | null;
  product_name_at_order: string | null;
  quantity: number | null;
  status: Generated<string>;
  user_created: string | null;
  user_updated: string | null;
}

export interface Orders {
  base_tax_amount: number | null;
  base_total_amount: number | null;
  base_total_amount_after_discount: number | null;
  creator: number | null;
  currency_code: Generated<string | null>;
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  id: Generated<number>;
  payment_id: Generated<string | null>;
  shipping_address_line_1: string | null;
  shipping_address_line_2: string | null;
  shipping_city: string | null;
  shipping_country: string | null;
  shipping_name: string | null;
  shipping_phone_number: string | null;
  shipping_postcode: string | null;
  shipping_province: Generated<string | null>;
  status: Generated<string>;
  total_amount: number | null;
  total_amount_after_discount: number | null;
  user: string | null;
  user_created: string | null;
  user_updated: string | null;
}

export interface ProductApplicationCategory {
  code: Generated<string | null>;
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  id: Generated<number>;
  name: Generated<string | null>;
  status: Generated<string>;
  user_created: string | null;
  user_updated: string | null;
}

export interface ProductCategories {
  code: Generated<string | null>;
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  id: Generated<number>;
  sort: number | null;
  status: Generated<string>;
  user_created: string | null;
  user_updated: string | null;
}

export interface ProductCategoriesTranslations {
  id: Generated<number>;
  languages_id: number | null;
  name: Generated<string | null>;
  product_categories_id: number | null;
}

export interface Products {
  approval_comment: string | null;
  base_currency: Generated<string | null>;
  category: number | null;
  creator: number | null;
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  discount: Generated<Numeric | null>;
  discount_percentage: Generated<Numeric | null>;
  id: Generated<number>;
  ip: number | null;
  is_new: Generated<boolean | null>;
  keywords: string | null;
  main_image: string | null;
  old_id: string | null;
  price: Generated<Numeric | null>;
  price_jpy: Generated<Numeric | null>;
  price_krw: Generated<Numeric | null>;
  product_application_category: number | null;
  revenue_total: number | null;
  sort: number | null;
  status: Generated<string>;
  stock_remaining: number | null;
  stock_total: number | null;
  units_sold: number | null;
  user_created: string | null;
  user_updated: string | null;
}

export interface ProductsFiles {
  directus_files_id: string | null;
  id: Generated<number>;
  products_id: number | null;
}

export interface ProductsTranslations {
  description: string | null;
  id: Generated<number>;
  languages_id: number | null;
  name: Generated<string | null>;
  products_id: number | null;
  test: boolean | null;
}

export interface Review {
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  dislike_count: number | null;
  id: Generated<number>;
  like_count: number | null;
  order: number | null;
  product: number | null;
  rating: Numeric | null;
  status: Generated<string>;
  text: string | null;
  user_created: string | null;
  user_updated: string | null;
}

export interface WebsiteSettings {
  bypass_key: string | null;
  date_created: Timestamp | null;
  date_updated: Timestamp | null;
  exchange_rates: Json | null;
  id: Generated<number>;
  is_fan_maintenance: Generated<boolean | null>;
  jpy_exchange_rate: number | null;
  krw_exchange_rate: number | null;
  open_exchange_rate_app_id: string | null;
  public_creator_domain: Generated<string | null>;
  public_fan_domain: string | null;
  public_owner_domain: Generated<string | null>;
  usd_exchange_rate: number | null;
  user_created: string | null;
  user_updated: string | null;
}

export interface DB {
  about_page: AboutPage;
  about_page_translations: AboutPageTranslations;
  addresses: Addresses;
  cart: Cart;
  companies: Companies;
  creator: Creator;
  directus_access: DirectusAccess;
  directus_activity: DirectusActivity;
  directus_collections: DirectusCollections;
  directus_comments: DirectusComments;
  directus_dashboards: DirectusDashboards;
  directus_extensions: DirectusExtensions;
  directus_fields: DirectusFields;
  directus_files: DirectusFiles;
  directus_flows: DirectusFlows;
  directus_folders: DirectusFolders;
  directus_migrations: DirectusMigrations;
  directus_notifications: DirectusNotifications;
  directus_operations: DirectusOperations;
  directus_panels: DirectusPanels;
  directus_permissions: DirectusPermissions;
  directus_policies: DirectusPolicies;
  directus_presets: DirectusPresets;
  directus_relations: DirectusRelations;
  directus_revisions: DirectusRevisions;
  directus_roles: DirectusRoles;
  directus_sessions: DirectusSessions;
  directus_settings: DirectusSettings;
  directus_shares: DirectusShares;
  directus_translations: DirectusTranslations;
  directus_users: DirectusUsers;
  directus_versions: DirectusVersions;
  directus_webhooks: DirectusWebhooks;
  ip: Ip;
  ip_categories: IpCategories;
  ip_categories_translations: IpCategoriesTranslations;
  ip_files: IpFiles;
  ip_owner: IpOwner;
  ip_translations: IpTranslations;
  junction_directus_users_products: JunctionDirectusUsersProducts;
  languages: Languages;
  messages: Messages;
  order_item: OrderItem;
  orders: Orders;
  product_application_category: ProductApplicationCategory;
  product_categories: ProductCategories;
  product_categories_translations: ProductCategoriesTranslations;
  products: Products;
  products_files: ProductsFiles;
  products_translations: ProductsTranslations;
  review: Review;
  website_settings: WebsiteSettings;
}
