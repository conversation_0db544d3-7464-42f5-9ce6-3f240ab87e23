module.exports = async function (data) {
    // Extract rates from the array (it's an array with one object)
    const ratesData = data.get_rates[0];

    // Check for product base currency - normalize to uppercase
    const base_currency = data.$trigger.base_currency?.toUpperCase();
    const product_id = data.$trigger.id;

    // Get the base price from the product based on its base currency
    let base_price;

    // Handle null/undefined base_currency
    if (!base_currency) {
        // Default to USD if no base currency is specified
        base_price = parseFloat(data.$trigger.price);
    } else {
        switch (base_currency) {
            case 'USD':
                base_price = parseFloat(data.$trigger.price);
                break;
            case 'JPY':
                base_price = parseFloat(data.$trigger.price_jpy);
                break;
            case 'KRW':
                base_price = parseFloat(data.$trigger.price_krw);
                break;
            default:
                base_price = parseFloat(data.$trigger.price); // fallback to USD
        }
    }

    // Skip if no base price is set
    if (isNaN(base_price) || base_price <= 0) {
        return { error: "No valid base price found" };
    }

    // Calculate converted prices for non-base currencies (only update price fields, not base_currency)
    const updates = {};

    // Convert to USD if not base currency
    if (base_currency !== 'USD') {
        if (base_currency === 'JPY') {
            // Round up to 2 decimal places for USD
            updates.price = Math.ceil((base_price / ratesData.jpy_exchange_rate) * 100) / 100;
        } else if (base_currency === 'KRW') {
            // Round up to 2 decimal places for USD
            updates.price = Math.ceil((base_price / ratesData.krw_exchange_rate) * 100) / 100;
        }
    }

    // Convert to JPY if not base currency
    if (base_currency !== 'JPY') {
        if (base_currency === 'USD') {
            // Round up for JPY (whole number)
            updates.price_jpy = Math.ceil(base_price * ratesData.jpy_exchange_rate);
        } else if (base_currency === 'KRW') {
            // Convert KRW -> USD -> JPY
            const usd_price = base_price / ratesData.krw_exchange_rate;
            updates.price_jpy = Math.ceil(usd_price * ratesData.jpy_exchange_rate);
        }
    }

    // Convert to KRW if not base currency
    if (base_currency !== 'KRW') {
        if (base_currency === 'USD') {
            // Round up for KRW (whole number)
            updates.price_krw = Math.ceil(base_price * ratesData.krw_exchange_rate);
        } else if (base_currency === 'JPY') {
            // Convert JPY -> USD -> KRW
            const usd_price = base_price / ratesData.jpy_exchange_rate;
            updates.price_krw = Math.ceil(usd_price * ratesData.krw_exchange_rate);
        }
    }

    // Set the normalized case in data as well
    updates.base_currency = base_currency;

    // Return the calculated updates
    return {
        product_id,
        updates
    };
}