import { createDirectus, authentication, rest, schemaSnapshot, schemaDiff, schemaApply } from '@directus/sdk';
const BASE_DIRECTUS_URL = 'https://ipgo-directus.zafar.dev';
// const BASE_ACCESS_TOKEN = 'UTxtNL0u5zSgHn8MITisi9p8YfmDO4Dr';

const TARGET_DIRECTUS_URL = 'https://stg-admin.ipgo.space';
// const TARGET_ACCESS_TOKEN = 'JL-JSge6b4TPIo4ah3gNHLvRoyiGNral';

const baseDirectus = createDirectus(BASE_DIRECTUS_URL).with(rest()).with(authentication());;
const targetDirectus = createDirectus(TARGET_DIRECTUS_URL).with(rest()).with(authentication());;

await baseDirectus.login('<EMAIL>', 'DemoDemo1');
await targetDirectus.login('<EMAIL>', 'DemoDemo1');

async function main() {
  const snapshot = await getSnapshot();
  console.log(snapshot);
  const diff = await getDiff(snapshot);
  console.log(diff);
  await applyDiff(diff);
}

main();

function getSnapshot() {
  return baseDirectus.request(schemaSnapshot());
}

function getDiff(snapshot) {
  return targetDirectus.request(schemaDiff(snapshot, true));
}

function applyDiff(diff) {
  return targetDirectus.request(schemaApply(diff));
}