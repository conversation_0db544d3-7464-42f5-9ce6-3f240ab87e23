import { Kysely, <PERSON>gresDialect, MysqlDialect } from 'kysely';
import { Pool } from 'pg';
import { createPool } from 'mysql2';
import type { DB } from './target-db';
import type { DB as SourceDB } from './source-db';
import { createDirectus, authentication, rest, importFile, updateItem } from '@directus/sdk';
import { config } from 'dotenv';
import Bottleneck from 'bottleneck';

// Load environment variables
config();

// Configuration with environment variable validation
interface MigrationConfig {
  targetDirectusUrl: string;
  directusEmail: string;
  directusPassword: string;
  ipImagesFolderId: string;
  defaultCategoryId: number;
  geminiApiKey: string;
  databaseUrl: string;
  sourceDatabaseUrl: string;
}

function validateAndGetConfig(): MigrationConfig {
  const requiredEnvVars = [
    'DATABASE_URL',
    'SOURCE_DATABASE_URL',
    'DIRECTUS_URL',
    'DIRECTUS_EMAIL',
    'DIRECTUS_PASSWORD',
    'IP_IMAGES_FOLDER_ID',
    'GEMINI_API_KEY'
  ];

  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  return {
    targetDirectusUrl: process.env.DIRECTUS_URL!,
    directusEmail: process.env.DIRECTUS_EMAIL!,
    directusPassword: process.env.DIRECTUS_PASSWORD!,
    ipImagesFolderId: process.env.IP_IMAGES_FOLDER_ID!,
    defaultCategoryId: parseInt(process.env.DEFAULT_CATEGORY_ID || '21'),
    geminiApiKey: process.env.GEMINI_API_KEY!,
    databaseUrl: process.env.DATABASE_URL!,
    sourceDatabaseUrl: process.env.SOURCE_DATABASE_URL!
  };
}

// Validate configuration
const config_vars = validateAndGetConfig();

const targetDirectus = createDirectus(config_vars.targetDirectusUrl).with(rest()).with(authentication());

// Rate limiter: <15 requests per minute to respect API limits
const rateLimiter = new Bottleneck({
  minTime: 100, // 3.5 seconds between requests (<15 per minute)
  maxConcurrent: 10 // Process one at a time
});

// Login to Directus
try {
  await targetDirectus.login(config_vars.directusEmail, config_vars.directusPassword);
  console.log('✅ Successfully connected to Directus');
} catch (error) {
  console.error('❌ Failed to connect to Directus:', error);
  process.exit(1);
}

interface IP {
  ipId: string;
  ipName1: string;
  ipName2: string;
  ipName3: string;
  ipName4: string;
  ipName5: string;
  ipDesc1: string;
  ipDesc2: string;
  ipDesc3: string;
  ipDesc4: string;
  ipDesc5: string;
  ipStatus: string;
  ipCatCode: string;
  ipCatName1: string;
  ipCatName2: string;
  ipCatName3: string;
  ipCatName4: string;
  ipCatName5: string;
  ipImgId: string[];
  status: 'published' | 'unpublished';
  createdDate: string;
  path: string[];
  fileNames: string[];
  owner: {
    id: string;
    username: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
    nickname: string | null;
    compName: string;
  };
}

// Target database (PostgreSQL - Directus)
export const db = new Kysely<DB>({
  dialect: new PostgresDialect({
    pool: new Pool({
      connectionString: config_vars.databaseUrl,
      ssl: {
        rejectUnauthorized: false,
        checkServerIdentity: () => undefined
      }
    }),
  }),
});

// Source database (MySQL - Legacy system)
export const sourceDb = new Kysely<SourceDB>({
  dialect: new MysqlDialect({
    pool: createPool({
      uri: config_vars.sourceDatabaseUrl,
    }),
  }),
});

// Function to fetch IPs from source database
async function fetchIPsFromDatabase(): Promise<IP[]> {
  console.log('🔍 Fetching IPs from source database...');

  try {
    // Test source database connection
    await sourceDb.selectFrom('t_ip').select('id').limit(1).execute();
    console.log('✅ Source database connection successful');
  } catch (error) {
    throw new Error(`Failed to connect to source database: ${error}`);
  }

  // First, get all IPs with their basic info and owner details
  const baseIPs = await sourceDb
    .selectFrom('t_ip as ip')
    .leftJoin('t_user as u', 'ip.user_id', 'u.id')
    .leftJoin('t_user_role as ur', 'u.id', 'ur.user_id')
    .leftJoin('t_role as r', 'ur.role_id', 'r.id')
    .leftJoin('t_ip_ip_cat as ipcat', 'ip.id', 'ipcat.ip_id')
    .leftJoin('t_ip_cat as cat', 'ipcat.ip_cat_id', 'cat.id')
    .select([
      // IP fields
      'ip.id as ipId',
      'ip.ip_name_1 as ipName1',
      'ip.ip_name_2 as ipName2',
      'ip.ip_name_3 as ipName3',
      'ip.ip_name_4 as ipName4',
      'ip.ip_name_5 as ipName5',
      'ip.ip_desc_1 as ipDesc1',
      'ip.ip_desc_2 as ipDesc2',
      'ip.ip_desc_3 as ipDesc3',
      'ip.ip_desc_4 as ipDesc4',
      'ip.ip_desc_5 as ipDesc5',
      'ip.ip_status as ipStatus',
      'ip.created_dt as createdDate',

      // Category fields
      'cat.ip_cat_code as ipCatCode',
      'cat.ip_cat_name_1 as ipCatName1',
      'cat.ip_cat_name_2 as ipCatName2',
      'cat.ip_cat_name_3 as ipCatName3',
      'cat.ip_cat_name_4 as ipCatName4',
      'cat.ip_cat_name_5 as ipCatName5',

      // Owner fields
      'u.id as ownerId',
      'u.username as ownerUsername',
      'u.email as ownerEmail',
      'u.first_name as ownerFirstName',
      'u.last_name as ownerLastName',
      'u.nickname as ownerNickname',
      'u.comp_name as ownerCompName',
    ])
    // Get all IPs regardless of status
    .where('r.role_code', '=', 'ROLE_OWNER') // Only get IPs from owners
    .execute();

  console.log(`📊 Found ${baseIPs.length} IPs from owners`);

  if (baseIPs.length === 0) {
    console.warn('⚠️ No IPs found in source database');
    return [];
  }

  // Now fetch images for each IP
  const ipsWithImages: IP[] = [];
  let processedCount = 0;
  
  for (const baseIP of baseIPs) {
    processedCount++;
    console.log(`🔄 Processing IP ${processedCount}/${baseIPs.length}: ${baseIP.ipId}`);

    // Validate required fields
    if (!baseIP.ipId || !baseIP.ownerId) {
      console.warn(`⚠️ Skipping IP with missing required data: ${baseIP.ipId}`);
      continue;
    }
    // Get images for this IP
    let images: any[] = [];
    try {
      images = await sourceDb
        .selectFrom('t_ip_img as img')
        .leftJoin('t_file as f', 'img.file_id', 'f.id')
        .select([
          'img.file_id as id',
          'f.path as path',
          'f.filename as filename'
        ])
        .where('img.ip_id', '=', baseIP.ipId)
        .execute();
    } catch (error) {
      console.warn(`⚠️ Failed to fetch images for IP ${baseIP.ipId}:`, error);
      // Continue with empty images array
    }

    // Build the IP object with proper structure
    const ip: IP = {
      ipId: baseIP.ipId,
      ipName1: baseIP.ipName1 || '',
      ipName2: baseIP.ipName2 || '',
      ipName3: baseIP.ipName3 || '',
      ipName4: baseIP.ipName4 || '',
      ipName5: baseIP.ipName5 || '',
      ipDesc1: baseIP.ipDesc1 || '',
      ipDesc2: baseIP.ipDesc2 || '',
      ipDesc3: baseIP.ipDesc3 || '',
      ipDesc4: baseIP.ipDesc4 || '',
      ipDesc5: baseIP.ipDesc5 || '',
      ipStatus: baseIP.ipStatus || '',
      ipCatCode: baseIP.ipCatCode || '',
      ipCatName1: baseIP.ipCatName1 || '',
      ipCatName2: baseIP.ipCatName2 || '',
      ipCatName3: baseIP.ipCatName3 || '',
      ipCatName4: baseIP.ipCatName4 || '',
      ipCatName5: baseIP.ipCatName5 || '',
      status: baseIP.ipStatus === 'A'? 'published' : 'unpublished',
      createdDate: baseIP.createdDate?.toISOString() || new Date().toISOString(),

      // Image arrays
      ipImgId: images.map(img => img.id || '').filter(Boolean),
      path: images.map(img => img.path || '').filter(Boolean),
      fileNames: images.map(img => img.filename || '').filter(Boolean),

      // Owner object
      owner: {
        id: baseIP.ownerId || '',
        username: baseIP.ownerUsername || '',
        email: baseIP.ownerEmail || '',
        firstName: baseIP.ownerFirstName,
        lastName: baseIP.ownerLastName,
        nickname: baseIP.ownerNickname,
        compName: baseIP.ownerCompName || '',
      }
    };

    // Final validation before adding to results
    if (ip.ipId && ip.owner.id) {
      ipsWithImages.push(ip);
    } else {
      console.warn(`⚠️ Skipping IP with incomplete data: ${baseIP.ipId}`);
    }
  }

  console.log(`✅ Successfully fetched ${ipsWithImages.length} IPs with image data`);

  // Final validation
  if (ipsWithImages.length === 0) {
    throw new Error('No valid IPs found in source database');
  }

  return ipsWithImages;
}

async function main() {
  try {
    console.log('🚀 Starting IP migration with direct database access...');

    // Fetch IPs directly from source database instead of JSON file
    const ips: IP[] = await fetchIPsFromDatabase();

    if (ips.length === 0) {
      console.warn('⚠️ No IPs to process, exiting...');
      return;
    }

    console.log(`📋 Processing ${ips.length} IPs...`);
    const ipsToProcess = ips;

    // Migration summary tracking
    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    // Process IPs with rate limiting
    for (const ip of ipsToProcess) {
      await rateLimiter.schedule(async () => {
        if (!ip) {
          console.log('IP is undefined');
          skipCount++;
          return;
        }

        // Basic validation
        if (!ip.ipId || !ip.owner?.email) {
          console.warn(`⚠️ Skipping IP with missing required data: ipId=${ip.ipId}, ownerEmail=${ip.owner?.email}`);
          skipCount++;
          return;
        }

        if (!ip.ipName1 && !ip.ipName2 && !ip.ipName3) {
          console.warn(`⚠️ Skipping IP with no name in any language: ${ip.ipId}`);
          skipCount++;
          return;
        }

        // Check if IP already exists to prevent duplicates
        const existingIp = await db
          .selectFrom('ip')
          .select('id')
          .where('old_id', '=', ip.ipId)
          .executeTakeFirst();

        if (existingIp) {
          console.log(`⚠️ IP already exists with old_id: ${ip.ipId}, skipping...`);
          skipCount++;
          return;
        }

        console.log(`🔄 Processing IP: ${ip.ipId} - ${ip.ipName1 || ip.ipName2 || ip.ipName3}`);

        try {

      // merge ipImgId and fileNames(this is the actual file name for the images, array order is important)
      const images = ip.ipImgId.map((imgId, index) => {
        return {
          id: imgId,
          filename_download: ip.fileNames[index],
          url: ip.path[index],
        };
      });

      let uploadedImageIds: string[] = [];
      for (const image of images) {
        if (!image.url) {
          console.log('Image is undefined');
          continue;
        }
        try {
          // Check if file already exists in Directus by filename (if filename exists)
          let existingFile = null;
          if (image.filename_download) {
            existingFile = await db
              .selectFrom('directus_files')
              .select('id')
              .where('filename_download', '=', image.filename_download)
              .where('folder', '=', config_vars.ipImagesFolderId)
              .executeTakeFirst();
          }

          if (existingFile) {
            console.log(`📁 File already exists in Directus: ${image.filename_download}, using existing file ${existingFile.id}`);
            uploadedImageIds.push(existingFile.id);
          } else {
            console.log('inserting image', image.url);
            // Import file to Directus using image URL with IP-specific folder
            const fileRes = await targetDirectus.request(
              importFile(image.url, { folder: config_vars.ipImagesFolderId }),
            );
            console.log(`image uploaded ${fileRes.id}`);
            uploadedImageIds.push(fileRes.id);
          }
        } catch (e) {
          console.error('Failed to upload image:', e);
        }
      }

      // Get the IP Category ID
      const ipCategoryIdResult = await db
        .selectFrom('ip_categories')
        .where('code', '=', ip.ipCatCode)
        .select('id')
        .executeTakeFirst();
      const ipCategoryId = ipCategoryIdResult?.id;
      console.log('ipCategoryId got', ipCategoryId);

      // Create IP
      // 1. Get the IP Owner ID by looking up the user by email first
      const directusUserResult = await db
        .selectFrom('directus_users')
        .where('email', '=', ip.owner.email)
        .select('id')
        .executeTakeFirst();

      if (!directusUserResult) {
        console.error(`❌ Directus user not found for email: ${ip.owner.email}, skipping IP: ${ip.ipId}`);
        skipCount++;
        return;
      }

      const ipOwnerIdResult = await db
        .selectFrom('ip_owner')
        .where('user', '=', directusUserResult.id)
        .select('id')
        .executeTakeFirst();
      const ipOwnerId = ipOwnerIdResult?.id;
      console.log('ipOwnerId got', ipOwnerId);

      if (!ipOwnerId) {
        console.error(`❌ IP Owner not found for user: ${directusUserResult.id} (email: ${ip.owner.email}), skipping IP: ${ip.ipId}`);
        skipCount++;
        return;
      }

      // 2. Create keywords from all translated names and descriptions
      const keywords: string[] = [];

      // Add all names (filter out empty ones)
      [ip.ipName1, ip.ipName2, ip.ipName3, ip.ipName4, ip.ipName5]
        .filter(name => name && name.trim())
        .forEach(name => keywords.push(name.trim()));

      // Add all descriptions (filter out empty ones)
      [ip.ipDesc1, ip.ipDesc2, ip.ipDesc3, ip.ipDesc4, ip.ipDesc5]
        .filter(desc => desc && desc.trim())
        .forEach(desc => keywords.push(desc.trim()));

      // Join all keywords with commas
      const keywordsString = keywords.join(', ');

      // 3. Create the IP with safe array access
      const mainImageId = uploadedImageIds.length > 0 ? uploadedImageIds[0] : null;
      if (!mainImageId) {
        console.warn(`⚠️ No images uploaded for IP: ${ip.ipId}, proceeding without main image`);
      }

      const ipInsertRes = await db
        .insertInto('ip')
        .values({
          status: 'published',
          main_image: mainImageId,
          category: ipCategoryId ?? config_vars.defaultCategoryId,
          user_created: directusUserResult.id,
          owner: ipOwnerId,
          date_created: ip.createdDate,
          old_id: ip.ipId,
          keywords: keywordsString,
        })
        .returning('id')
        .executeTakeFirst();
      console.log(ipInsertRes);
      const ipId = ipInsertRes?.id;
      console.log('ipId created', ipId);

      if (!ipId) {
        console.error(`❌ Failed to create IP: ${ip.ipId}, skipping...`);
        errorCount++;
        errors.push(`Failed to create IP: ${ip.ipId}`);
        return;
      }

      // At this point ipId is guaranteed to be a number
      const confirmedIpId: number = ipId;

      // update ip files with duplicate protection
      if (uploadedImageIds.length > 0) {
        // Check existing files for this IP
        const existingFiles = await db
          .selectFrom('ip_files')
          .select('directus_files_id')
          .where('ip_id', '=', confirmedIpId)
          .execute();

        const existingFileIds = new Set(existingFiles.map(f => f.directus_files_id));

        const payloadIpFiles: { ip_id: number; directus_files_id: string }[] = [];
        uploadedImageIds.forEach((fileId) => {
          if (!existingFileIds.has(fileId)) {
            payloadIpFiles.push({
              ip_id: confirmedIpId,
              directus_files_id: fileId,
            });
          }
        });

        console.log('payloadIpFiles', payloadIpFiles);

        if (payloadIpFiles.length > 0) {
          try {
            await db.insertInto('ip_files').values(payloadIpFiles).execute();
            console.log(`✅ Inserted ${payloadIpFiles.length} file records for IP: ${ip.ipId}`);
          } catch (e) {
            console.error('Failed to insert ip files', e);
          }
        } else {
          console.log(`ℹ️ All files already exist for IP: ${ip.ipId}`);
        }
      }

    // update ip translations
    // Check if we need to generate translations using Gemini API
    // Language ID mappings: 1 = English, 2 = Japanese, 3 = Korean
    const hasEnglishContent = ip.ipName1 && ip.ipDesc1;
    const hasJapaneseContent = ip.ipName2 && ip.ipDesc2;
    const hasKoreanContent = ip.ipName3 && ip.ipDesc3;

    console.log('Content availability:', { hasEnglishContent, hasJapaneseContent, hasKoreanContent });

    // Generate missing translations if needed
    if (hasEnglishContent && (!hasJapaneseContent || !hasKoreanContent)) {
      console.log('Generating missing translations from English using Gemini API...');
      try {
        if (!hasJapaneseContent) {
          console.log('Translating English to Japanese...');
          const japaneseTranslation = await translateWithGemini(ip.ipName1, ip.ipDesc1, 'English', 'Japanese');
          ip.ipName2 = japaneseTranslation.name;
          ip.ipDesc2 = japaneseTranslation.description;
          console.log('Generated Japanese translation');
        }

        if (!hasKoreanContent) {
          console.log('Translating English to Korean...');
          const koreanTranslation = await translateWithGemini(ip.ipName1, ip.ipDesc1, 'English', 'Korean');
          ip.ipName3 = koreanTranslation.name;
          ip.ipDesc3 = koreanTranslation.description;
          console.log('Generated Korean translation');
        }
      } catch (error) {
        console.error('Error generating translations:', error);
      }
    } else if (hasKoreanContent && (!hasEnglishContent || !hasJapaneseContent)) {
      console.log('Generating missing translations from Korean using Gemini API...');
      try {
        if (!hasEnglishContent) {
          console.log('Translating Korean to English...');
          const englishTranslation = await translateWithGemini(ip.ipName3, ip.ipDesc3, 'Korean', 'English');
          ip.ipName1 = englishTranslation.name;
          ip.ipDesc1 = englishTranslation.description;
          console.log('Generated English translation');
        }

        if (!hasJapaneseContent) {
          console.log('Translating Korean to Japanese...');
          const japaneseTranslation = await translateWithGemini(ip.ipName3, ip.ipDesc3, 'Korean', 'Japanese');
          ip.ipName2 = japaneseTranslation.name;
          ip.ipDesc2 = japaneseTranslation.description;
          console.log('Generated Japanese translation');
        }
      } catch (error) {
        console.error('Error generating translations:', error);
      }
    } else if (hasJapaneseContent && (!hasEnglishContent || !hasKoreanContent)) {
      console.log('Generating missing translations from Japanese using Gemini API...');
      try {
        if (!hasEnglishContent) {
          console.log('Translating Japanese to English...');
          const englishTranslation = await translateWithGemini(ip.ipName2, ip.ipDesc2, 'Japanese', 'English');
          ip.ipName1 = englishTranslation.name;
          ip.ipDesc1 = englishTranslation.description;
          console.log('Generated English translation');
        }

        if (!hasKoreanContent) {
          console.log('Translating Japanese to Korean...');
          const koreanTranslation = await translateWithGemini(ip.ipName2, ip.ipDesc2, 'Japanese', 'Korean');
          ip.ipName3 = koreanTranslation.name;
          ip.ipDesc3 = koreanTranslation.description;
          console.log('Generated Korean translation');
        }
      } catch (error) {
        console.error('Error generating translations:', error);
      }
    }

      // Language ID mappings for database: 1 = English, 2 = Japanese, 3 = Korean
      // Check existing translations to prevent duplicates
      const existingTranslations = await db
        .selectFrom('ip_translations')
        .select(['languages_id'])
        .where('ip_id', '=', confirmedIpId)
        .execute();

      const existingLanguageIds = new Set(existingTranslations.map(t => t.languages_id));

      const payloadIpTranslations: { ip_id: number; languages_id: number; name: string; description: string }[] = [];

      // Only add translations that don't exist and have content
      if (!existingLanguageIds.has(1) && ip.ipName1 && ip.ipDesc1) {
        payloadIpTranslations.push({
          ip_id: confirmedIpId,
          languages_id: 1, // English
          name: ip.ipName1,
          description: ip.ipDesc1,
        });
      }

      if (!existingLanguageIds.has(2) && ip.ipName2 && ip.ipDesc2) {
        payloadIpTranslations.push({
          ip_id: confirmedIpId,
          languages_id: 2, // Japanese
          name: ip.ipName2,
          description: ip.ipDesc2,
        });
      }

      if (!existingLanguageIds.has(3) && ip.ipName3 && ip.ipDesc3) {
        payloadIpTranslations.push({
          ip_id: confirmedIpId,
          languages_id: 3, // Korean
          name: ip.ipName3,
          description: ip.ipDesc3,
        });
      }

      console.log('payloadIpTranslations', payloadIpTranslations);

      if (payloadIpTranslations.length > 0) {
        try {
          const translationIds: any = await db
            .insertInto('ip_translations')
            .values(payloadIpTranslations)
            .returning('id')
            .execute();

          console.log(`✅ Inserted ${translationIds.length} translations for IP: ${ip.ipId}`);

          // Trigger a translation update via directus sdk as well
          if (translationIds.length > 0) {
            await targetDirectus.request(
              updateItem('ip_translations', translationIds[0].id, {
                refresh: true,
              }),
            );
          }
        } catch (e) {
          console.error('Failed to insert ip translations', e);
        }
      } else {
        console.log(`ℹ️ All translations already exist for IP: ${ip.ipId}`);
      }

      // IP successfully processed
      successCount++;
      console.log(`✅ Successfully processed IP: ${ip.ipId}`);

    } catch (error) {
        errorCount++;
        const errorMsg = `Error processing IP ${ip.ipId}: ${error}`;
        console.error(`❌ ${errorMsg}`);
        errors.push(errorMsg);
        // Continue with next IP instead of exiting
      }
      }); // Close rate limiter schedule
    }

    // Print migration summary
    console.log('\n📊 Migration Summary:');
    console.log(`✅ Successfully processed: ${successCount} IPs`);
    console.log(`⚠️ Skipped: ${skipCount} IPs`);
    console.log(`❌ Errors: ${errorCount} IPs`);

    if (errors.length > 0) {
      console.log('\n❌ Error details:');
      errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    console.log('\n🎉 IP migration completed');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

/**
 * Translates text using the Gemini API
 * @param name The name to translate
 * @param description The description to translate
 * @param sourceLanguage The source language
 * @param targetLanguage The target language
 * @returns The translated name and description
 */
/**
 * Verifies if text is likely in the expected language
 * This is a simple check and not 100% accurate
 */
function verifyLanguage(text: string, targetLanguage: string): boolean {
  // Simple language verification based on character sets and common words
  if (targetLanguage === 'Korean') {
    // Check for Korean characters (Hangul)
    return /[\uAC00-\uD7AF\u1100-\u11FF\u3130-\u318F\uA960-\uA97F\uD7B0-\uD7FF]/.test(text);
  } else if (targetLanguage === 'Japanese') {
    // Check for Japanese characters (Hiragana, Katakana, and some Kanji)
    return /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF]/.test(text);
  } else if (targetLanguage === 'English') {
    // Check for primarily Latin characters and common English words
    return (
      /^[\p{Script=Latin}\d\s\p{P}]*$/u.test(text) &&
      !/[\uAC00-\uD7AF\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF]/.test(text)
    );
  }
  return true; // Default to true for unsupported language checks
}

// Rate limiting for Gemini API (15 calls per minute)
let lastApiCall = 0;
const API_RATE_LIMIT_MS = 0; // 4 seconds between calls

async function translateWithGemini(
  name: string,
  description: string,
  sourceLanguage: string,
  targetLanguage: string,
): Promise<{ name: string; description: string }> {
  // Rate limiting - wait if needed
  const now = Date.now();
  const timeSinceLastCall = now - lastApiCall;
  if (timeSinceLastCall < API_RATE_LIMIT_MS) {
    const waitTime = API_RATE_LIMIT_MS - timeSinceLastCall;
    console.log(`⏳ Rate limiting: waiting ${Math.round(waitTime / 1000)}s before next API call...`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }
  lastApiCall = Date.now();

  // Use validated API key from configuration
  const apiKey = config_vars.geminiApiKey;
  const apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

  const prompt = `You are a professional translator. Translate the following text from ${sourceLanguage} to ${targetLanguage}. Be accurate and maintain the original meaning and style.

Name: ${name}
Description: ${description}

You MUST respond ONLY in ${targetLanguage}. Your response MUST follow this EXACT format:
Name: [translated name in ${targetLanguage}]
Description: [translated description in ${targetLanguage}]`;

  try {
    const response = await fetch(`${apiUrl}?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt,
              },
            ],
          },
        ],
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('Gemini API error:', data);
      throw new Error(`Gemini API error: ${data.error?.message || 'Unknown error'}`);
    }

    const generatedText = data.candidates[0].content.parts[0].text;

    console.log('Translation response:', generatedText);

    // Extract the translated name and description from the response
    const nameMatch = generatedText.match(/Name:\s*(.+?)(?:\n|$)/);
    const descriptionMatch = generatedText.match(/Description:\s*([\s\S]+)$/);

    // Basic language verification
    const extractedName = nameMatch ? nameMatch[1].trim() : '';
    const extractedDesc = descriptionMatch ? descriptionMatch[1].trim() : '';

    // Verify language is correct based on target language
    const isCorrectLanguage = verifyLanguage(extractedName + ' ' + extractedDesc, targetLanguage);

    if (!isCorrectLanguage) {
      console.warn(`Warning: Translation may not be in ${targetLanguage}. Attempting one more time...`);
      // You could implement retry logic here if needed
    }

    return {
      name: extractedName,
      description: extractedDesc,
    };
  } catch (error) {
    console.error('Error calling Gemini API:', error);
    throw error;
  }
}

await main();
