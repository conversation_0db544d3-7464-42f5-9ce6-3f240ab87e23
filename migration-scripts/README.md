# IPGO Migration Scripts

This directory contains migration scripts for transferring data from the legacy IPGO system to the new Directus-based system.

## Setup

### 1. Install Dependencies

```bash
cd migration-scripts
npm install
# or
bun install
```

### 2. Environment Variables

Create a `.env` file in this directory with:

```env
# Target Database (Directus PostgreSQL)
DATABASE_URL=postgresql://username:password@host:port/database

# Source Database (Legacy MySQL)
SOURCE_DATABASE_URL=mysql://username:password@host:port/database

# Directus API
DIRECTUS_URL=https://your-directus-instance.com
DIRECTUS_EMAIL=<EMAIL>
DIRECTUS_PASSWORD=your-password

# Optional: For translation features
GEMINI_API_KEY=your-gemini-api-key
```

## Scripts

### Migration Scripts

```bash
# Migrate users from legacy system
npm run migrate:users

# Migrate IP properties
npm run migrate:ips

# Migrate products
npm run migrate:products
```

### Directus Schema Migration

```bash
# Migrate schema between Directus instances
npm run directus:migrate
```

### Database Code Generation

```bash
# Generate TypeScript types for source database
npm run codegen:source

# Generate TypeScript types for target database  
npm run codegen:target
```

## Files

### Migration Scripts
- `migrate-users.ts` - Migrates user accounts and profiles
- `migrate-ips.ts` - Migrates IP properties and metadata
- `migrate-products.ts` - Migrates products with translations
- `directus-migrator.js` - Syncs Directus schema between instances

### Database Types
- `source-db.ts` - TypeScript types for legacy MySQL database
- `target-db.ts` - TypeScript types for new PostgreSQL database

### Utilities
- `currency-conversion-function.js` - Directus flow function for currency conversion

### Data Files
- `ips.json` - Exported IP data
- `products.json` - Exported product data

## Usage Notes

1. **Run migrations in order**: Users → IPs → Products
2. **Check for existing data**: Scripts include duplicate checking
3. **Backup before running**: Always backup your target database
4. **Environment specific**: Update URLs and credentials for your environment

## Dependencies

- **@directus/sdk** - Directus API client
- **kysely** - Type-safe SQL query builder
- **mysql2** - MySQL database driver
- **pg** - PostgreSQL database driver
- **cross-fetch** - Fetch polyfill for Node.js

## Development

This is a standalone Node.js project separate from the main Nuxt application. It has its own dependencies and can be run independently.
