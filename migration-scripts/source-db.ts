/**
 * This file was generated by kysely-codegen.
 * Please do not edit it manually.
 */

import type { ColumnType } from "kysely";

export type Decimal = ColumnType<string, number | string>;

export type Generated<T> = T extends ColumnType<infer S, infer I, infer U>
  ? ColumnType<S, I | undefined, U>
  : ColumnType<T, T | undefined, T>;

export interface TApplicationType {
  app_type_code: string | null;
  app_type_name: string | null;
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  id: string;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TCart {
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  id: string;
  product_id: string | null;
  quantity: number | null;
  status: Generated<string | null>;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  user_id: string | null;
  version: Generated<number | null>;
}

export interface TChat {
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  id: string;
  message: string | null;
  order_details_id: string | null;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  user_id: string | null;
  version: Generated<number | null>;
}

export interface TChatReaction {
  chat_id: string | null;
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  emoji: string | null;
  id: string;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  user_id: string | null;
  version: Generated<number | null>;
}

export interface TFavourite {
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  id: string;
  product_id: string | null;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  user_id: string | null;
  version: Generated<number | null>;
}

export interface TFeedback {
  created_by: string | null;
  created_dt: Generated<Date>;
  feedback_desc: string | null;
  feedback_dt: Date | null;
  id: string;
  product_id: string | null;
  tenant_code: string | null;
  updated_by: string | null;
  updated_dt: Generated<Date | null>;
  user_id: string | null;
  version: Generated<number | null>;
}

export interface TFile {
  created_by: string | null;
  created_dt: Generated<Date>;
  file_cat: string | null;
  file_size: number | null;
  file_type: string | null;
  filename: string | null;
  id: string;
  is_temp: string | null;
  path: string | null;
  remarks: string | null;
  storage_type: string | null;
  tenant_code: string | null;
  updated_by: string | null;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TForex {
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  currency_code: string;
  exchange_rate: Decimal;
  id: string;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TIp {
  created_by: string | null;
  created_dt: Generated<Date>;
  id: string;
  ip_desc_1: string | null;
  ip_desc_2: string | null;
  ip_desc_3: string | null;
  ip_desc_4: string | null;
  ip_desc_5: string | null;
  ip_name_1: string | null;
  ip_name_2: string | null;
  ip_name_3: string | null;
  ip_name_4: string | null;
  ip_name_5: string | null;
  ip_status: string | null;
  tenant_code: string | null;
  updated_by: string | null;
  updated_dt: Generated<Date | null>;
  user_id: string | null;
  version: Generated<number | null>;
}

export interface TIpCat {
  created_by: string | null;
  created_dt: Generated<Date>;
  id: string;
  ip_cat_code: string | null;
  ip_cat_name_1: string | null;
  ip_cat_name_2: string | null;
  ip_cat_name_3: string | null;
  ip_cat_name_4: string | null;
  ip_cat_name_5: string | null;
  tenant_code: string | null;
  updated_by: string | null;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TIpImg {
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  file_id: string;
  ip_id: string;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TIpIpCat {
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  ip_cat_id: string;
  ip_id: string;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TOrder {
  base_tax_amount: Decimal | null;
  base_total_amount_after: Decimal | null;
  base_total_amount_before: Decimal | null;
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  creator_id: string | null;
  currency_code: string | null;
  fan_id: string | null;
  id: string;
  order_dt: Date | null;
  order_ref_no: string | null;
  order_status: string | null;
  tax_amount: Decimal | null;
  tenant_code: Generated<string | null>;
  total_amount_after: Decimal | null;
  total_amount_before: Decimal | null;
  total_discount: Decimal | null;
  total_quantity: number | null;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TOrderDetails {
  base_price_after: Decimal | null;
  base_price_before: Decimal | null;
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  currency_code: string | null;
  detail_status: string | null;
  discount: Decimal | null;
  id: string;
  item_cat_code: string | null;
  item_cat_name_1: string | null;
  item_cat_name_2: string | null;
  item_cat_name_3: string | null;
  item_cat_name_4: string | null;
  item_cat_name_5: string | null;
  item_desc_1: string | null;
  item_desc_2: string | null;
  item_desc_3: string | null;
  item_desc_4: string | null;
  item_desc_5: string | null;
  item_name_1: string | null;
  item_name_2: string | null;
  item_name_3: string | null;
  item_name_4: string | null;
  item_name_5: string | null;
  order_id: string | null;
  price_after: Decimal | null;
  price_before: Decimal | null;
  product_id: string | null;
  quantity: number | null;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TOrderDetailsImg {
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  file_id: string;
  order_details_id: string;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TPermission {
  created_by: string | null;
  created_dt: Generated<Date>;
  id: string;
  permission_code: string | null;
  permission_desc: string | null;
  tenant_code: string | null;
  updated_by: string | null;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TProdProdCat {
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  prod_cat_id: string;
  prod_id: string;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TProduct {
  application_type_id: string | null;
  applied_dt: Generated<Date>;
  base_currency: string | null;
  created_by: string | null;
  created_dt: Generated<Date>;
  id: string;
  ip_id: string | null;
  is_approve_reject: string | null;
  is_new: string | null;
  product_desc_1: string | null;
  product_desc_2: string | null;
  product_desc_3: string | null;
  product_desc_4: string | null;
  product_desc_5: string | null;
  product_discount: Generated<Decimal | null>;
  product_name_1: string | null;
  product_name_2: string | null;
  product_name_3: string | null;
  product_name_4: string | null;
  product_name_5: string | null;
  product_price: Decimal | null;
  product_price_jpy: Decimal | null;
  product_price_krw: Decimal | null;
  product_sale_status: string | null;
  product_status: string | null;
  rem_stock: number | null;
  tenant_code: string | null;
  total_rev_gen: Decimal | null;
  total_stock: number | null;
  unit_sold: number | null;
  updated_by: string | null;
  updated_dt: Generated<Date | null>;
  user_id: string | null;
  version: Generated<number | null>;
}

export interface TProductCat {
  created_by: string | null;
  created_dt: Generated<Date>;
  id: string;
  product_cat_code: string | null;
  product_cat_name_1: string | null;
  product_cat_name_2: string | null;
  product_cat_name_3: string | null;
  product_cat_name_4: string | null;
  product_cat_name_5: string | null;
  tenant_code: string | null;
  updated_by: string | null;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TProductImg {
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  file_id: string;
  product_id: string;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TReview {
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  id: string;
  order_detail_id: string | null;
  product_id: string | null;
  rate: number | null;
  review: string | null;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  user_id: string | null;
  version: Generated<number | null>;
}

export interface TRole {
  created_by: string | null;
  created_dt: Generated<Date>;
  id: string;
  role_code: string | null;
  role_desc: string | null;
  tenant_code: string | null;
  updated_by: string | null;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TRolePermission {
  created_by: string | null;
  created_dt: Generated<Date>;
  permission_id: string;
  role_id: string;
  tenant_code: string | null;
  updated_by: string | null;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TSequence {
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  id: string;
  seq_name: string | null;
  seq_value: number;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  version: Generated<number | null>;
}

export interface TUser {
  apt_suite: string | null;
  avatar_file_id: string | null;
  birth_dt: Date | null;
  city: string | null;
  comp_cont_name: string | null;
  comp_link: string | null;
  comp_name: string | null;
  comp_role: string | null;
  comp_tel_no: string | null;
  country: string | null;
  created_by: string | null;
  created_dt: Generated<Date>;
  email: string | null;
  first_name: string | null;
  id: string;
  last_name: string | null;
  nickname: string | null;
  password: string | null;
  postcode: string | null;
  province: string | null;
  self_intro: string | null;
  status: string | null;
  tenant_code: string | null;
  updated_by: string | null;
  updated_dt: Generated<Date | null>;
  username: string;
  verify_code: string | null;
  verify_code_exp_dt: Date | null;
  version: Generated<number | null>;
}

export interface TUserRole {
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  role_id: string;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  user_id: string;
  version: Generated<number | null>;
}

export interface TUserSession {
  access_token: string | null;
  at_expire_dt: Date | null;
  created_by: Generated<string | null>;
  created_dt: Generated<Date>;
  id: string;
  is_valid: string | null;
  refresh_token: string | null;
  rt_expire_dt: Date | null;
  tenant_code: Generated<string | null>;
  updated_by: Generated<string | null>;
  updated_dt: Generated<Date | null>;
  user_id: string;
  version: Generated<number | null>;
}

export interface DB {
  t_application_type: TApplicationType;
  t_cart: TCart;
  t_chat: TChat;
  t_chat_reaction: TChatReaction;
  t_favourite: TFavourite;
  t_feedback: TFeedback;
  t_file: TFile;
  t_forex: TForex;
  t_ip: TIp;
  t_ip_cat: TIpCat;
  t_ip_img: TIpImg;
  t_ip_ip_cat: TIpIpCat;
  t_order: TOrder;
  t_order_details: TOrderDetails;
  t_order_details_img: TOrderDetailsImg;
  t_permission: TPermission;
  t_prod_prod_cat: TProdProdCat;
  t_product: TProduct;
  t_product_cat: TProductCat;
  t_product_img: TProductImg;
  t_review: TReview;
  t_role: TRole;
  t_role_permission: TRolePermission;
  t_sequence: TSequence;
  t_user: TUser;
  t_user_role: TUserRole;
  t_user_session: TUserSession;
}
