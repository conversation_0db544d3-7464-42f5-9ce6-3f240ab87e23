[{"ipId": "146442f0-9c47-4e93-b1a9-b2e702cce1d5", "ipName1": "", "ipName2": "", "ipName3": "T1 LCK Team Player", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "구마유시 (Gumayusi) – 이민형\n포지션: 원거리 딜러(BOT)\n생년월일: 2002년 2월 6일\n키: 181cm\n외형: 건장한 체형, 부드러운 인상의 얼굴, 다양한 스타일의 헤어 변화를 즐김\n무대 스타일: T1 유니폼\n일상 스타일: 맨투맨,후디 등 캐주얼한 차림 선호\n성격 키워드: 자신감 / 유쾌함 / 개성 / 친근함\n특징: 장난기 많은 말투, 인터뷰에서의 재치 있는 답변, 승리 후 세레모니 즐기는 편\n***작품 제작 시 스폰서 로고 제외 필수입니다. ", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-28T12:31:13.165Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["b2f321e0-a108-4baa-9e60-edbfe32ef880", "67c55fde-e183-4acd-b8e2-6fe5122acf38"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/10801920_%ED%94%84%EB%A1%9C%ED%95%84_%EA%B5%AC%EB%A7%88.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/241220_GoalStudio_T10408.jpg"], "fileNames": ["10801920_프로필_구마.jpg", "241220_GoalStudio_T10408.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "8f21785f-dafe-4c6e-9e5d-b46be9375b19", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "SK telecom CS T1"}}, {"ipId": "17d28331-d1ed-4b8f-9bea-178eefcfa2cd", "ipName1": "", "ipName2": "", "ipName3": "<승리의 여신: 니케>", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "어느 날 하늘에서 나타난 강철의 외계 생명체 랩쳐에 대항하는\n소녀 전사 '니케'들의 지휘관으로서 세력을 넓혀가는 세계 탈환을 위한 미소녀 건슈팅 RPG\n\n<2차 창작 가이드 라인 - https://policy.shiftup.co.kr/ip/kr/index.html>\n시프트업은 게임과 IP에 다양한 재미와 즐거움을 더해주는 창작자 여러분의 2차 창작 활동을 존중합니다.\n여러분의 열정과 창의성, 그리고 신중하게 수행되는 2차 창작 활동이 게임 콘텐츠의 발전에 도움이 될 것입니다. \n시프트업은 창작자 여러분들과 함께 자유로운 2차 창작 활동의 활성화와 문화 콘텐츠 발전에 기여하도록 하겠습니다.\n\n<주의 사항>\n※ 2차 창작 시 아래와 같은 내용이나 표현이 포함되면 안 됩니다.\na. 법률을 위반하거나 지적재산권, 초상권 등 제3자의 권리를 침해하는 내용\nb. 특정한 정치적 견해나 입장을 나타내는 내용\nc. 특정 집단, 종교, 인종, 성별 등을 차별, 비하하거나 혐오를 조장하는 내용\nd. 공식 콘텐츠가 위험성이 있는 콘텐츠로 오해 받는 내용\ne. 시프트업의 공식 콘텐츠로 오인될 수 있는 콘텐츠가 포함된 2차 창작물을 제작 또는 배포하거나 명시적으로 허위 진술, 제안 또는 기타 허위 진술, 묵시적으로 귀하가 시프트업 또는 관련 당사자의 후원, 보증 또는 제휴 관계임을 나타내는 내용\nf. 기타 시프트업이 부적절하다고 판단하는 내용\n\n※ 2차 창작물을 공개할 때 오리지널 콘텐츠(시프트업 및 게임 이름)를 명시해 주시기 바랍니다.\n※ 본 가이드라인 또는 관련 조건을 위반하는 경우, 시프트업은 2차 창작물의 게시, 배포 또는 전송을 중단하거나 삭제하도록 요청하는 것을 포함하되 이에 국한되지 않는 적절하다고 판단되는 조치를 취할 수 있습니다.\n※ 게임 IP에 대한 모든 권리는 시프트업 또는 해당 라이선스 제공자가 보유합니다.\n※ 본 가이드라인의 내용은 예고 없이 변경될 수 있으며 개별 사항에 따라 별도의 정책이 적용될 수 있습니다.\n\nㆍ게임 다운로드 링크:\n  1) 구글 플레이: https://play.google.com/store/apps/details?id=com.proximabeta.nikke&hl=ko&gl=kr\n  2) 앱스토어: https://apps.apple.com/us/app/demian-saga/id1618201654", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-14T02:30:22.163Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["f800468d-e638-45ab-ba84-9a33008b6006", "914941e5-5483-4c51-825a-57358daa7c18"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%28KV%29_CountersIllust.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E7%B1%B3%E8%A5%BF%E5%88%A9%E6%96%AF%E5%B7%A5%E4%B8%9AKV_%E7%BB%88%E7%A8%BF%E4%BF%AE%E6%94%B93.0.jpg"], "fileNames": ["(KV)_CountersIllust.jpg", "米西利斯工业KV_终稿修改3.0.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "d84ffe4b-3114-4f20-aa05-6ddf0e70dcea", "username": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "email": "eo.se<PERSON>so<PERSON>@shiftup.co.kr", "firstName": null, "lastName": null, "nickname": null, "compName": "시프트업"}}, {"ipId": "191babae-be3f-48de-a1e9-b0ed8377e58b", "ipName1": "", "ipName2": "", "ipName3": "T1 LCK Team Player", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "페이커 (Faker) – 이상혁\n포지션: 미드(MID)\n생년월일: 1996년 5월 7일\n키: 177cm\n외형: 슬림한 체형, 단정한 머리 스타일\n무대 스타일: T1 유니폼\n일상 스타일: 기본 티셔츠 착용, 캐주얼 룩\n성격 키워드: 침착함 / 프로페셔널 / 겸손 / 쿨함\n말투 및 습관: 조리 있고 차분한 말투/ 승리 후 엄지 척/ 팬이나 미디어를 향한 깔끔하고 공손한 인사\n특징:세계 최고 수준의 리그 오브 레전드 프로게이머, 승패에 관계없이 일관된 침착한 태도, 팬들과 겸손하고 따뜻하게 소통하는 레전드 스타\n***작품 제작 시 스폰서 로고 제외 필수입니다. ", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-28T12:30:42.38Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["b3953907-dea1-4531-b753-9719a4b75d5d", "30a14945-7e13-4f36-9023-b2421934f358"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/10801920_%ED%94%84%EB%A1%9C%ED%95%84_%ED%8E%98%EC%9D%B4%EC%BB%A4.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/241220_GoalStudio_T10295.jpg"], "fileNames": ["10801920_프로필_페이커.jpg", "241220_GoalStudio_T10295.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "8f21785f-dafe-4c6e-9e5d-b46be9375b19", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "SK telecom CS T1"}}, {"ipId": "1baa122a-3faf-4bec-9598-7358de3c3eb2", "ipName1": "", "ipName2": "", "ipName3": "오즈 리:라이트'(OZ Re:write) - 벨", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "이름 : 벨\n나이 : 37세\n종족 : 요정\n신장 : 152cm\n체중 : 35kg\n성격 : 활발, 쾌활, 가벼움\n\n캐릭터 소개\t\n별의 요정족인 그녀의 별가루는 언어를 지우는 힘을 갖고 있다. \n그녀의 별가루를 사용하여 마법사들의 주문을 봉쇄할 수 있었고, 봉쇄된 마법사를 「팬」이 \n처리하였다. 그렇게 현상금이 걸린 마녀나 마법사를 사냥하는 현상금 사냥꾼 역할을 하게 된다. \n하지만 어느 날 파트너였던 「팬」이 사라지고, 그동안의 사냥꾼 일에 염증을 느낀 벨은 고향으로 \n돌아오게 된다.\n자신의 별가루가 강력한 도구로 사용될 수있다는 것을 생각한 벨은 별가루를 담은 「마법의 \n수류탄」을 제작하여 판매하고 큰 돈을 벌게 된다. \n그렇게 돈의 매력에 흠뻑 빠지게 되고 새로운 사업을 구상하게 되는데..", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T12:51:59.574Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["8a554b02-2b27-46c2-be2d-08d5fe281780", "e6725742-758d-46ac-9627-489e4d5946e7", "581681ea-15fa-4d32-8847-77a98644be78", "3ceb9546-091b-4063-93a9-fa503bddd393"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im_%E1%84%83%E1%85%A2%E1%84%8C%E1%85%B5%201%20%E1%84%89%E1%85%A1%E1%84%87%E1%85%A9%E1%86%AB.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im-06.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%A6%E1%86%AF_%E1%84%8B%E1%85%B5%E1%86%AF%E1%84%85%E1%85%A5%E1%84%89%E1%85%B3%E1%84%90%E1%85%B3.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%A6%E1%86%AF_%E1%84%8F%E1%85%A5%E1%86%AB%E1%84%89%E1%85%A6%E1%86%B8.png"], "fileNames": ["하이브im_대지 1 사본.jpg", "하이브im-06.jpg", "벨_일러스트.png", "벨_컨셉.png"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "d9377607-6f94-49f3-b9f8-611504b9bc0e", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 마코빌"}}, {"ipId": "262c92bd-5e11-4115-ad47-1c9cc3de4ad2", "ipName1": "", "ipName2": "", "ipName3": "배드 본 블러드", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "*줄거리\n끝없는 자원 쟁탈로 인해 지구는 황폐화되고, 인류는 우주로 나아가 행성 국가 시대를 맞이한다.\n\n수백년간 각기 다른 행성에서 삶을 이어오던 인류는 정체불명의 외계 문명의 침략을 받고 '노바스' 라는 지구와 유사한 환경의 행성에 정착한다.\n\n벨라토 연방, 코라 신성동맹, 아크레시아 제국.\n\n세 개의 국가로 나뉜 인류는 노바스에서 다시금 자원을 위한 독식을 꾀하고자 하는데….\n\n*캐릭터(루카)\n하층 구역 제72보육원 출신으로, 열다섯의 나이에 아크레시아 제국의 근위 생도.\n\n어렸을 때부터 열악한 환경 속에서도  강한 끈기와 노력으로 생도 시절 수석을 차지하고, 제국에 무조건적으로 복종하는 강한 충성심을 가진 군인으로 성장한다.\n\n그런 그에게 다소 불손한 사상을 가진 ‘일레이’라는 친구가 생기고, 상부의 복잡한 이해관계를 넘어 노바스의 세 국가 간 정치싸움에도 엮이게 된다.\n\n겉으로는 누구보다 이성적이고 냉정하지만 사실은 주변인들에 대한 무한한 신뢰와 자기희생을 아끼지 않는 인물.", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-08T01:12:15.879Z", "ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["95ed8eb1-e360-4f4f-a07b-99c38de0659f", "962ae1ce-6c7c-4f35-a58f-453a34d4331c", "c7f91e23-dfb8-4704-9fcf-b62ea256e172", "c5f4057e-98bb-4c69-bdb9-90e3984776d1", "c4a5fa4b-13ef-47c1-ae59-1a2d9d94d8dc"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EB%B0%B0%EB%93%9C%20%EB%B3%B8%20%EB%B8%94%EB%9F%AC%EB%93%9C%201.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EB%B0%B0%EB%93%9C%20%EB%B3%B8%20%EB%B8%94%EB%9F%AC%EB%93%9C%202.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%EB%A3%A8%EC%B9%B4%201.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%EB%A3%A8%EC%B9%B4%202.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%EB%A3%A8%EC%B9%B4%203.jpg"], "fileNames": ["배드 본 블러드 1.jpg", "배드 본 블러드 2.jpg", "루카 1.jpg", "루카 2.jpg", "루카 3.jpg"], "categories": [{"ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "fe20dc83-7184-45af-81e5-e8432066b3f3", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 리버스"}}, {"ipId": "275ada47-a446-4680-b4e1-3224c7dfc75c", "ipName1": "", "ipName2": "", "ipName3": "강분팡", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "🩷 이름 : 강분팡\n🩷 나이 : 23세\n🩷생일 : 2003년 3월 17일\n🩷 MBTI : ISTP/ISFP\n🩷 팬이름 : 포자\n🩷 팬닉넴 : [팡]ooo", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T12:26:16.102Z", "ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["2c3524fb-5db9-4336-b56b-1558218d9379", "7cb8db6f-1bf9-4725-a5f8-f48b3b24dd24"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%8F%E1%85%B3%E1%84%85%E1%85%A6%E1%84%87%E1%85%B5%E1%84%8C%E1%85%B2_img-10.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%8F%E1%85%B3%E1%84%85%E1%85%A6%E1%84%87%E1%85%B5%E1%84%8C%E1%85%B2_img-09.jpg"], "fileNames": ["크레비쥬_img-10.jpg", "크레비쥬_img-09.jpg"], "categories": [{"ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "0e8e1fb1-8130-4c21-ade0-09f5c089a4b3", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "크레비쥬"}}, {"ipId": "2d229ede-a15a-491c-b36d-0794a8aaa884", "ipName1": "", "ipName2": "", "ipName3": "소닉 더 헤지혹 (<PERSON> the Hedgehog)", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "▼IP명\n소닉 더 헤지혹\n통칭: 소닉\n\n\n▼개요\n어디서든 음속으로 달리는, 역사상 가장 빠른 고슴도치.\n\n자유롭게 사는 것을 좋아하고, 불의를 싫어한다. 다소 성미가 급하지만, 곤란한 사람을 보면 그냥 지나칠 수 없는 따뜻한 마음도 가지고 있다.\n\n인생은 사건과 모험의 연속이라고 생각하며, 주변의 규칙과 상식보다 자신만의 규칙을 따르며 산다. 약속은 반드시 지키며, 어기지 않는다. 자신의 정의에 대해서는 항상 정직하다.\n\n평소에는 단순한 장난꾸러기지만, 어떤 위기 상황에서든지 여유롭게 넘기며, 결정적인 순간에는 다른 사람을 깜짝 놀라게 할 정도로 강렬하고 날카롭게 변한다.\n\n\n▼크기\n키: 100cm\n체중: 35kg\n\n\n▼금지 사항\n- 정치적, 종교적 내용을 연상시키는 표현\n- 공공질서와 윤리에 반하는 표현\n- 다른 저작권에 저촉될 수 있는 표현\n- 성적, 음주, 담배 등과 관련된 표현\n- 칼이나 총 등, 살상력 있는 무기를 들고 있는 표현\n- 세가의 심사에서 부적합하거나 허가되지 않는 표현 및 상품", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-15T05:44:15.712Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["32b17276-e395-4c53-ae3e-0f35af965e54"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/sega-01.jpg"], "fileNames": ["sega-01.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "0e6f62ad-5bbb-4e2e-9970-970216285f33", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "SEGA"}}, {"ipId": "39769340-b5e7-4fe4-8d53-751e1a1c4299", "ipName1": "", "ipName2": "", "ipName3": "벨디르 미스틸테인 아스티즈 베들폴리니아 나인 아차트라 페이챠리온 나트리그", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "1. IP 기본 정보\n벨디르가 이름이고, 뒤에 미스틸테인은 부대명. 이후 나오는 단어들은 모두 죽인 적들의 이름이다.\n영문도 모르고 지상으로 떨어진 천사다.\n떨어진 이유를 기억하지 못한다고 주장하고 있다.\n깨진 헤일로와 검게 탄 날개 끝을 비롯해\n그녀를 보다 보면 떨어질만했다는 생각이 든다.\n어쨌든 천사는 천사.\n\n2. 주요 캐릭터 설명\n - 이름 : 벨디르 미스틸테인 아스티즈 베들폴리니아 나인 아차트라 페이챠리온 나트리그\n - 외형적 특징 :  \n키 : 162\n체형 : 마른 체형, 풍만한 가슴\n특징적 소품 : 헤일로-날개, 보석 장신구\n - 대사 스타일 : 존댓말\n - 관계도 및 설정 : 하늘에서 죄를 지어서 데드케이브에 떨어진 상태. 이아나의 동굴 근처 절벽에 거주 중.\n - 시그니처 포즈/상징물/컬러 : 귀족 손짓 / 헤일로, 보석 / #ABD1FE\n\n3. 세계관 및 배경 설명\n - IP주요 무대 : 판타지 세상\n - 세계관 설정 요약 : 마법과 이종족이 존재하는 이세계\n - 시대 설정 : 중세풍 판타지\n\n4. 비주얼 디자인 가이드\n - 대표 컬러 / 보조 컬러 :  ABD1FE / 금색\n\n5. 2차 창작 및 굿즈 제작 가이드\n - 허용되는 제작 범위 : 제한 없음\n - 포현 가능 / 불가능한 방향성 : 지나치게 선정적인 포즈, 폭력적인 연출은 NG", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T02:28:29.515Z", "ipCatCode": "O", "ipCatName1": "Other", "ipCatName2": "その他", "ipCatName3": "기타", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["b93b9f30-6418-44fc-8664-b0a13bbe8b51", "f0af8c3b-adcd-4b73-876c-6ff809e4a4cd", "f22f591b-ce1e-4644-8dc2-a17b7c597f8f"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1beldir_2D_1st%20%EC%82%BC%EB%A9%B4%EB%8F%84.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1V-LUP_Beldir_%EC%82%BC%EB%A9%B4%EB%8F%84_23.06.26.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/124.01.31_V-LUP_Beldir_3rd_3%EB%A9%B4%EB%8F%84.jpg"], "fileNames": ["1beldir_2D_1st 삼면도.jpg", "1V-LUP_Beldir_삼면도_23.06.26.png", "124.01.31_V-<PERSON><PERSON>_Beldir_3rd_3면도.jpg"], "categories": [{"ipCatCode": "O", "ipCatName1": "Other", "ipCatName2": "その他", "ipCatName3": "기타", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "eb90c9e0-ef08-4101-8695-34a03ac61427", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 크리에이터버스"}}, {"ipId": "3a1bff4a-3f89-4dc3-9231-484b68ee2976", "ipName1": "", "ipName2": "", "ipName3": "Princess Connect! Re: Dive / プリンセスコネクト！ Re:Dive", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "Princess Connect! Re: Dive is an anime RPG developed by Cygames.", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-20T11:31:20.506Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["9d53128b-703c-4266-8eee-06a3581e4e5f"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%89%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%80%E1%85%A6%E1%84%8B%E1%85%B5%E1%86%B7%E1%84%8C%E1%85%B3-03.jpg"], "fileNames": ["사이게임즈-03.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "4215a41a-2cef-455d-bfcf-1103fcec656a", "ipName1": "", "ipName2": "", "ipName3": "Wonder Festival 2025 Korea 완다 & 리셋", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "1. IP 기본 정보\n\n● 이름 및 활동명\n완다 (<PERSON>): 모험을 좋아하는 에너자이저 고양이형 캐릭터\n리셋 (Reset): 차원을 넘나드는 토끼형 천재 프로그래머\n\n● 주요 이미지 및 콘셉트 요약\n완다와 리셋은 원래 일본에서 활약하던 캐릭터!\n하지만 어느 날, 정체불명의 원더페스티벌 포털이 열리고,\n둘은 갑자기 Wonder Festival 2025 Korea로 워프!?\n\n새로운 세계에서 새로운 미션!\n한국 문화를 배우고, 한 번도 본 적 없는 조형 작품들을 만나며 두근두근한 모험이 시작된다!\n\n하지만 즉흥적으로 돌진하는 완다의 성격과, 차분하면서도 귀여운 걸 좋아하는 츤데레 기질의 리셋이 충돌할 때마다\n사건・사고・대혼란이 끊임없이 벌어지는데……!?\n\n\n2. 외형적 특징 및 이미지 키워드\n\n● 완다\n•성격 키워드: 쾌활함, 에너지 넘침, 직진형, 감각적\n•이미지 요약: 팀 분위기를 띄우는 중심, 감정표현이 풍부한 스타일\n•키/체형: 약 155cm / 작고 탄력적인 체형\n•헤어스타일: 밝은 핑크색 숏컷\n•패션 아이템: 고양이 귀 헤드기어 , 점프슈트형 하이테크 수트\n•대표 스타일: 컬러풀하고 활동적인 무드의 슈트형 의상 + 변형 가능한 블래스터\n\n● 리셋\n•성격 키워드: 냉정, 계산적, 츤데레, 내면의 귀여움 추구\n•이미지 요약: 완다와 대비되는 쿨한 매력, 가끔 보이는 허당기가 포인트\n•키/체형: 약 162cm / 슬림하고 중성적인 체형\n•헤어스타일: 실버톤 장발\n•패션 아이템: 토끼 귀 헤드기어, 미세한 회로 문양이 새겨진 슈트\n•대표 스타일: 하이테크 × 미니멀 디자인의 수트 + 반짝이는 방어 장치 (버블실드)\n\n\n3. 2차 창작 및 굿즈 제작 가이드\n\n●표현 가능/불가능 방향성 및 주의사항\n가능한 방향성: SD화, 캐주얼한 코믹 묘사, 일상풍 확장 등\n불가능한 방향성: 성적 묘사, 폭력적 설정, 캐릭터 성격의 왜곡 표현\n주의 사항: 캐릭터의 시그니처 요소(귀, 꼬리, 블래스터, 수트)는 반드시 유지되어야 함", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-03T00:59:26.772Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["a03f9d7d-6983-47b8-9981-a04dfc0f87b9", "66f45b37-d354-47ae-85f0-bab0b82d519f"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/KakaoTalk_Photo_2025-04-24-15-18-33%20001.jpeg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/KakaoTalk_Photo_2025-04-24-15-18-33%20002.jpeg"], "fileNames": ["KakaoTalk_Photo_2025-04-24-15-18-33 001.jpeg", "KakaoTalk_Photo_2025-04-24-15-18-33 002.jpeg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "43c48cb6-b1d6-42cd-ba30-fe9171fa3edd", "ipName1": "", "ipName2": "", "ipName3": "오즈 리:라이트'(OZ Re:write) - 제노아", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "이름 : 제노아\n나이 : 기동된지 7년\n종족 : 기인\n신장 : 160cm\n체중 : 80kg\n성격 : 분석적, 선함, 차분함\n\n캐릭터 소개\t\n먼치킨 왕국의 숲에서 발견된 기인. \n코어가 없어 가동할 수 없다는 게 기술자들의 분석이었지만, 도로시를 만나 그녀의 마력을 흡수하며 기동할 수 있게 되었다.\n어떤 원리로 작동하고 있는지 알 수 없는 자신의 존재에 대해서 가끔 사유할 때가 있지만, 대부분의 경우 크게 신경 쓰지 않고 자신을 깨워준 도로시를 주인으로 여기며 지내고 있다.\n재기동한 지 7년밖에 되지 않은 탓인지 기인 중에서도 아직 감정 표현이 서툴고 기계적인 말투를 구사하지만 기본적으로 마음 씀씀이는 선하며, 나름의 유머를 구사하려고 노력한다.\n신체의 변화를 정밀하게 인지해 감정을 추측하고 계산하는 특기 아닌 특기가 있다.", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T12:36:49.606Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["ac3714e3-bddc-4191-989e-25f28dae3775", "0f395c73-66cb-41f1-84d3-ac8728db31e2", "1da715b2-6f7d-431a-98da-89ed05477bbb", "d3e89a5c-e268-4e00-a547-349a64386737"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im-14.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im-02.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%8C%E1%85%A6%E1%84%82%E1%85%A9%E1%84%8B%E1%85%A1_%E1%84%8B%E1%85%B5%E1%86%AF%E1%84%85%E1%85%A5%E1%84%89%E1%85%B3%E1%84%90%E1%85%B3.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%8C%E1%85%A6%E1%84%82%E1%85%A9%E1%84%8B%E1%85%A1_%E1%84%8F%E1%85%A5%E1%86%AB%E1%84%89%E1%85%A6%E1%86%B8.png"], "fileNames": ["하이브im-14.jpg", "하이브im-02.jpg", "제노아_일러스트.png", "제노아_컨셉.png"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "d9377607-6f94-49f3-b9f8-611504b9bc0e", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 마코빌"}}, {"ipId": "43e02eb6-62e8-44cd-bfeb-c569eda80db5", "ipName1": "", "ipName2": "", "ipName3": "에픽세븐 (Epic Seven) 삭제 예정", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "PLAY THE ANIMATION! 내 손으로 플레이 하는 한 편의 애니메이션 '에픽세븐' \n전세계 300만 사용자가 즐기는 에픽세븐은 풀 프레임 2D 애니메이션 연출이 매력적인 턴제 RPG 입니다. \n\n원더 페스티벌에 출전하는 모든 굿즈 창작자 여러분! 반갑습니다!\n에픽세븐에 등장하는 수많은 영웅, 아티팩트, 배경, 펫 등 모든 소재로 출품하실 수 있습니다. 많은 관심을 가져주셔서 감사드립니다.\n\n단, 에픽세븐과 콜라보레이션으로 제작된 저작물은 출품이 불가합니다. \n대표적으로 다음 영웅과 아티팩트가 해당되며, 콜라보레이션 한정 펫, 포스터, 일러스트 역시 불가하오니 유의해주시기 바랍니다.\n\n1. 영웅(캐릭터): 솔, 엘페르트, 디지, 바이켄, 잭 오, 키즈나 아이, 렘, 람, 에밀리아, 리무루, 슈나, 밀림, 베니마루, 에드워드 엘릭, 로이 머스탱, 리자 호크아이, ae-카리나, ae-윈터, ae-지젤, ae-닝닝, 아인즈 울 고운, 알베도, 샤르티아\n\n2. 아티팩트: 정크야드 도그, 푸른빛 혜성, 항마의 가면, 잭 오의 상징, 강철의 오토메일, 베니마루의 도, 스포이트 랜스, 로켓 펀치, 3F, 아머먼트, 명사수의 권총, 네크로 & 운디네, 잘려진 뿔 지팡이, 개량형 드래곤 너클, 발화포 장갑, 빛의 프레임, 스태프 오브 아인즈 울 고운, 수호의 얼음 파편, 천의무봉, EXIF Detective(E.d.)\n\n‘창작 굿즈’ 본품, ‘창작 굿즈’ 패키지, 그리고 상세페이지 본문에는 회사의 공식(Official) 제품이 아님을 표시하기 위해 반드시 아래 문구를 명기해야 하며, \n‘창작 굿즈’ 전용 로고는 필요에 따라 적용 유무를 선택할 수 있습니다.\n\n- 기본형: Epic Seven Non-official Goods Made by Fan User\n- 축약형: E7 FANMADE (공간 제약으로 인해 기본형 기재 불가 시 사용)\n- ‘창작 굿즈’ 전용 로고 다운로드 링크 : https://page.onstove.com/epicseven/kr/view/10182713\n\n다시 한 번 에픽세븐으로 참여해주시는 점 감사드리며, 에픽세븐은 UGC 정책도 운영하고 있으니 많은 관심 부탁드립니다.\n(이메일 문의: 에픽세븐_UGC <<EMAIL>>)", "ipDesc4": "", "ipDesc5": "", "ipStatus": "I", "createdDate": "2025-05-19T09:15:44.164Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["f47c63be-3074-49ee-b8cc-09f1106fb660", "b1dad5e0-c6c4-40fa-b805-5655fc94aca9", "aa9e92d4-088e-45d5-a7cd-9ffb015881a7", "2129471f-31eb-4f62-a377-10d233624e9e", "2eba3e60-1e13-47e6-bf49-764ef17d17f0"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/6%20%281%29.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/3%20%281%29.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/4%20%281%29.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/2%20%281%29.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/1%20%281%29.jpg"], "fileNames": ["6 (1).jpg", "3 (1).jpg", "4 (1).jpg", "2 (1).jpg", "1 (1).jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "6153e82c-cd44-4a3b-8066-33f17ed599a0", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "스마일게이트 홀딩스 메가포트지점"}}, {"ipId": "43ec8fad-04c1-41f5-bf8b-84cd777b4fce", "ipName1": "", "ipName2": "", "ipName3": "플레이투게더(Play Together)", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "1. IP 기본 정보\nㆍ게임명 : 플레이투게더\n\nㆍ게임 소개 \n: 동화 풍의 가상세계 '카이아 섬'에서 글로벌 친구들과 함께 모여 다양한 일상 생활을 즐길 수 있는 캐주얼 소셜 게임\n\nㆍ게임 주요 특징\n  1) 실시간 멀티플레이로 대결하는 30종 이상 미니게임 콘텐츠\n  2) 글로벌 친구들과 자유롭게 소통할 수 있는 커뮤니케이션 시스템\n  3) 나만의 캐릭터와 하우스를 꾸미고 친구들을 만나는 홈파티 기능\n  4) 낚시, 채집, 요리, 펫, 드라이빙, 셀카 촬영, 학교 수업, 일일 미션 등 다양한 일상생활 즐길거리 제공\n\nㆍ 게임 다운로드 링크\n  1) 모바일 : http://haegin.kr/Download/playtogether\n  2) PC : https://store.steampowered.com/app/3198850/Play_Together/\n\n2. 주요 디자인 요소 특징\nㆍ동화 풍의 귀여운 그래픽 추구 \nㆍ현실 세상을 닮은 가상 세계 배경 구현\nㆍ성별 구분이 없는 캐릭터 디자인\n   - 4가지 체형(애기, 평범, 롱다리, 듬직) 선택 가능\n   - 피부색, 헤어, 얼굴 표정, 상의, 하의, 신발, 가방, 액세서리 변경 가능\n   - 장난감, 악기, 낚싯대, 곡괭이 등 아이템 양손에 착용 가능\n   - 3,000개 이상의 꾸미기 아이템 보유\n\n3. 2차 창작 및 굿즈 제작 가이드\nㆍ창작 제안: 귀엽고 재미 있는 플레이투게더의 캐릭터를 강조한 굿즈\n  - 다양한 아이템으로 캐릭터를 자유롭게 꾸미는 게임인 만큼, 여러분이 생각하는 플레이투게더 캐릭터를 창조해주세요!\n\n  1) 기존 아이템 디자인 활용 시\n    - 게임 내에 존재하는 다양한 디자인의 아이템으로 캐릭터의 외형을 꾸미고 굿즈에 활용해주세요. \n      (단, 플레이투게더와 제휴 중인 IP의 아이템은 제외해주세요!)\n\n  2) 신규 창작 희망 시\n    - 내가 원하는 코스튬과 아이템을 착용한 플레이투게더 캐릭터의 모습을 자유롭게 창작해주세요! \n      (단, 다른 IP의 저작권을 침해하지 않도록 주의해주세요.)\n\nㆍ표현 가능/불가능 방향성 및 주의사항\n  - 가능한 방향성 : 자유로운 캐릭터 외형 창작, 캐주얼한 코믹 묘사, 창작자가 희망하는 굿즈 카테고리 모두 제작 가능\n  - 불가능한 방향성 : 성적 묘사, 폭력적 설정 \n  - 주의사항 : 캐릭터 체형은 기본 4가지 체형 내에서 반드시 유지되어야 합니다. (플레이투게더 디자인 아이덴티티 유지)\n\nㆍ플레이투게더 창작자 가이드라인(파일)\n  - https://drive.google.com/file/d/1Zl0yX_TZd81ArF4-X4r1sA95eNWTOjiH/view?usp=drive_link", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-25T02:47:54.735Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["7255e81a-866f-4d5d-9638-9ab7ac79d6eb"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/PT_LiveStream_Banner_600x800_A.png"], "fileNames": ["PT_LiveStream_Banner_600x800_A.png"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "26ca2b66-a47e-434f-9984-314ad5704246", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "(주)해긴"}}, {"ipId": "4a8e9f17-778b-46fd-87d9-341387c3e9ee", "ipName1": "", "ipName2": "", "ipName3": "현단아 / Hyundana", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "1. IP 기본 정보\n• 이름 : 현단아 \n\n• 주요 이미지 및 콘셉트 \n달달한 단꿈을 꾸게 해주는 아이라는 뜻의 이름, 현단아\n병아리와 함께 사는 엉뚱한 시골소녀. \n세상 사람들을 행복하게 해주고 싶어 아이돌을 꿈꿨으나, \n본인의 노래 실력이 엉망인 것을 일찌감치 깨닫고 아이돌을 포기. \n지금은 어떻게 하면 사람들을 행복하게 해줄 수 있을지 고민하며 병아리와 함께 하는 세계 여행을 꿈꾸고 있다. \n\n2. 외형적 특징 및 스타일\n• 키/체형 - 155cm / 평범한 체형.\n• 헤어스타일 - 밝은 핑크색 컬러. 번헤어를 자주 하나, 양갈래, 반묶음, 포니테일 등 가리지 않는다.\n• 패션 아이템 - 캐쥬얼, 아이돌 무대 의상, 교복 등\n• 병아리 - 화가나면 빨간색, 흑화하면 검은색이 되기도 한다. (평생 병아리로 성장하지 않음)\n\n3. 성격 및 이미지 키워드\n• 성격 키워드 : 엉뚱, 발랄, 허당, 귀여움자각없음, 노력형, \n• 이미지 요약 : 생각의 흐름이 어디로 튈지 모르는 천진난만함. \n                   엉뚱한 말실수나 행동들이 귀여움. \n                   본인은 열심히 노력한다고 하지만 허당끼가 있음.\n• 대표적 말투 및 습관 \n혀가 짧은 편이라, 혀 짧은 발음이 많으며\n고민하는 듯 조금 느린 말투가 특징\n\n4. 비주얼 디자인 가이드\n• 대표 컬러 및 보조 컬러 FEBAC3 / FFE047\n\n5. 2차 창작 및 굿즈 제작 가이드\n• 허용 제작 범위 : 제한 없음\n• 표현 가능/불가능 방향성 \n  - 폭력적인 설정, 성적묘사, 지나치게 선정적인 포즈 , 성인이용가 수준 NG\n• 주의 사항: 병아리는 항상 함께여야 함. \n위치는 주로 머리 위에 있으나, 손이나 어깨 등 여기저기 옮겨다님.", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T10:16:16.055Z", "ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["d782c6d9-db20-4166-b2c2-8f1e28876a2b", "1402ac1a-0c46-4eeb-80de-06cf7fe17420", "9d5f0409-3450-40aa-b73b-6c56b66b0171"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/Meligo_Screenshot_1920X1080_20250430_183440_233d.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/Meligo_Screenshot_1920X103.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/0416r_7.png"], "fileNames": ["Meligo_Screenshot_1920X1080_20250430_183440_233d.png", "Meligo_Screenshot_1920X103.png", "0416r_7.png"], "categories": [{"ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "abad00d9-3384-47f6-9b0a-f827a2393417", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "현단아v"}}, {"ipId": "4cf3c8e1-0e60-4221-8a2a-ef2bfe630d21", "ipName1": "", "ipName2": "", "ipName3": "늘우", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "💜이름 : 늘우\n💜종족 : 숲의 정령\n(사슴 아님, 노루 아님, 고라니 아님)\n💜생일 : 3월 30일\n💜나이 : 500+@살 / 인간 나이 24세\n💜키 : 163cm\n💜MBTI : ISTP\n💜첫 데뷔 : 2020.09.18 ~ 2023.10.10\n💜재데뷔 : 2025.03.05\n💜팬덤명 : NU(앤유) / 팬닉: 이름 앞에 [NU]\n💜좋아하는 거 : 디저트, 맛있는 음식, 잠, 노래, 예쁜 여자\n💜싫어하는 거 : 갑툭튀, 귀신, 고어, 호러, 벌레, 곤충", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T12:28:25.352Z", "ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["e7191649-9df7-4363-b096-8fa22bec55d1", "f9dd0449-2e1f-4dba-8f28-9a2cd7b51c22"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%8F%E1%85%B3%E1%84%85%E1%85%A6%E1%84%87%E1%85%B5%E1%84%8C%E1%85%B2_img-08.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%8F%E1%85%B3%E1%84%85%E1%85%A6%E1%84%87%E1%85%B5%E1%84%8C%E1%85%B2_img-07.jpg"], "fileNames": ["크레비쥬_img-08.jpg", "크레비쥬_img-07.jpg"], "categories": [{"ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "0e8e1fb1-8130-4c21-ade0-09f5c089a4b3", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "크레비쥬"}}, {"ipId": "562540f3-a415-48f2-a2fc-c41ccac82403", "ipName1": "", "ipName2": "", "ipName3": "문모모", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "블리즈 (VLYZ)의 上꼬맹이 문모모 이옵니다. ", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T08:49:40.633Z", "ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["a73918b7-401e-4bff-bbaa-579abb6a066a", "9a642577-4a1b-472e-8ba1-ff78d5caf492", "7278546c-4514-4dbb-a0e4-7a41a589e8ca", "453b6445-75a9-456f-9414-9e923500976d", "bd8d4f83-5896-4b53-b299-bab64b630d52"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-10.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-09.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-06.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-07.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-08.jpg"], "fileNames": ["블리즈-10.jpg", "블리즈-09.jpg", "블리즈-06.jpg", "블리즈-07.jpg", "블리즈-08.jpg"], "categories": [{"ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "48450811-98c3-4494-8435-3335222ffe99", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "블리즈"}}, {"ipId": "5651cf2a-14c5-4bc5-ad58-ee79b8ff353a", "ipName1": "", "ipName2": "", "ipName3": "신세기 에반게리온 극장판 데스 (트루)²／에어／진심을、너에게 / 新世紀エヴァンゲリオン劇場版　DEATH (TRUE)2 / Air / まこころを、君に", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "신세기 에반게리온 극장판 데스 (트루)²／에어／진심을、너에게 / 新世紀エヴァンゲリオン劇場版　DEATH (TRUE)2 / Air / まこころを、君に", "ipDesc4": "", "ipDesc5": "", "ipStatus": "I", "createdDate": "2025-05-19T13:55:55.204Z", "ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["6a927c26-f83e-4c01-a478-c59562b80480"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E6%96%B0%E4%B8%96%E7%B4%80%E3%82%A8%E3%82%A6%E3%82%99%E3%82%A1%E3%83%B3%E3%82%B1%E3%82%99%E3%83%AA%E3%82%AA%E3%83%B3%E5%8A%87%E5%A0%B4%E7%89%88%E3%80%80DEATH%20%28TRUE%292%20.png"], "fileNames": ["新世紀エヴァンゲリオン劇場版　DEATH (TRUE)2 .png"], "categories": [{"ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "5a47d608-54a8-4d87-8b60-a52c82d06e75", "ipName1": "", "ipName2": "", "ipName3": "오즈 리:라이트'(OZ Re:write) - 로라", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "이름 : 로라\n나이 : 23세\n종족 : 반수인\n신장 : 175cm\n체중 : 64kg\n성격 : 밝음, 천연, 본능, 소유욕(마력)\n\n캐릭터 소개\t\n로라는 태어날 때부터 마력에 예민했으며, 접촉을 통해 마력을 흡수하는 신체를 타고났다. \n마력을 흡수할 때면 항상 그녀는 행복했다. \n마음을 연 사람과 접촉하고 있으면 자동적으로 마력이 흡수된다. \n타고난 그녀의 매력에 수많은 남성 마법사들이 마음을 열고, 또 마력을 빼앗겨 왔다. \n흡수된 충만한 마력 덕에 항상 밝고 행복한 상태이다.\n따라서 그녀는 본능적으로 강한 마력에 집착하며, 그것을 가지기 위해 자신의 마력으로 상대를 \n무력화하고, 강제로 접촉하는 공격성을 보이기도 한다. \n극상의 향기가 나는 캐플린을 껴안으려 시도하다가 계속해서 거절당하고, 따라다녔던 과거가 \n있으며, 이제는 캐플린의 가장 가까운 이해자가 되었다", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T12:41:42.387Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["016a3562-e3de-48ce-958e-24219b8e8ae6", "ef54f436-1000-4713-87e7-18a44efb9986", "d659e7a4-bbb3-42c3-943f-42bdee932aa6", "d7932a31-8e50-4f1b-a3cf-da771fd1bbef"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im-10.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im-08.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%85%E1%85%A9%E1%84%85%E1%85%A1_%E1%84%8B%E1%85%B5%E1%86%AF%E1%84%85%E1%85%A5%E1%84%89%E1%85%B3%E1%84%90%E1%85%B3.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%85%E1%85%A9%E1%84%85%E1%85%A1_%E1%84%8F%E1%85%A5%E1%86%AB%E1%84%89%E1%85%A6%E1%86%B8.png"], "fileNames": ["하이브im-10.jpg", "하이브im-08.jpg", "로라_일러스트.png", "로라_컨셉.png"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "d9377607-6f94-49f3-b9f8-611504b9bc0e", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 마코빌"}}, {"ipId": "5aa2a96e-b18c-45ae-944c-01db2445637d", "ipName1": "", "ipName2": "", "ipName3": "브라운더스트2", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": " ", "ipDesc4": "", "ipDesc5": "", "ipStatus": "I", "createdDate": "2025-05-20T10:56:15.276Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["11f5a2dc-6110-4ee2-accf-be9184a2b4ba"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/2%E1%84%8E%E1%85%A1_01.jpg"], "fileNames": ["2차_01.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "5abf6ebe-034d-4773-9334-fabc63c1d237", "ipName1": "", "ipName2": "", "ipName3": "데미안 전기", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "1. IP 기본 정보\nㆍ게임명: 데미안 전기\n\nㆍ게임 요약 설명:\n‘데미안 전기’는 전설의 오파츠 ‘데미안’을 둘러싼 해적들의 모험 이야기를 다루는 수집방치형 RPG입니다. 마법과 과학의 공존, 미지의 이차원 탐험, 세계의 종말을 막는 영웅적인 이야기 등 다양한 콘셉트가 녹아 있는 세계 속에서, 플레이어는 이야기의 주인공인 아이샤와 제이든과 함께 각각의 매력이 있는 수십종의 캐릭터를 동료로 맞이하고 그들을 성장시키면서 이야기를 진행해 나가는 게임입니다.\n\nㆍ게임 다운로드 링크:\n  1) 구글 플레이: https://play.google.com/store/apps/details?id=com.haegin.pirates&hl=en\n  2) 앱스토어: https://apps.apple.com/us/app/demian-saga/id1618201654\n\n2. 2차 창작 및 굿즈 제작 가이드\n ㆍ개발사 한마디: 매력 넘치는 세계관과 캐릭터들은 첨부 드리는 파일을 참고 부탁드리며, 최대한 원작 디자인을 훼손하지 않는 선에서 자유롭게 창작 바랍니다. 기본적으로 모든 것에 대해 열려 있으나, 성인물 혹은 지나치게 선정적인 표현은 삼가 바랍니다. \n    - 키워드: 운명, 세계 붕괴, 해적, 동료, 모험, 보물, 판타지\n\n ㆍ창작 범위 및 제안: 기본적으로 모든 상품에 대해 열려 있습니다만, 지나친 선정성 및 폭력성 만큼은 지양 부탁드립니다. 또한 가급적 원 디자인을 존중 부탁드리며, 훼손하지 않는 선에서 창작을 부탁드립니다.\n\n\n ㆍ IP 창작자용 참고 자료\n  1) 설정 자료\n    - 게임 설정: https://docs.google.com/spreadsheets/d/1o4xWAmlfqtw3hfNPkXjZsQXuk7t44FJIpFd-hhX8NyA/edit?usp=drive_link\n    - 캐릭터 설정: https://docs.google.com/spreadsheets/d/1pnLv06lZ8NMHaRm2IX2xpk9zb2JuBqYEpgim0kKjbGo/edit?usp=drive_link\n\n 2) 이미지 파일\n   - 캐릭터 이미지: https://drive.google.com/drive/folders/11OmcKB5Ms3I8KMwTwmYI4iPlHCWxRxEd?usp=drive_link\n   - 배경 원화: https://drive.google.com/drive/folders/1YGhZJcegicbMkzLyvWzkMtXdtFdnlvEn?usp=drive_link\n   - 국가 아이콘 & 국기: https://drive.google.com/drive/folders/1Bh509j7DwxFd_abhvZWxToM7oxZsGoey?usp=drive_link", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-25T04:46:25.951Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["29a7964a-8249-46fa-aadf-80efdb9e9528", "b4b84e15-b826-4bbe-83c8-300b98b12152", "e38f4803-7e30-4ece-b339-278fd6fb795a", "28c64f6b-1e1b-4c88-a96c-abc00a212bc7", "d2b39686-979a-4ec4-8f0d-673de7c79c2b"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/DS_onestore_1024x500_KR.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/DS_wallpaper09_1920x1080_KR.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/DS_wallpaper11_1920x1080_%EB%AF%B8%EC%8A%A4%ED%94%BC%EB%9D%BC%ED%83%80.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/DS_6%EC%9B%94_%EC%9B%94%ED%8E%98%EC%9D%B4%ED%8D%BC_1920x1080.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/DS_CommunityTitle_cafe_mobile_720x450.png"], "fileNames": ["DS_onestore_1024x500_KR.png", "DS_wallpaper09_1920x1080_KR.png", "DS_wallpaper11_1920x1080_미스피라타.png", "DS_6월_월페이퍼_1920x1080.png", "DS_CommunityTitle_cafe_mobile_720x450.png"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "26ca2b66-a47e-434f-9984-314ad5704246", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "(주)해긴"}}, {"ipId": "5aeaed88-3ba8-42d4-8505-33a1196c909f", "ipName1": "", "ipName2": "", "ipName3": "리미 / Limi / リミ", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "1. IP 기본 정보\n보부상, 코끼리 땃쥐\n차원을 이동하는 보부상.\n차원을 이동하며 여러 물건을 수집하고 판매한다.\n다양한 차원에서 색다른 물건과 이야기를 접하는 것을 즐긴다.\n의뢰를 받는다면 어떠한 물건도 구해다 준다.\n\n2. 주요 캐릭터 설명\n - 이름 : 리미 / Limi / リミ\n - 외형적 특징 :  \n키 : 153\n체형 : 가녀린 소녀 체형, 빈유도 거유도 아님\n머리 색상 : b58797 / ffffff\n피부색 : fff1e6 / dcaca4\n특징적 소품 : 큰가방 / 귀 / 꼬리\n복장 : 후드, 반바지\n - 대사 스타일 : 3인칭, ~했지 뭐야\n - 관계도 및 설정 : 매드사이언티스트(노이)에게 재료 납품\n - 시그니처 포즈/상징물/컬러 : 큰 가방, 꼬리, 귀 / A3EEE9\n\n3. 세계관 및 배경 설명\n - IP주요 무대 : 판타지 세상\n - 세계관 설정 요약 : 마법과 이종족이 존재하는 이세계\n - 시대 설정 : 중세풍 판타지\n\n4. 비주얼 디자인 가이드\n - 대표 컬러 / 보조 컬러 :  A3EEE9 / b58797\n\n5. 2차 창작 및 굿즈 제작 가이드\n - 허용되는 제작 범위 : 제한 없음\n - 포현 가능 / 불가능한 방향성 : 지나치게 선정적인 포즈, 폭력적인 연출은 NG", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T02:36:48.481Z", "ipCatCode": "O", "ipCatName1": "Other", "ipCatName2": "その他", "ipCatName3": "기타", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["2d421ada-102d-492b-ae04-e3ea5f29c934"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1%EB%A6%AC%EB%AF%B8%202-5.png"], "fileNames": ["1리미 2-5.png"], "categories": [{"ipCatCode": "O", "ipCatName1": "Other", "ipCatName2": "その他", "ipCatName3": "기타", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "eb90c9e0-ef08-4101-8695-34a03ac61427", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 크리에이터버스"}}, {"ipId": "5cb74c63-fd45-4480-b385-6f4f1444bd0d", "ipName1": "GIRL`S FRONTLINE: NEURAL CLOUD", "ipName2": "", "ipName3": "", "ipName4": "", "ipName5": "", "ipDesc1": "\"Warning! Fatal Error: System integrity severely compromised...\"\nThis is an unprecedented threat to the existence of Dolls. In face of formidable enemies and a future filled with uncertainties, the scattered Dolls grit their teeth and steel themselves as they travel in search of the slim hope of salvation.\nHumankind may have abandoned them, but as the person in charge of \"Project Neural Cloud\", you have steadfastly set foot in this unknown land and founded the \"Exiles\" as you take in meandering Dolls. With you as their leader, the Exiles shall explore the secrets of the world, find a way out of this desperate situation, and uncover the truth..", "ipDesc2": "", "ipDesc3": "", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-29T03:42:50.653Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["a66e83e8-39ee-498c-bdae-c5e861ed11e9"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/2024%E5%91%A8%E5%B9%B4KV%E3%80%90%E5%AE%8C%E6%88%90%E7%89%88%E3%80%91.png"], "fileNames": ["2024周年KV【完成版】.png"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "83c95581-13f0-4f9a-9364-e7fceb5b19f8", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "Shanghai Yunjie Trading Co., Ltd."}}, {"ipId": "61554cb1-8b6e-49c5-9c81-2fc5bf5ca075", "ipName1": "", "ipName2": "", "ipName3": "윤이제", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "🔷🔹️ VLYZ 리더 윤이제 입니다🔹🔷 ", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T08:46:21.774Z", "ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["f3ea79ed-bba2-4dd6-8722-2b35dfa9de7c", "ef18ea97-9695-4a13-b88b-0c6d7cacc048", "2d4c8c76-5b90-4943-b471-944f46a8939e", "6d85334a-0d7a-4e4b-89f6-4221960395c6", "d6ec91f9-a665-459d-86d7-92cbb80d750f"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-15.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-14.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-13.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-12.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-11.jpg"], "fileNames": ["블리즈-15.jpg", "블리즈-14.jpg", "블리즈-13.jpg", "블리즈-12.jpg", "블리즈-11.jpg"], "categories": [{"ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "d6283060-6f3a-449f-805b-63b9f515e2c0", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "블리즈"}}, {"ipId": "644a9398-3178-47da-8be9-5fac130018c7", "ipName1": "GIRL`S FRONTLINE 2: EXILIUM", "ipName2": "", "ipName3": "", "ipName4": "", "ipName5": "", "ipDesc1": "The end of an era, the dawn of another; the fall of a faction, the rise of another... The torchbearers shall shine upon the brave new world.\n\nAfter severing ties with G&K, the Commander bid farewell to the past and chose to venture into the contamination zones. During their journey, the Commander encountered more and more individuals and Tactical Dolls. Each with their own unique stories, they became indispensable members of the Commander's team. The Commander, who only sought to complete bounty missions smoothly and earn a stable income, was unexpectedly ambushed during what appeared to be a routine transport mission. Far from the bustling vortex, it became clear that the Commander had been drawn into an even larger maelstrom...", "ipDesc2": "", "ipDesc3": "", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-28T10:28:05.857Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["7dec6f26-8f4f-48c5-9693-bd88701f2e21"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E6%B5%B7%E5%A4%96%E5%85%AC%E6%B5%8B%E6%A8%AA%E6%9D%BF%E5%8E%BB%E5%AD%97.jpg"], "fileNames": ["海外公测横板去字.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "83c95581-13f0-4f9a-9364-e7fceb5b19f8", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "Shanghai Yunjie Trading Co., Ltd."}}, {"ipId": "647a0f02-2c7d-42e2-a850-51dcdeeab030", "ipName1": "", "ipName2": "", "ipName3": "新世紀エヴァンゲリオンシリーズ", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "新世紀エヴァンゲリオンシリーズ \n신세기 에반게리온 시리즈\n\n\t•\t『新世紀エヴァンゲリオン』\n → 신세기 에반게리온\n\t•\t『新世紀エヴァンゲリオン劇場版 シト新生』\n → 신세기 에반게리온 극장판 사도신생\n\t•\t『新世紀エヴァンゲリオン劇場版／Airまごころを、君に』\n → 신세기 에반게리온 극장판 Air / 진심을, 너에게\n\t•\t『新世紀エヴァンゲリオン劇場版 DEATH (TRUE)2／Air／まごころを、君に』\n → 신세기 에반게리온 극장판 DEATH (TRUE)2 / Air / 진심을, 너에게", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-23T07:27:16.339Z", "ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["0d629ed7-5027-48ae-8c14-c37d74504bab", "6afb8441-3306-42cc-91da-992b252e5473"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E6%96%B0%E4%B8%96%E7%B4%80%E3%82%A8%E3%82%A6%E3%82%99%E3%82%A1%E3%83%B3%E3%82%B1%E3%82%99%E3%83%AA%E3%82%AA%E3%83%B3%E3%82%B7logo.jpeg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E6%96%B0%E4%B8%96%E7%B4%80%E3%82%A8%E3%82%A6%E3%82%99%E3%82%A1%E3%83%B3%E3%82%B1%E3%82%99%E3%83%AA%E3%82%AA%E3%83%B3%E3%82%B7.jpeg"], "fileNames": ["新世紀エヴァンゲリオンシlogo.jpeg", "新世紀エヴァンゲリオンシ.jpeg"], "categories": [{"ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "74450dde-95f7-41c8-b978-536eb3fbb4a9", "ipName1": "", "ipName2": "", "ipName3": "T1 LCK Team Player", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "케리아 (Keria) – 류민석\n포지션: 서포터(SUP)\n생년월일: 2002년 10월 14일\n키: 165cm\n외형: 슬림하고 균형 잡힌 체형, 강아지 상\n무대 스타일: T1 유니폼\n일상 스타일: 캐주얼룩 / 스트릿룩\n성격 키워드: 투명함 / 스마트함 / 재치있음\n특징: 승리 후 손 하트 포즈, 밝고 경쾌한 말투, 팬들에게 장난스럽고 친근한 멘트 자주 사용\n***작품 제작 시 스폰서 로고 제외 필수입니다. ", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-28T12:32:23.878Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["5bd6f68c-3cdd-4894-af45-cd23cd615f32", "265cce8e-86a4-4576-b11a-a461355d573f"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/10801920_%ED%94%84%EB%A1%9C%ED%95%84_%EC%BC%80%EB%A6%AC%EC%95%84.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/241220_GoalStudio_T10636.jpg"], "fileNames": ["10801920_프로필_케리아.jpg", "241220_GoalStudio_T10636.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "8f21785f-dafe-4c6e-9e5d-b46be9375b19", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "SK telecom CS T1"}}, {"ipId": "759c745b-7b05-40b9-b8f0-0b810f86bedb", "ipName1": "", "ipName2": "", "ipName3": "나 혼자 만렙 뉴비", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "*줄거리\n극악의 난이도로 인해 아무도 정상을 보지 못한 게임, [시련의 탑]. 그 탑의 정상은 오직 나만이 알고 있다.\n\n게임 유튜버로 활동 중인 주인공 진혁은 유일하게 [시련의 탑]의 엔딩을 봤지만, 게임의 인기가 하락하며 더 이상 게임 유튜버로서의 삶도 유지하기 힘들어진다. 엔딩을 보았기에 이대로 게임을 마무리하려는 진혁. 바로 그날, [시련의 탑]은 현실이 되었다.\n게임의 모든 요소를 다 알고 있는 진혁은 누구보다 빠르게 모든 것을 차지한다!\n\n\"진짜 고인물이 뭔지 보여주지.\"\n\n*캐릭터(강진혁)\n가상현실게임 [시련의 탑]을 유일하게 클리어 했던 플레이어.\n\n부모에게 버려져 고아원에 들어갔고 온갖 일을 하다 인터넷 방송을 시작하게 되지만 그닥 인기가 없었다. 무려 11년 동안 [시련의 탑]을 플레이하다 27살에 클리어하고 방송을 접으려 했지만 [시련의 탑]이 현실에 등장해 탑을 오르며 얻었던 수많은 경험들과 피지컬을 바탕으로 다시 정복하고자 한다.\n\n특정 조건을 달성하면 적의 능력을 복사할 수 있으며 복사한 능력들을 고유 능력 ‘융합’을 사용하여 새로운 능력을 만들어 낼 수 있다.", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-08T01:09:14.029Z", "ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["707f0cf6-9096-4716-8b8c-255bf766c649", "75851556-a796-41c7-9c96-279e91711e81", "6d340fa3-64c1-4ead-b9e2-57b98e5fd40b"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%EB%82%98%EB%A7%8C%EB%89%B4%201.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%EB%82%98%20%ED%98%BC%EC%9E%90%20%EB%A7%8C%EB%A0%99%20%EB%89%B4%EB%B9%84%202.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%EB%82%98%20%ED%98%BC%EC%9E%90%20%EB%A7%8C%EB%A0%99%20%EB%89%B4%EB%B9%84%203.jpg"], "fileNames": ["나만뉴 1.jpg", "나 혼자 만렙 뉴비 2.jpg", "나 혼자 만렙 뉴비 3.jpg"], "categories": [{"ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "fe20dc83-7184-45af-81e5-e8432066b3f3", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 리버스"}}, {"ipId": "75e4586d-e6e5-4089-8248-1734c8af2e63", "ipName1": "", "ipName2": "", "ipName3": "신 에반게리온 신극장판 / 新ヱヴァンゲリヲン新劇場版", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "신 에반게리온 신극장판 / 新ヱヴァンゲリヲン新劇場版", "ipDesc4": "", "ipDesc5": "", "ipStatus": "I", "createdDate": "2025-05-19T13:53:03.479Z", "ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["2ad0a68d-3d7e-4de8-bbf9-5632d5f9cad0"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%8F%E1%85%A1%E1%84%85%E1%85%A1-02.jpg"], "fileNames": ["카라-02.jpg"], "categories": [{"ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "7904c122-731d-476a-b23f-ca9baea0df91", "ipName1": "", "ipName2": "", "ipName3": "엔드 오브 에반게리온 / 世紀エヴァンゲリオン劇場版 Air/ まこころを、 君に", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "엔드 오브 에반게리온 / 世紀エヴァンゲリオン劇場版 Air/ まこころを、 君に", "ipDesc4": "", "ipDesc5": "", "ipStatus": "I", "createdDate": "2025-05-19T13:54:41.448Z", "ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["7fd4eabd-aea0-49dc-a66a-e01c3bc075ed"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E4%B8%96%E7%B4%80%E3%82%A8%E3%82%A6%E3%82%99%E3%82%A1%E3%83%B3%E3%82%B1%E3%82%99%E3%83%AA%E3%82%AA%E3%83%B3%E5%8A%87%E5%A0%B4%E7%89%88%20Air.png"], "fileNames": ["世紀エヴァンゲリオン劇場版 Air.png"], "categories": [{"ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "872b93ff-a1d5-48d3-b56b-11115eb478f4", "ipName1": "", "ipName2": "", "ipName3": "이주인 / <PERSON>oin <PERSON> / イ・ジュイン", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "1. IP 기본 정보\n버려진 펫숍에 살고 있던 고양이. 인간으로 완벽히 둔갑할 수 있게 된 후 로판에 과몰입해 자신이 여왕이라고 주장하는 중이지만 여전히 고양이급 지능이다.\n\n2. 주요 캐릭터 설명\n - 이름 : 이주인 / <PERSON> / イ・ジュイン\n - 외형적 특징 : 고양이 귀, 고양이 꼬리, 바보털, 오드아이, 고양이 입\n - 대사 스타일 : ~느니라\n - 관계도 및 설정 : 고양이 여왕, 버려진 펫숍 출신\n - 시그니처 포즈/상징물/컬러 : FEF7F1 / 6B0D0D\n\n3. 세계관 및 배경 설명\n - IP주요 무대 : 고양이 왕국\n - 세계관 설정 요약 : 고양이가 인간으로 변신하여 인간들 사이에서 살고 있다.\n - 시대 설정 : 현대\n\n4. 비주얼 디자인 가이드\n - 대표 컬러 / 보조 컬러 : FEF7F1 / 6B0D0D\n\n5. 2차 창작 및 굿즈 제작 가이드\n - 허용되는 제작 범위 : 제한 없음\n - 포현 가능 / 불가능한 방향성 : 지나치게 선정적인 포즈, 폭력적인 연출은 NG", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-29T07:29:41.003Z", "ipCatCode": "O", "ipCatName1": "Other", "ipCatName2": "その他", "ipCatName3": "기타", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["b3c67575-4a8e-4511-80f3-500cbc4b53ad", "e890d285-15d0-424c-9c67-a664dd2813f2", "d047ede6-5590-40cb-9628-b0d741add3ef", "03872f90-170f-423c-8bcf-606f310ffe76"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/1%EB%89%B4%EC%A3%BC%EC%9D%B8%20%EC%82%BC%EB%A9%B4%EB%8F%84.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/1%EC%9D%B4%EC%A3%BC%EC%9D%B8%20%EC%98%A4%EB%A6%AC%EC%A7%80%EB%84%90%20%EC%82%BC%EB%A9%B4%EB%8F%84%28%EA%B8%B0%EB%B3%B8%29.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/13D%20%EC%9D%B4%EC%A3%BC%EC%9D%B8%20%EC%98%A4%EB%A6%AC%EC%A7%80%EB%84%90%20%EC%82%BC%EB%A9%B4%EB%8F%84%20%28%EC%9B%A8%EB%94%A92%29%20.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/13D%20%EC%9D%B4%EC%A3%BC%EC%9D%B8%20%EC%98%A4%EB%A6%AC%EC%A7%80%EB%84%90%20%EC%82%BC%EB%A9%B4%EB%8F%84%28%EC%97%AC%EC%8B%A0%29%20.png"], "fileNames": ["1뉴주인 삼면도.png", "1이주인 오리지널 삼면도(기본).png", "13D 이주인 오리지널 삼면도 (웨딩2) .png", "13D 이주인 오리지널 삼면도(여신) .png"], "categories": [{"ipCatCode": "O", "ipCatName1": "Other", "ipCatName2": "その他", "ipCatName3": "기타", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "eb90c9e0-ef08-4101-8695-34a03ac61427", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 크리에이터버스"}}, {"ipId": "879618dc-a5d9-4050-9316-8be861a593f3", "ipName1": "", "ipName2": "", "ipName3": "사이다 cider", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "*버츄얼 치어리더 \n사이다 : 금발 청안(진한파랑아니고 하늘색). 키  158 마른체형\n소품 : 머리에 캔따개모양 머리핀 + 리본(위치상관 x빨간색을 주로착용)\n복장스타일 : 야구복 혹은 치어리더복 \n성격키워드 : 밝음 , 분노의 야구버튜버  \n대표말투(어록) : 공을 네모안에 넣어 , 그럴거면 컴활을 따 \n허용되는 제작범위 : 무관 \n표현불가능 : R18+ 불가능 ", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-20T06:08:17.86Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["c618ac66-4239-4de2-bf4b-973f302e5c14", "309f2ce4-f2a4-430d-9bc4-530db18a1266", "048bd28e-d3cf-4058-8e8f-3919a0c0cbe5"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%82%AC%EC%9D%B4%EB%8B%A4.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/cider.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EC%8A%A4%ED%81%AC%EB%A6%B0%EC%83%B7%202025-04-10%20131419.png"], "fileNames": ["사이다.png", "cider.png", "스크린샷 2025-04-10 131419.png"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "379cdc91-0915-481c-9185-ef54c6fd9493", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "스튜디오42"}}, {"ipId": "89d38504-1e1f-4aac-aa4c-c223ff8b9ecf", "ipName1": "", "ipName2": "", "ipName3": "Dreamcast", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "▼ 발매연도\n1998년\n\n\n▼ 특징\n엔터테인먼트의 미래를 선도하는 기계로 탄생한 '꿈의 게임기'. 본격적인 온라인 커뮤니케이션 기능을 갖춘 업계 최초의 가정용 게임기입니다. 네트워크 기능을 활용한 온라인 게임도 다수 출시되었습니다.\n\n또한, 휴대성을 고려한 모니터 내장 메모리 카드 '비주얼 메모리', 아케이드 보드 'NAOMI'와의 연동 등 하드 단독으로는 구현되지 않는 확장성도 화제를 모았습니다.\n현재로서는 세가의 마지막 가정용 게임기입니다.\n\n\n▼ 금지 사항\n- 정치적, 종교적 내용을 연상시키는 표현\n- 공공질서 및 윤리에 반하는 표현\n- 다른 저작권에 저촉될 위험이 있는 표현\n- 세가의 심사에서 부적합하거나 허가되지 않은 표현 및 상품", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-15T05:47:52.765Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["6ce918ff-d148-45c7-9ea2-0ea717fc7616"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/sega-03.jpg"], "fileNames": ["sega-03.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "0e6f62ad-5bbb-4e2e-9970-970216285f33", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "SEGA"}}, {"ipId": "8aedb9bb-5c66-4bc7-9623-2483659c8769", "ipName1": "", "ipName2": "", "ipName3": "Umamusume: Pretty Derby / ウマ娘 プリティーダービー", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "Umamusume: Pretty Derby is a cross-media content IP developed by Cygames.", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-20T11:30:00.613Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["c0f6233c-870c-40fc-9212-bc8fbe9a3de1"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%89%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%80%E1%85%A6%E1%84%8B%E1%85%B5%E1%86%B7%E1%84%8C%E1%85%B3-01.jpg"], "fileNames": ["사이게임즈-01.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "8cf01f7c-9f04-497c-95f7-671a02018f9d", "ipName1": "", "ipName2": "", "ipName3": "오즈 리:라이트'(OZ Re:write) - 도로시", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "이름 : 도로시\n나이 :17세\n종족 : 인간\n신장 : 162cm\n체중 : 47kg\n성격 : 청순함, 활발함, 이해심이 많음\n\n캐릭터 소개 \n아버지는 본래 지구인이었고, 지구로 찾아온 「글린다」가 어머니다.\n런던에서 태어난 도로시는 고등학교 때 시부야 도쿄로 전학을 갔으며 그곳에서 「주인공」을 만났다.\n이후 글린다의 부름으로 인해 메르헨 대륙에 도착했고, 먼치킨 왕국의 여왕이 된다.\n위기에 처한 먼치킨 왕국을 도와줄 용사를 소환하며 「주인공」을 다시 만나고, 느닷없이 「주인공」을 용사로 추대해 버린다.\n선량하고 활발한 성품으로 평범한 소녀 같지만, 지구에서는 외롭게 살아온 그녀이기에 다른 사람의 입장과 상황도 깊게 고민하고 배려하며 왕국을 따뜻하게 다스려 간다.\n그녀의 행동 원리는 어디까지나 [먼치킨 왕국의, 아니 이 세상 모든 이들의 행복]을 위해서다.", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T12:34:45.975Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["fa37b60d-86b0-4d98-b2a3-9c362272935c", "675ec06c-133d-409e-98b3-54e978912ba0", "4b112b2e-12a2-455b-a1e1-a98f2c6c0fe3", "306abfbe-2641-45af-a390-af3d5399bee7"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im-13.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im-05.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%83%E1%85%A9%E1%84%85%E1%85%A9%E1%84%89%E1%85%B5_%E1%84%8B%E1%85%B5%E1%86%AF%E1%84%85%E1%85%A5%E1%84%89%E1%85%B3%E1%84%90%E1%85%B3.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%83%E1%85%A9%E1%84%85%E1%85%A9%E1%84%89%E1%85%B5_%E1%84%8F%E1%85%A5%E1%86%AB%E1%84%89%E1%85%A6%E1%86%B8.png"], "fileNames": ["하이브im-13.jpg", "하이브im-05.jpg", "도로시_일러스트.png", "도로시_컨셉.png"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "d9377607-6f94-49f3-b9f8-611504b9bc0e", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 마코빌"}}, {"ipId": "90b5e72d-292a-42b1-a706-4ae5711ad649", "ipName1": "", "ipName2": "", "ipName3": "투미츠", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "✿ 이름: 투미츠\n✿ 나이 : 5000살\n✿ 생일 : 07월 31일\n✿ 첫 데뷔일 : 20년 01월 31일\n✿ MBTI : ENTP\n✿ 팬덤명 : 투찌 (초콜릿🍫)\n✿ 팬닉 : 미츠의OOO\n✿ 오시마크 : 🍫❤\n✿ 팬아트/클립 : #2Mits", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T12:26:46.98Z", "ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["190ba984-73d3-4048-bc11-caec3b4675b9", "69467a9c-01a7-4814-91b7-ef00347ebb79"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%8F%E1%85%B3%E1%84%85%E1%85%A6%E1%84%87%E1%85%B5%E1%84%8C%E1%85%B2_img_%E1%84%83%E1%85%A2%E1%84%8C%E1%85%B5%201.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%8F%E1%85%B3%E1%84%85%E1%85%A6%E1%84%87%E1%85%B5%E1%84%8C%E1%85%B2_img-02.jpg"], "fileNames": ["크레비쥬_img_대지 1.jpg", "크레비쥬_img-02.jpg"], "categories": [{"ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "0e8e1fb1-8130-4c21-ade0-09f5c089a4b3", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "크레비쥬"}}, {"ipId": "9128c702-14ea-485b-a810-5a4d12d6c881", "ipName1": "", "ipName2": "", "ipName3": "T1 LCK Team Player", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "오너 (Oner) – 문현준\n포지션: 정글(JUG)\n생년월일: 2002년 12월 24일\n키: 178cm\n외형: 슬림+단단한 체형\n무대 스타일: T1 유니폼\n일상 스타일: 댄디룩, 스트릿 룩\n성격 키워드: 침착함 / 집중력 / 조용한 리더십 / 믿음직함\n특징: 차분하고 신중한 말투, 인터뷰 시에도 감정을 크게 드러내지 않고 조리 있게 답변\n***작품 제작 시 스폰서 로고 제외 필수입니다. ", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-28T12:30:01.037Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["eec737e9-60f3-4c16-b5e3-89c83746c23e", "986691ad-938c-4692-b220-6e0ecd4b71dd"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/10801920_%ED%94%84%EB%A1%9C%ED%95%84_%EC%98%A4%EB%84%88.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/241220_GoalStudio_T10130.jpg"], "fileNames": ["10801920_프로필_오너.jpg", "241220_GoalStudio_T10130.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "8f21785f-dafe-4c6e-9e5d-b46be9375b19", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "SK telecom CS T1"}}, {"ipId": "919ec32e-7ad8-4aa7-8f92-7d78a694962c", "ipName1": "", "ipName2": "", "ipName3": "뿌요뿌요", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "▼개요\n『뿌요뿌요』는 색깔이 다양한 '뿌요'를 4개 이상 연결하여 없애는 퍼즐 게임으로, 연쇄에 의한 상쾌함과 상대에게 '방해뿌요'를 보내는 전략성이 특징입니다. \n\n간단한 규칙이지만 깊이 있는 승부를 즐길 수 있어, 초보자부터 숙련자까지 폭넓게 지지를 받습니다. \n\n현재도 대전 모드와 다양한 캐릭터들로 발전을 거듭하는 인기 시리즈입니다.\n\n\n▼금지 사항\n- 정치적, 종교적 내용을 연상시키는 표현\n- 공공질서와 윤리에 반하는 표현\n- 다른 저작권에 저촉될 수 있는 표현\n- 성적 표현 또는 음주, 담배와 관련된 표현\n- 동일 색상의 '뿌요'를 4개 이상 연결하는 표현\n- 세가의 심사에서 부적합하거나 허가되지 않는 표현 및 상품", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-15T05:46:38.572Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["b40c9376-e6c6-4618-94d9-596ed3392c73"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/sega-02.jpg"], "fileNames": ["sega-02.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "0e6f62ad-5bbb-4e2e-9970-970216285f33", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "SEGA"}}, {"ipId": "94aa41c5-0684-469f-b5b4-0b3257202ceb", "ipName1": "", "ipName2": "ｷｬﾗｸﾀｰ･ﾎﾞｰｶﾙ･ｼﾘｰｽﾞ01　初音ミク", "ipName3": "", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "1. IP基本情報\n初音ミク　(Hatsune Miku)\nクリプトン・フューチャー・メディア株式会社が開発した、歌詞とメロディーを入力して誰でも歌を歌わせることができる「ソフトウェア」です。 大勢のクリエイターが「初音ミク」で音楽を作り、インターネット上に投稿したことで一躍ムーブメントとなりました。\n「キャラクター」としても注目を集め、今ではバーチャルシンガーとしてグッズ展開やライブを行うなど多方面で活躍するようになり、人気は世界に拡がっています。\n\n2. キャラクター説明\n・年齢：16歳\n・身長：158cm\n・体重：42kg\n・イメージカラー：ブルーグリーン\n\n3. 世界観および背景説明\n・指定無し\n\n4. ビジュアルデザインガイド\n・ブルーグリーンの長いツインテールにヘッドセットが特徴\n・左腕には01のナンバリング\n\n5. 二次創作およびグッズ制作ガイド\n【NGな内容】\n・エロ・グロ・公序良俗に反するもの、政治・宗教を連想させるもの、下着が見えるもの\n・「VOCALOID」または「ボーカロイド」の名称の利用\n\n6. サンプル提出\n・本申請の承認時に前面から撮影した写真サンプルを1枚ご提出ください。", "ipDesc3": "", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-20T01:54:03.408Z", "ipCatCode": "M", "ipCatName1": "Music", "ipCatName2": "音楽", "ipCatName3": "음악", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["2bb66627-e020-4d32-a780-6aeb14c46a1e", "8bcbcd50-c058-4a84-9c7d-4916a7901bae"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%5Bcredit-RGB%5D_HATSUNE_MIKU_resized.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/Hatsune%20Miku.jpg"], "fileNames": ["[credit-RGB]_HATSUNE_MIKU_resized.jpg", "Hatsune Miku.jpg"], "categories": [{"ipCatCode": "M", "ipCatName1": "Music", "ipCatName2": "音楽", "ipCatName3": "음악", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "5cb2393a-c71b-4261-98d3-c3b1801168a9", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "Crypton Future Media, INC."}}, {"ipId": "97695459-6d66-4a50-9831-d4861fd7e6a5", "ipName1": "", "ipName2": "", "ipName3": "에픽세븐 (Epic Seven)", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "PLAY THE ANIMATION! 내 손으로 플레이 하는 한 편의 애니메이션 '에픽세븐' \n전세계 300만 사용자가 즐기는 에픽세븐은 풀 프레임 2D 애니메이션 연출이 매력적인 턴제 RPG 입니다. \n\n원더 페스티벌에 출전하는 모든 굿즈 창작자 여러분! 반갑습니다!\n에픽세븐에 등장하는 수많은 영웅, 아티팩트, 배경, 펫 등 모든 소재로 출품하실 수 있습니다. 많은 관심을 가져주셔서 감사드립니다.\n\n단, 에픽세븐과 콜라보레이션으로 제작된 저작물은 출품이 불가합니다. \n대표적으로 다음 영웅과 아티팩트가 해당되며, 콜라보레이션 한정 펫, 포스터, 일러스트 역시 불가하오니 유의해주시기 바랍니다.\n\n1. 영웅(캐릭터): 솔, 엘페르트, 디지, 바이켄, 잭 오, 키즈나 아이, 렘, 람, 에밀리아, 리무루, 슈나, 밀림, 베니마루, 에드워드 엘릭, 로이 머스탱, 리자 호크아이, ae-카리나, ae-윈터, ae-지젤, ae-닝닝, 아인즈 울 고운, 알베도, 샤르티아\n\n2. 아티팩트: 정크야드 도그, 푸른빛 혜성, 항마의 가면, 잭 오의 상징, 강철의 오토메일, 베니마루의 도, 스포이트 랜스, 로켓 펀치, 3F, 아머먼트, 명사수의 권총, 네크로 & 운디네, 잘려진 뿔 지팡이, 개량형 드래곤 너클, 발화포 장갑, 빛의 프레임, 스태프 오브 아인즈 울 고운, 수호의 얼음 파편, 천의무봉, EXIF Detective(E.d.)\n\n‘창작 굿즈’ 본품, ‘창작 굿즈’ 패키지, 그리고 상세페이지 본문에는 회사의 공식(Official) 제품이 아님을 표시하기 위해 반드시 아래 문구를 명기해야 하며, \n‘창작 굿즈’ 전용 로고는 필요에 따라 적용 유무를 선택할 수 있습니다.\n\n- 기본형: Epic Seven Non-official Goods Made by Fan User\n- 축약형: E7 FANMADE (공간 제약으로 인해 기본형 기재 불가 시 사용)\n- ‘창작 굿즈’ 전용 로고 다운로드 링크 : https://page.onstove.com/epicseven/kr/view/10182713\n\n다시 한 번 에픽세븐으로 참여해주시는 점 감사드리며, 에픽세븐은 UGC 정책도 운영하고 있으니 많은 관심 부탁드립니다.\n(이메일 문의: 에픽세븐_UGC <<EMAIL>>)", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-19T09:45:10.787Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["9a480cee-9729-42b1-a03f-6e1cc8cc0e8e", "602228fb-6730-44a8-bbf8-08c089235847", "18d50226-0d2e-41d7-ab87-4ab7d280d9a5", "d264cd70-3494-4791-8706-46434a19ef74", "b85f61f1-fcab-4849-9260-17e3bd5e9c66"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/6%20%281%29.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/3%20%281%29.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/4%20%282%29.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/2%20%281%29.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/1%20%281%29.jpg"], "fileNames": ["6 (1).jpg", "3 (1).jpg", "4 (2).jpg", "2 (1).jpg", "1 (1).jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "6153e82c-cd44-4a3b-8066-33f17ed599a0", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "스마일게이트 홀딩스 메가포트지점"}}, {"ipId": "99e40fa1-9cdc-40c0-a3b5-edffb9e239b3", "ipName1": "", "ipName2": "", "ipName3": "오즈 리:라이트'(OZ Re:write) - 스노우", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "이름 : 스노우\n나이 : 19세\n종족 : 인간\n신장 : 177cm\n체중 : 56kg\n성격 : 권위적, 희생적, 리더쉽\n\n캐릭터 소개\t\n스노우는 69년 전 가장 먼저 메르헨 대륙으로 건너온 「조율자」이다.\n순종적이고 순수했던 스노우가 「압펠하임」을 방문했을 때의 이곳은 예술과 문화가 있는 \n아름다운 도시였다. 이런 외부에 개방된 형태의 도시가 못마땅했던 어머니인 「율리아」는 \n스노우의 조율자의 힘을 이용해 도시 주변에 높은 방벽을 세웠다. \n그렇게 20년이 지나고 어머니인 「율리아」의 명령으로 압펠하임에 학교를 짓게 되었고 그때 \n스노우는 처음으로 반항이라는 것을 하였다. 그녀가 지구에서 동경했던 학교를 「율리아」가 \n통제의 수단으로 사용할 목적이었다는 것에 큰 충격을 받은 것이다.\n「율리아」 또한 본인에게 반항하는 스노우에 분노하였고 이에 의해 마찰이 일어나고 만다.\n본인의 어머니인 「율리아」를 봉인해 버리고 만 스노우는 사건 이후 다른 사람이 된 것처럼 변해 \n버리고 말았다", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T12:53:05.116Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["5ca9751e-1624-4a1d-bbf8-3ba5ec601810", "b70f20b1-240d-44e1-87cb-b235abf605f8", "2ce5acdf-8a60-40e3-bb2e-bd7292ad253f", "10926855-6ccd-445f-808d-1f881756a76b"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im-16.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im-04.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%89%E1%85%B3%E1%84%82%E1%85%A9%E1%84%8B%E1%85%AE_%E1%84%8B%E1%85%B5%E1%86%AF%E1%84%85%E1%85%A5%E1%84%89%E1%85%B3%E1%84%90%E1%85%B3.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%89%E1%85%B3%E1%84%82%E1%85%A9%E1%84%8B%E1%85%AE_%E1%84%8F%E1%85%A5%E1%86%AB%E1%84%89%E1%85%A6%E1%86%B8.png"], "fileNames": ["하이브im-16.jpg", "하이브im-04.jpg", "스노우_일러스트.png", "스노우_컨셉.png"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "d9377607-6f94-49f3-b9f8-611504b9bc0e", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 마코빌"}}, {"ipId": "9c6e4f69-832d-4f8a-84ec-382f38c46075", "ipName1": "", "ipName2": "", "ipName3": "이레인", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "러블리 더블리 블리즈 이레인입니다♥", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T08:48:37.712Z", "ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["8918cbd9-27f1-4922-b0f3-53f1c902b355", "ae487a55-3641-4f81-a6f2-afeaca8d745c", "2a77f3a8-7115-499d-bcfb-fce85e3c1152", "b2ecdea2-da54-4cc0-a5b8-5f77dd8c215a", "0197dc91-1e07-4311-95d6-1365c1eb1141"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-20.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-19.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-18.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-17.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-16.jpg"], "fileNames": ["블리즈-20.jpg", "블리즈-19.jpg", "블리즈-18.jpg", "블리즈-17.jpg", "블리즈-16.jpg"], "categories": [{"ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "fad11d98-6c3f-4a44-8607-aaa6fc4fd79d", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "이레인"}}, {"ipId": "9fdc4486-aab8-495d-8ddc-b23fe745b929", "ipName1": "", "ipName2": "", "ipName3": "그랑블루 판타지 / グランブルーファンタジー", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": " ", "ipDesc4": "", "ipDesc5": "", "ipStatus": "I", "createdDate": "2025-05-19T06:33:57.944Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["27e6ae2f-6ad1-41c9-850b-949b5391e52d"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/2%E1%84%8E%E1%85%A1_04.jpg"], "fileNames": ["2차_04.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "a5cb511d-27e0-4f16-9e3f-334615e62f1d", "ipName1": "", "ipName2": "", "ipName3": "노이 / Noi / ノイ", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "1. IP 기본 정보\n매드 사이언티스트\n반인류적, 위험한 실험들을 연구하며 자신이 내키는 연구만을 진행한다.\n실험을 위해서라면 자신에게 실험 하는 것도 서슴치 않는다.\n더 다양한 실험 재료와 주제를 찾고 싶어 한다.\n\n2. 주요 캐릭터 설명\n - 이름 : 노이 / Noi / ノイ\n - 외형적 특징 : \n키 143(더듬이 포함), 키는 반올림 해서 150라고 얘기하는 편\n체형 : 로리 체형, 빈유\n머리 색상 : 4487a1 / b47ada\n피부색 : fff5eb / f5b7af\n특징적 소품 : 슬라임(팬 케릭터), 포션 유리병(실험용 실린더), 안경(원형)\n복장 : 오버 사이즈 코드(실험복 / 473869 / 6e356b), 멜빵 반바지, 과학자\n - 대사 스타일 : 것이다(のだ) 말투를 즐겨 쓴다\n - 관계도 및 설정 : 보부상(리미)에게 실험 재료 조달 / 에스퍼(라즈)에게 정보 빼돌리기\n - 시그니처 포즈/상징물/컬러 : 물약병, 해골 머리핀 / 4487A1\n\n3. 세계관 및 배경 설명\n - IP주요 무대 : 판타지 세상\n - 세계관 설정 요약 : 마법과 이종족이 존재하는 이세계\n - 시대 설정 : 중세풍 판타지\n\n4. 비주얼 디자인 가이드\n - 대표 컬러 / 보조 컬러 : 4487A1 / b47ada\n\n5. 2차 창작 및 굿즈 제작 가이드\n - 허용되는 제작 범위 : 제한 없음\n - 포현 가능 / 불가능한 방향성 : 지나치게 선정적인 포즈, 폭력적인 연출은 NG", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T02:34:25.783Z", "ipCatCode": "O", "ipCatName1": "Other", "ipCatName2": "その他", "ipCatName3": "기타", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["350440fa-5da4-4726-b015-bc19f6dccab7"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1%EB%85%B8%EC%9D%B4%202-5.png"], "fileNames": ["1노이 2-5.png"], "categories": [{"ipCatCode": "O", "ipCatName1": "Other", "ipCatName2": "その他", "ipCatName3": "기타", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "eb90c9e0-ef08-4101-8695-34a03ac61427", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 크리에이터버스"}}, {"ipId": "ac840c03-ca5d-4d5b-a100-49adcd709879", "ipName1": "", "ipName2": "", "ipName3": "울트라맨(Ultraman)/스타일 가이드 발송", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "1. IP 기본 정보\n울트라맨(Ultraman)은 1966년 일본 츠부라야 프로덕션(Tsuburaya Productions)이 창조한 특촬 히어로 캐릭터로, 반세기가 넘는 시간 동안 지속적인 콘텐츠 확장과 팬층 확보를 이루어낸 대표적인 글로벌 IP입니다.\n\n울트라맨은 '빛의 전사'라는 독자적인 세계관을 바탕으로, 다양한 시리즈와 캐릭터 라인업을 구축해왔습니다. 현재까지 수십 개의 TV 시리즈, 영화, 애니메이션, 만화, 게임, 완구 등으로 전개되며 멀티 플랫폼 프랜차이즈로 자리잡았습니다. 특히 각기 다른 성격과 스토리를 가진 울트라 히어로들이 세대를 넘나들며 등장해, 끊임없는 리부트와 확장을 가능하게 합니다.\n\n브랜드로서의 울트라맨은 일본뿐 아니라 아시아 전역, 북미, 유럽 등 세계 시장에서도 인지도를 갖춘 IP입니다. \n\n울트라맨은 단순한 히어로 캐릭터를 넘어, 정의와 희생, 연대의 가치를 전달하는 상징적인 존재로서의 정체성을 구축하며, 시대에 맞춘 진화를 통해 지속 가능한 콘텐츠 생태계를 형성해가고 있는 강력한 IP입니다.\n\n2. 신청 가능 시리즈 (개별 스타일 가이드 참조)\nULTRAMAN OMEGA\nULTRAMAN ARC\nULTRAMAN BLAZAR\nULTRAMAN DECKER\nULTRAMAN REGULOS\nULTRAMAN TRIGGER\nULTRAMAN Z\nULTRAMAN RIBUT\nULTRAMAN FUMA\nULTRAMAN TITAS\nULTRAMAN TAIGA\nULTRAWOMAN GRIGIO\nULTRAMAN BLU\nULTRAMAN ROSSO\nULTRAMAN GEED\nULTRAMAN ORB\nULTRAMAN X\nULTRAMAN VICTORY\nULTRAMAN GINGA\nULTRAMAN ZERO\nULTRAMAN HIKARI\nULTRAMAN MEBIUS\nULTRAMAN XENON\nULTRAMAN MAX\nULTRAMAN NEXUS\nULTRAMAN JUSTICE\nULTRAMAN COSMOS\nULTRASEVEN 21\nULTRAMAN NEOS\nULTRAMAN AGUL\nULTRAMAN GAIA\nULTRAMAN DYNA\nULTRAMAN TIGA\nULTRAMAN POWERED\nULTRAMAN GREAT\nULTRAMAN CHUCK\nULTRAWOMAN BETH\nULTRAMAN SCOTT\nYULLIAN\nULTRAMAN 80\nULTRAMAN JONEUS\nULTRAMAN KING\nASTRA\nULTRAMAN LEO\nMOTHER OF ULTRA\nFATHER OF ULTRA\nULTRAMAN TARO\nULTRAMAN ACE\nULTRAMAN JACK\nULTRASEVEN\nZOFFY\nULTRAMAN\n\n스타일 가이드 수령 후:\n수령하신 스타일 가이드를 충분히 검토한 뒤, 실제 제품 기획안과 디자인을 반영하여 신규 제품 신청을 다시 진행해 주시기 바랍니다.\n\n3. 2차 창작 및 굿즈 제작 가이드\n• 허용되는 제작 범위(피규어, 아크릴 스탠드, 지류)\n\n4. 샘플 제출 :\n본신청 승인 시 완제품 샘플 2개를 의무 제출하여야 합니다.", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-15T02:23:48.985Z", "ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["c6b65e45-86cb-429b-825f-a92cc18326d4", "89279905-0a59-422e-9bb5-367fe59d594f"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/Ultraman%20Omega_ko.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/Teaser%20visual_yoko_Ko.png"], "fileNames": ["Ultraman Omega_ko.png", "Teaser visual_yoko_Ko.png"], "categories": [{"ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "ef55c46c-0f8f-49fd-a2d3-d9462edaeba0", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "SLP Inc."}}, {"ipId": "af4102dd-5506-457e-8643-1198bf3d558c", "ipName1": "", "ipName2": "", "ipName3": "신세기 에반게리온 / 新世紀エヴァンゲリオン", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "신세기 에반게리온 / 新世紀エヴァンゲリオン", "ipDesc4": "", "ipDesc5": "", "ipStatus": "I", "createdDate": "2025-05-19T13:53:31.184Z", "ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["a5d97062-0239-46c7-8dba-6cb70147946a"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%8F%E1%85%A1%E1%84%85%E1%85%A1-04.jpg"], "fileNames": ["카라-04.jpg"], "categories": [{"ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "b06d87bc-61bf-4e7a-ba33-263e90cf2aba", "ipName1": "", "ipName2": "", "ipName3": "시트리 / Sirty / シートリー", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "1. IP 기본 정보\n부유섬의 낮잠요정.\n백야현상이 있는 부유섬에서 중요한 낮잠 요정이라는 역할을 하고 있다.\n새로운 이들을 만나서 능력을 마음껏 발휘 하고 싶어 한다.\n\n2. 주요 캐릭터 설명\n - 이름 : 시트리 / Sirty / シートリー\n - 외형적 특징 :  \n키 : 155\n체형 : 가녀린 소녀 체형, 빈유도 거유도 아님\n머리 색상 : f9fafc / f8c4ae\n피부색 : fffafa / edd3d2\n특징적 소품 : 구름 날개, 헤어 브릿지\n복장 : 드레스, 청록색 반망토\n - 특징적 소품 : 구름 날개, 헤어 브릿지\n - 복장 : 드레스, 청록색 반망토\n - 대사 스타일 : 없음\n - 관계도 및 설정 : 부유섬의 낮잠요정\n - 시그니처 포즈/상징물/컬러 : 구름 날개, 헤어 브릿지 / fdcbb4\n\n3. 세계관 및 배경 설명\n - IP주요 무대 : 판타지 세상\n - 세계관 설정 요약 : 마법과 이종족이 존재하는 이세계\n - 시대 설정 : 중세풍 판타지\n\n4. 비주얼 디자인 가이드\n - 대표 컬러 / 보조 컬러 :  fdcbb4 / cfecd6\n\n5. 2차 창작 및 굿즈 제작 가이드\n - 허용되는 제작 범위 : 제한 없음\n - 포현 가능 / 불가능한 방향성 : 지나치게 선정적인 포즈, 폭력적인 연출은 NG", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T02:32:36.102Z", "ipCatCode": "O", "ipCatName1": "Other", "ipCatName2": "その他", "ipCatName3": "기타", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["83dfa7e2-d6ed-4af1-978f-369277d02450", "af35c1e1-b759-4641-a7e7-9ca966c35ded"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1%EC%8B%9C%ED%8A%B8%EB%A6%AC%202-5.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1sitry_2D_2nd_%EC%82%BC%EB%A9%B4%EB%8F%84.png"], "fileNames": ["1시트리 2-5.jpg", "1sitry_2D_2nd_삼면도.png"], "categories": [{"ipCatCode": "O", "ipCatName1": "Other", "ipCatName2": "その他", "ipCatName3": "기타", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "eb90c9e0-ef08-4101-8695-34a03ac61427", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 크리에이터버스"}}, {"ipId": "b0e7459b-736e-4fc0-baa1-7511eae27fe0", "ipName1": "", "ipName2": "", "ipName3": "아마이 유키", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "❄️ 이름 : 아마이 유키(달콤히 내리는 눈) \n❄️ 데뷔: 2024.10.21 \n❄️ 나이 : 21살 \n❄️ 키 : 156cm \n❄️ 생일 : 1월 5일\n❄️ MBTI : ISTJ\n❄️ 팬닉 : *닉네임*\n❄️ 팬애칭 : 단눈\n", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T12:16:07.852Z", "ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["ddbbfaf3-f4a6-4265-a78f-33558102c354", "845513c7-0f58-4d48-ba51-8cc5406394ec"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%8F%E1%85%B3%E1%84%85%E1%85%A6%E1%84%87%E1%85%B5%E1%84%8C%E1%85%B2_img-04.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%8F%E1%85%B3%E1%84%85%E1%85%A6%E1%84%87%E1%85%B5%E1%84%8C%E1%85%B2_img-03.jpg"], "fileNames": ["크레비쥬_img-04.jpg", "크레비쥬_img-03.jpg"], "categories": [{"ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "0e8e1fb1-8130-4c21-ade0-09f5c089a4b3", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "크레비쥬"}}, {"ipId": "b770fa73-c4ee-4af1-94f1-3323bf486e02", "ipName1": "", "ipName2": "", "ipName3": "라즈 / Raz / ラズ", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "1. IP 기본 정보\n에스퍼, 정보원\n눈으로 마음을 읽는 정보원.\n부정적인 마음을 많이 보게 되어 사고방식이 남들과 다르다.\n'innovill'에 스카웃 되어 정보를 캐내는 일을 하고 있다.\n\n2. 주요 캐릭터 설명\n - 이름 : 라즈 / Raz / ラズ\n - 외형적 특징 :  \n키 : 163\n체형 : 마른 체형, 풍만한 가슴(벨디르 보다는 작다)\n머리 색상 : 403758 / d9d9e4\n피부색 : f4e4de / f1c8c3\n특징적 소품 : 금장식 / 보석\n복장 : 오버 사이즈 외투(퍼) / 오른쪽에만 신은 망사 스타킹\n - 대사 스타일 : 존댓말\n - 관계도 및 설정 : 매드사이언티스트(노이)에게 정보 탈취 당하기\n - 시그니처 포즈/상징물/컬러 : 구슬 / 403758\n\n3. 세계관 및 배경 설명\n - IP주요 무대 : 판타지 세상\n - 세계관 설정 요약 : 마법과 이종족이 존재하는 이세계\n - 시대 설정 : 중세풍 판타지\n\n4. 비주얼 디자인 가이드\n - 대표 컬러 / 보조 컬러 :  403758 / d9d9e4\n\n5. 2차 창작 및 굿즈 제작 가이드\n - 허용되는 제작 범위 : 제한 없음\n - 포현 가능 / 불가능한 방향성 : 지나치게 선정적인 포즈, 폭력적인 연출은 NG", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T02:37:57.979Z", "ipCatCode": "O", "ipCatName1": "Other", "ipCatName2": "その他", "ipCatName3": "기타", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["b5430570-15eb-4f9f-a93e-2411c1988d3f"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/1%EB%9D%BC%EC%A6%88%202-5.jpg"], "fileNames": ["1라즈 2-5.jpg"], "categories": [{"ipCatCode": "O", "ipCatName1": "Other", "ipCatName2": "その他", "ipCatName3": "기타", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "eb90c9e0-ef08-4101-8695-34a03ac61427", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 크리에이터버스"}}, {"ipId": "c246f166-5a9f-4c91-8138-d14be11deed1", "ipName1": "", "ipName2": "", "ipName3": "프린세스 커넥트! Re:Dive / プリンセスコネクト！ Re:Dive", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": " ", "ipDesc4": "", "ipDesc5": "", "ipStatus": "I", "createdDate": "2025-05-19T12:52:17.598Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["54308199-f14b-4c80-b93f-6a3077bcaada"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/2%E1%84%8E%E1%85%A1_02.jpg"], "fileNames": ["2차_02.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "c33d15b8-64e8-42f4-9f00-e1d6f920cc79", "ipName1": "", "ipName2": "", "ipName3": "T1 LCK Team Player", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "도란 (<PERSON><PERSON>) – 최현준\n포지션: 탑(TOP)\n생년월일: 2000년 7월 22일\n키: 182cm\n외형: 큰 키에 마른 체형, 부드럽고 선한 인상\n무대 스타일: T1 유니폼\n일상 스타일: 캐주얼룩 선호\n성격 키워드: 성숙함 / 안정감 / 부드러움\n특징: 다정한 말투, 팬들과의 소통에서도 따뜻하고 진심 어린 답변 \n***작품 제작 시 스폰서 로고 제외 필수입니다. ", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-28T12:24:39.727Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["85731aba-e0ef-4c4c-b01f-c9abd56ff090", "03792cad-947b-477f-9d54-b14aaa18c89e"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/10801920_%ED%94%84%EB%A1%9C%ED%95%84_%EB%8F%84%EB%9E%80.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/241220_GoalStudio_T10017.jpg"], "fileNames": ["10801920_프로필_도란.jpg", "241220_GoalStudio_T10017.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "8f21785f-dafe-4c6e-9e5d-b46be9375b19", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "SK telecom CS T1"}}, {"ipId": "c35f388d-ddfa-4ea0-8aea-fd9a2e74a5f6", "ipName1": "", "ipName2": "", "ipName3": "이아나 이그노시스 / <PERSON>a <PERSON> / イアナ • イグノシス", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "1. IP 기본 정보\n노래를 즐기는 빛의 드래곤.\n사람의 손을 타지 않은 동굴 '데드케이브' 끝자락에서 살고 있다.\n그녀의 노래는 한 줄기 빛이 되어 사람들의 마음을 따뜻하게 만들어준다.\n\n2. 주요 캐릭터 설명\n - 이름 : 이아나 이그노시스 / Iana Ignosis / イアナ • イグノシス\n - 외형적 특징 :  \n키 : 160 (뿔 포함 169)\n체형 : 전체적으로 호리호리함\n머리 색상 : 4487a1 / b47ada\n피부색 : fff5eb / f5b7af\n특징적 소품 : 용 꼬리, 용 뿔, 음표 모양 머리 장식\n복장 : 흰색 드레스\n - 대사 스타일 : 없음\n - 관계도 및 설정 : 사람의 손을 타지 않은 동굴 '데드케이브' 끝자락에서 살고 있음\n - 시그니처 포즈/상징물/컬러 : 용 꼬리, 용 뿔, 음표 모양 머리 장식 / ffd736\n\n3. 세계관 및 배경 설명\n - IP주요 무대 : 판타지 세상\n - 세계관 설정 요약 : 마법과 이종족이 존재하는 이세계\n - 시대 설정 : 중세풍 판타지\n\n4. 비주얼 디자인 가이드\n - 대표 컬러 / 보조 컬러 :  ffeea6 / f0f1f5\n\n5. 2차 창작 및 굿즈 제작 가이드\n - 허용되는 제작 범위 : 제한 없음\n - 포현 가능 / 불가능한 방향성 : 지나치게 선정적인 포즈, 폭력적인 연출은 NG", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T02:23:25.193Z", "ipCatCode": "O", "ipCatName1": "Other", "ipCatName2": "その他", "ipCatName3": "기타", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["72d981f4-59db-4f13-bfee-84331f5451e6", "345102ce-2876-4d71-9e4f-e596c1b8c6da", "ba441e1a-2d7a-4502-8288-e80bafb5a8e3", "e1c8a305-dd26-4f59-b4c0-0f013035a14a"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/1%EC%9D%B4%EC%95%84%EB%82%98%20%EC%9D%B4%EA%B7%B8%EB%85%B8%EC%8B%9C%EC%8A%A4%202-5.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/1iana_2D_2nd_%EC%82%BC%EB%A9%B4%EB%8F%84%20%28small%29.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/1iana_2D_3rd_%EC%82%BC%EB%A9%B4%EB%8F%84.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/1iana_2D_4th_%EC%82%BC%EB%A9%B4%EB%8F%84%20%28KR%29.png"], "fileNames": ["1이아나 이그노시스 2-5.jpg", "1iana_2D_2nd_삼면도 (small).png", "1iana_2D_3rd_삼면도.png", "1iana_2D_4th_삼면도 (KR).png"], "categories": [{"ipCatCode": "O", "ipCatName1": "Other", "ipCatName2": "その他", "ipCatName3": "기타", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "eb90c9e0-ef08-4101-8695-34a03ac61427", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 크리에이터버스"}}, {"ipId": "cc7db88e-c341-4850-a2af-0587d604ccc2", "ipName1": "", "ipName2": "", "ipName3": "비체", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "단테랑 노는 게 제일 좋은 고양이 비체 등장!\n💜 비체 (viche)💜\n💜 생일 : 11월 12일\n💜 MBTI : ENTP\n💜나이 : 4살 (2n살)\n💜 팬이름: 단테\n", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T12:25:13.922Z", "ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["eac25a2e-8eb0-4175-9530-ea36cd757017", "e8d5d7f7-4461-428b-9960-e96d8bcb35ca"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%8F%E1%85%B3%E1%84%85%E1%85%A6%E1%84%87%E1%85%B5%E1%84%8C%E1%85%B2_img-06.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%8F%E1%85%B3%E1%84%85%E1%85%A6%E1%84%87%E1%85%B5%E1%84%8C%E1%85%B2_img-05.jpg"], "fileNames": ["크레비쥬_img-06.jpg", "크레비쥬_img-05.jpg"], "categories": [{"ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "0e8e1fb1-8130-4c21-ade0-09f5c089a4b3", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "크레비쥬"}}, {"ipId": "ccec78d4-455b-4919-89cd-f61cc6379921", "ipName1": "", "ipName2": "", "ipName3": "Granblue Fantasy / グランブルーファンタジー", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "A classic JRPG that takes you to heights beyond imagination, all from your phone.", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-20T11:32:23.936Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["8026052f-f4c8-4e40-8747-9e4c58b15c2a"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%89%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%80%E1%85%A6%E1%84%8B%E1%85%B5%E1%86%B7%E1%84%8C%E1%85%B3-02.jpg"], "fileNames": ["사이게임즈-02.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "d786ee0c-5231-4639-92d7-d3085ebdca64", "ipName1": "", "ipName2": "", "ipName3": "오즈 리:라이트'(OZ Re:write) - 코네티", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "이름 : 코네티\n나이 : 499세\n종족 : ?\n신장 : 110cm\n체중 : 11kg\n성격 : 명랑, 호기심 많음\n\n캐릭터 소개\t\n주인공이 메르헨에 도착한 후부터 주인공을 도와주던 정체불명의 소녀.\n메르헨의 역사 및 단체, 국가는 물론이고 인간 관계에도 모두 이해하고 있어서 주인공의 메르헨 \n생활에 적응하는 데 많은 도움을 준다. \n상냥하고 붙임성이 있으며, 세상 만사에 호기심도 많은 발랄한 소녀로, 전투력은 전무하지만 \n메르헨의 고유 통신 시스템인 ‘미러그램’과 현실 세계를 자유롭게 넘나드는 능력이 있다. \n정체는 메르헨을 창조한 오즈가 미러그램의 코드를 기반으로 만들어낸 일종의 기인으로 다른 \n기인과는 다르게 전파와 코드를 기반으로 제작되어 육체가 없으며, 자신이 보이고 싶은 \n상대에게만 보이도록 할 수 있다.\n메르헨의 탄생과 함께 생겨나 세계의 모든 것을 기록해 온 존재로, 「주인공」에게 새로운 \n오즈임을 알려 준다", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T12:39:20.761Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["c7748653-fcd5-4fdd-b6be-ccfe62473b1b", "12374755-0f4a-4e42-a355-f315ee278863"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im-11.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im_%E1%84%83%E1%85%A2%E1%84%8C%E1%85%B5%201.jpg"], "fileNames": ["하이브im-11.jpg", "하이브im_대지 1.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "d9377607-6f94-49f3-b9f8-611504b9bc0e", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 마코빌"}}, {"ipId": "e01a9b82-cd59-4004-a42c-5e83115e5b57", "ipName1": "", "ipName2": "", "ipName3": "오즈 리:라이트'(OZ Re:write) - 린", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "이름 : 린\n나이 : 150세\n종족 : 마녀\n신장 : 137cm\n체중 : 27kg\n성격 : 내성적, 낮가림, 감정기복이 심함\n\n캐릭터 소개\t\n린은 유일하게 메르헨 대륙에서 태어난 리라이터이다.\n정확하게 어떻게 태어나고, 스네헤이에 오게 되었는지는 알려져 있지 않는다. 약 14년 전 사이베리아 북부의 얼음 속에서 발견되었고 의식을 찾자마자 「스네헤이」를 리라이팅해 거대 도시를 만들었다.\n지하 자원도 자연 환경도 풍족하지 않은 사이베리아에서 「스네헤이」에 대한 의존도가 매우 높아지고 린이 구세주로 추앙받으며 신흥 종교인 「진리교단」이 탄생한다. \n그렇게 평화롭기만 한 사이베리아에 2년 전부터 변화가 찾아온다. 린의 주변에 새로운 여성이 나타난 이후로 스네헤이에는 점차적으로 이상 기온이 찾아오더니 결국 도시 전체가 얼어붙은 얼음의 도시가 되고 만 것이다. 사이베리아는 린에게 분노하는 사람과, 두려움에 더욱더 그녀를 숭배하는 사람으로 나뉘게 되었다", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T12:49:52.518Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["850b3c9b-abfb-4bc8-a043-2ab60f731d42", "2bc1d2c0-34d2-4135-86bd-94a570fc69fe", "45895f41-02fe-4ce5-9827-53e8c70e3ad0", "d124de12-3005-4d63-b709-a2a0f6a98e0f"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im-12.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im-07.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%85%E1%85%B5%E1%86%AB_%E1%84%8B%E1%85%B5%E1%86%AF%E1%84%85%E1%85%A5%E1%84%89%E1%85%B3%E1%84%90%E1%85%B3.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%85%E1%85%B5%E1%86%AB_%E1%84%8F%E1%85%A5%E1%86%AB%E1%84%89%E1%85%A6%E1%86%B8.png"], "fileNames": ["하이브im-12.jpg", "하이브im-07.jpg", "린_일러스트.png", "린_컨셉.png"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "d9377607-6f94-49f3-b9f8-611504b9bc0e", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 마코빌"}}, {"ipId": "e2344f89-1812-4c33-b46b-5e5a2a0c98d2", "ipName1": "", "ipName2": "", "ipName3": "브라운더스트2 (BROWN DUST2)", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "IP소개: \n\n브라운더스트2는 브라운더스트1 이전 사건을 배경으로 전개되는 IP 입니다. \nJ-SRPG 감성의 스토리전개와 아기자기한 SD캐릭터, 높은 수준의 2D배경과 그래픽을 자랑합니다.\n\n서브컬처 유저들을 대상으로 매력있는 여성캐릭터들이 다양한 배경과 컨셉을 통해 코스튬을 선보입니다.\n\n\n스토리 팩과 캐릭터 팩으로 나뉘어진 스토리 콘텐츠를 제공합니다.\n\n1) 스토리 팩 - 스토리 팩은 브라운더스트 정식 스토리를 따르며 업데이트.\n미지의 힘을 지닌 '사도'를 추종하는 흑마법사 세력이 알 수 없는 음모를 준비하던 중 루고 마을의 약초꾼 라텔은 사건에 휘말린다. \n흑마법사를 조사하던 유스티아는 단서를 쫓다가 라텔과 조우한다.\n\n이후, 여러 지역을 돌아다니며 조력자를 만나고 도움을 받아 사건을 해결하여 성장한다.\n\n\n2) 캐릭터 팩: 캐릭터 팩은 스토리 팩에 등장한 캐릭터들이 다른 시대와 배경의 평행선상에서 벌어지는 컨셉스토리입니다. \n하이스쿨 판타지, 포스트 아포칼립스, 스파이 액션, 호러, 동화, 오리엔탈 등 다양한 컨셉을 중심으로 사건을 따라 전개되는 스토리입니다.\n", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-13T08:12:48.489Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["11dff742-dab5-48c6-ae73-1d09ee96bf74", "ed0dca3c-93ce-4170-be21-c56d1dbbfdd1", "f6aeba42-dd4d-47c5-80fc-6e6c2a389123", "2bfb93ad-73bd-4956-8b72-6a6e168890a6", "31e243f4-561a-44b1-bdcc-1ca6101d4549"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%EB%B8%8C%EB%8D%942%EB%A1%9C%EA%B3%A0_250512.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/NEOWIZ_CI_FullColor%28Horizontal%29_.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%EA%B7%B8%EB%9E%80%ED%9E%90%ED%8A%B8.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/241205_BD2_12%EC%9B%94_%EC%9D%B4%EB%AF%B8%EC%A7%80%EC%97%90%EC%85%8B_01_AOS_1920x1080.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/241029_BD2_10%EC%9B%94_08%EC%8B%9C%EB%B6%80%EC%95%BC%ED%95%A0%EB%A1%9C%EC%9C%88_LiveOps_AOS_iOS_1920x1080.jpg"], "fileNames": ["브더2로고_250512.png", "NEOWIZ_CI_FullColor(Horizontal)_.png", "그란힐트.png", "241205_BD2_12월_이미지에셋_01_AOS_1920x1080.jpg", "241029_BD2_10월_08시부야할로윈_LiveOps_AOS_iOS_1920x1080.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "f82921f7-ee64-4418-9f7a-899435a89da3", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 네오위즈"}}, {"ipId": "e26c92f8-204c-4422-ba80-9a11a28e574d", "ipName1": "", "ipName2": "", "ipName3": "동방프로젝트 / 東方Project", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "동방프로젝트(Touhou Project)는 일본의 동인 서클 ’상하이 앨리스 환악단(上海アリス幻樂団)’의 ZUN이 개발한 탄막 슈팅 게임 시리즈 및 이를 기반으로 한 종합 콘텐츠 프로젝트입니다.\n\n환상향(幻想郷)이라는 가상의 세계를 무대로, 인간, 요괴, 신령 등이 함께 살아가는 독창적인 세계관과 개성적인 캐릭터들이 특징입니다. 고전적인 탄막 슈팅 스타일과 함께, 작품마다 독특한 스토리와 음악이 어우러져 많은 이들의 사랑을 받고 있습니다.\n\n©︎上海アリス幻樂団\n（©︎Team Shanghai Alice）", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-29T02:10:51.727Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["0019e086-3d45-4843-99f8-fde69a9f484f", "5d8ff4df-6607-49a6-8290-6e4334383f7f", "22f16ecc-8428-49c4-9062-c3bfc69284fe", "9c100fca-e97a-4a54-a9fb-3a8b276cba74", "8905f2f0-789a-4465-9628-dcac5a2c4017"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/th19_002.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/th19_005.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/th20_02.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/th19_058.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/th20_01.png"], "fileNames": ["th19_002.jpg", "th19_005.jpg", "th20_02.jpg", "th19_058.jpg", "th20_01.png"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "e6d5fd68-2e38-4c7f-b714-390261ac53bc", "ipName1": "", "ipName2": "", "ipName3": "エヴァンゲリオン新劇場版シリーズ", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "エヴァンゲリオン新劇場版シリーズ\n에반게리온 신극장판 시리즈\n\n\t•\t『ヱヴァンゲリヲン新劇場版：序』\n → 에반게리온 신극장판: 서\n\t•\t『ヱヴァンゲリヲン新劇場版：破』\n → 에반게리온 신극장판: 파\n\t•\t『ヱヴァンゲリヲン新劇場版：Q』\n → 에반게리온 신극장판: Q\n\t•\t『シン・エヴァンゲリオン劇場版』\n → 신 에반게리온 극장판", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-23T07:28:06.057Z", "ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["87d4d7fd-83a5-4365-989b-3c91b87951be", "ae4f2c71-38c8-49be-8628-05a482f6fd23"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E3%82%A8%E3%82%A6%E3%82%99%E3%82%A1%E3%83%B3%E3%82%B1%E3%82%99%E3%83%AA%E3%82%AA%E3%83%B3%E6%96%B0%E5%8A%87%E5%A0%B4%E7%89%88logo.jpeg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E3%82%A8%E3%82%A6%E3%82%99%E3%82%A1%E3%83%B3%E3%82%B1%E3%82%99%E3%83%AA%E3%82%AA%E3%83%B3%E6%96%B0%E5%8A%87%E5%A0%B4%E7%89%88.jpeg"], "fileNames": ["エヴァンゲリオン新劇場版logo.jpeg", "エヴァンゲリオン新劇場版.jpeg"], "categories": [{"ipCatCode": "CA", "ipCatName1": "Comics & Animation", "ipCatName2": "漫画・アニメ", "ipCatName3": "만화/애니", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "ed4b8b9e-9198-4d45-a3c6-d45bf281c0bb", "ipName1": "", "ipName2": "", "ipName3": "『Limbus Company』료슈 E.G.O 「4번째 성냥불」[『Limbus Company』 Ryōshū E.G.O 「4th Match Flame」]", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "료슈 : Limbus Company 게임의 메인 캐릭터 12명 중 한명으로 과묵한 성격에 항상 입에 담배를 물고 있다.,\n\n료슈는 Limbus Company의 메인 캐릭터 12명 중 한명으로 각성기 스킬인 EGO를 사용하면 성냥 형태의 무기를 이용하여 강력한 공격을 하는 컨셉으로 변신한다. 검은색 치마와 일본풍의 옷, 등에는 '열'이 새겨진 망토를 착용하고 있다. \"이 불 또한 예술의 황홀경이지.\" 라는 대사를 함께 사용한다.", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-05-15T05:17:00.562Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["a60bd6d9-5335-4cad-b7f9-afae7c0a5605"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/ryoshu_EGO_resize.png"], "fileNames": ["ryoshu_EGO_resize.png"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "8c37e142-c761-41ec-bb01-e1b0d5d3565e", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "(주)프로젝트문"}}, {"ipId": "f0a7696e-03c1-431f-b1a0-027250301551", "ipName1": "", "ipName2": "", "ipName3": "나나링 ", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "인간이 되기 위해 인삼을 백일동안 먹은 고양이💗\nVLYZ 블리즈 나나링 입니다 ", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T08:37:11.727Z", "ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["092c6fae-f0bb-40a4-a56c-9e38a47b42b0", "80f34b6e-8f43-4b57-91ac-e426f7547e1b", "9786e116-278b-40a7-a35d-dd936f1c665c", "87d6bb9f-d032-4cc7-8fef-4a03a3bf9a50", "dedb3d1c-e599-4d4c-b880-45240c747e00"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-05.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%87%E1%85%B3%E1%86%AF%E1%84%85%E1%85%B5%E1%84%8C%E1%85%B3-04.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%EB%82%98%EB%82%98%EB%8B%982_%EC%BA%90%EB%A6%AD%ED%84%B0.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%EB%82%98%EB%82%98%EB%A7%81%20%EB%A9%94%EC%9D%B4%EB%93%9C%EB%B3%B5%20%EB%A6%AC%EC%82%AC%EC%9D%B4%EC%A7%95.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%EC%99%BC%EC%AA%BD%EC%97%90%20%EC%86%A1%EA%B3%B3%EB%8B%88%20%EC%9E%88%EC%9D%8C.png"], "fileNames": ["블리즈-05.jpg", "블리즈-04.jpg", "나나님2_캐릭터.png", "나나링 메이드복 리사이징.png", "왼쪽에 송곳니 있음.png"], "categories": [{"ipCatCode": "DI", "ipCatName1": "Digital Influencers", "ipCatName2": "デジタルインフルエンサー", "ipCatName3": "디지털인플루언서", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "79a0151b-aba1-4363-ba0c-abf350aaf796", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "나나링"}}, {"ipId": "f0edc8b0-9e4d-4472-9499-59496a80559b", "ipName1": "GIRL`S FRONTLINE", "ipName2": "", "ipName3": "", "ipName4": "", "ipName5": "", "ipDesc1": "In 2030, the Beilan Island Incident erupted, triggering environmental deterioration that threatened humanity's survival and intensified global conflicts, ultimately leading to a worldwide war.\n\nNow, after years of relentless warfare, you take on the role of a Commander under the employment of <PERSON> & Kryuger Private Military Contractor. With a squad of Tactical Dolls, you will navigate the power struggles between factions, strategically deploy your forces, and restore order and stability to a war-torn world.", "ipDesc2": "", "ipDesc3": "", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-29T04:03:49.971Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["3162767d-9cdd-4edc-b0a4-37e1205971b6"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E4%BC%81%E4%B8%9A%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_17458991514748.png"], "fileNames": ["企业微信截图_17458991514748.png"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "83c95581-13f0-4f9a-9364-e7fceb5b19f8", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "Shanghai Yunjie Trading Co., Ltd."}}, {"ipId": "f584040f-7fad-42a9-bde9-f90c939a00d5", "ipName1": "", "ipName2": "", "ipName3": "에픽세븐(테스트)", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "안녕하세요. 테스트입니다. \n안녕하세요. 테스트입니다.\n안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.안녕하세요. 테스트입니다.", "ipDesc4": "", "ipDesc5": "", "ipStatus": "I", "createdDate": "2025-04-29T04:26:40.746Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["974d3790-f5fa-4b91-ba0c-2c33186cce56"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/Screenshot%202025-04-29%20at%2013.45.07.JPG"], "fileNames": ["Screenshot 2025-04-29 at 13.45.07.JPG"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "6153e82c-cd44-4a3b-8066-33f17ed599a0", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "스마일게이트 홀딩스 메가포트지점"}}, {"ipId": "fce74bad-6e00-4544-8209-18bbb98b49b5", "ipName1": "", "ipName2": "", "ipName3": "하츠네 미쿠 / 初音ミク", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "하츠네 미쿠 / 初音ミク", "ipDesc4": "", "ipDesc5": "", "ipStatus": "I", "createdDate": "2025-05-19T13:51:19.628Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["9b242b6d-a210-4ce4-9659-01d13e41d949", "d596cdda-7d77-49f5-b6a0-8f548486001f"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E5%88%9D%E9%9F%B3%E3%83%9F%E3%82%AF%20ipgo.jpeg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%5Bcredit-RGB%5D_HATSUNE_MIKU.jpg"], "fileNames": ["初音ミク ipgo.jpeg", "[credit-RGB]_HATSUNE_MIKU.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}, {"ipId": "fe6878a8-3792-4e1f-a395-e6686797c7fa", "ipName1": "", "ipName2": "", "ipName3": "오즈 리:라이트'(OZ Re:write) - 엔젤", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": "이름 : 엔젤\n나이 : 18세\n종족 : 인간\n신장 : 168cm\n체중 : 55kg\n성격 : 성실함, 상냥함, 낙천적\n\n캐릭터 소개\t\n선이 가는 외모와 어린 나이, 수줍은 표정 탓에 인지하기 어렵지만. 그녀가 사용하는 「스위츠」는\n같은 질량의 사물을 재질과 상관없이 전부 「과자」로 만들 수 있는, 최상위 마법이다. \n그렇게 만들어진 과자는 맛은 존재하며 [육체가 아니라 영혼을 살찌우는 마법]으로 열광적인 \n지지를 받고 있다. 강력한 마력을 기반으로 압펠하임의 최연소 수석 마법사가 되었고 디저트 \n부를 맡아서 성내의 모든 파티를 주관하게 되었다. \n그런 그녀에게는… 딱 한 가지 비밀이 있다.\n파티시에가 꿈이었던 그녀는 함께 2년 전 여동생과 위기에 처했을 때… 위급한 상황에서 구사한 \n마법으로 여동생과 성격이 뒤바뀌고 말았다.\n그렇게 가장 용감했던 그는 가장 사랑스러운 그녀가 되었다.(정신 한정)", "ipDesc4": "", "ipDesc5": "", "ipStatus": "A", "createdDate": "2025-04-30T12:55:06.583Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["534024db-4bd1-43aa-85d9-1be77d08a03b", "83569a21-f97e-4c9c-bcab-912f936daf4e", "2240c75c-c73f-4a5e-9b85-c6e447676eac", "2904c5ba-425b-45ca-bb21-9eb500d8afde"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im-15.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/product/%E1%84%92%E1%85%A1%E1%84%8B%E1%85%B5%E1%84%87%E1%85%B3im-03.jpg", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%8B%E1%85%A6%E1%86%AB%E1%84%8C%E1%85%A6%E1%86%AF_%E1%84%8B%E1%85%B5%E1%86%AF%E1%84%85%E1%85%A5%E1%84%89%E1%85%B3%E1%84%90%E1%85%B3.png", "https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/%E1%84%8B%E1%85%A6%E1%86%AB%E1%84%8C%E1%85%A6%E1%86%AF_%E1%84%8F%E1%85%A5%E1%86%AB%E1%84%89%E1%85%A6%E1%86%B8.png"], "fileNames": ["하이브im-15.jpg", "하이브im-03.jpg", "엔젤_일러스트.png", "엔젤_컨셉.png"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "d9377607-6f94-49f3-b9f8-611504b9bc0e", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": null, "lastName": null, "nickname": null, "compName": "주식회사 마코빌"}}, {"ipId": "fe99eaf6-c0e1-4af8-ad5f-8dcf5d597fb0", "ipName1": "", "ipName2": "", "ipName3": "우마무스메 프리티 더비 / ウマ娘 プリティーダービー", "ipName4": "", "ipName5": "", "ipDesc1": "", "ipDesc2": "", "ipDesc3": " ", "ipDesc4": "", "ipDesc5": "", "ipStatus": "I", "createdDate": "2025-05-16T06:53:52.397Z", "ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": "", "ipImgId": ["27f67074-20c5-4c3a-ae83-34e1b5bf3651"], "path": ["https://ipgo-be-api-bucket.s3.ap-northeast-1.amazonaws.com/ip/2%E1%84%8E%E1%85%A1_03.jpg"], "fileNames": ["2차_03.jpg"], "categories": [{"ipCatCode": "GM", "ipCatName1": "Gaming & Metaverse", "ipCatName2": "ゲーム・メタバース", "ipCatName3": "게임/메타버스", "ipCatName4": "", "ipCatName5": ""}], "owner": {"id": "12629b11-e8b4-4cbf-80bf-db95afe7976c", "username": "<EMAIL>", "email": "<EMAIL>", "firstName": "", "lastName": "", "nickname": "", "compName": "원더페스티벌 한국 실행위원회"}}]