# Database Configuration
# Target database (PostgreSQL - Directus)
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
# Source database (MySQL - Legacy system)
SOURCE_DATABASE_URL=mysql://username:password@source-host:3306/source_database

# Directus Configuration
DIRECTUS_URL=https://your-directus-instance.com
DIRECTUS_EMAIL=<EMAIL>
DIRECTUS_PASSWORD=your-password

# File Upload Configuration - Different folders for different content types
# IP migration script uses IP_IMAGES_FOLDER_ID
IP_IMAGES_FOLDER_ID=8036543e-dce2-4c84-9ee7-c53273ffb2ce
# Products migration script uses PRODUCT_IMAGES_FOLDER_ID (hardcoded in script)
PRODUCT_IMAGES_FOLDER_ID=fe1d3239-bc75-42cc-b937-7fe23a4e43e8
# User migration script uses USER_IMAGES_FOLDER_ID (hardcoded in script)
USER_IMAGES_FOLDER_ID=e15ab896-96c2-4886-9071-c93e0529ff78
# Company images folder (for future use)
COMPANY_IMAGES_FOLDER_ID=3cb8e671-e115-4332-896d-2bb38604c66c

# Translation Configuration
GEMINI_API_KEY=your-gemini-api-key

# Optional Configuration
DEFAULT_CATEGORY_ID=21
