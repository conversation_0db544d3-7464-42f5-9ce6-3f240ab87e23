import { Kysely, <PERSON>gresDial<PERSON><PERSON>, MysqlDialect, sql } from 'kysely';
import { Pool } from 'pg';
import { createPool } from 'mysql2';
import type { DB as sDB } from './source-db';
import type { DB as tDB } from './target-db';
import { config } from 'dotenv';

import { createDirectus, authentication, rest, importFile } from '@directus/sdk';

// Load environment variables from .env
config();
// const TARGET_DIRECTUS_URL = 'https://stg-admin.ipgo.space';
const TARGET_DIRECTUS_URL = 'http://localhost:8055';
const targetDirectus = createDirectus(TARGET_DIRECTUS_URL).with(rest()).with(authentication());

// Migration statistics tracking
interface MigrationStats {
  totalUsers: number;
  processedUsers: number;
  skippedUsers: number;
  createdUsers: number;
  uploadedAvatars: number;
  createdCreators: number;
  createdFans: number;
  createdOwners: number;
  errors: Array<{ userId: string; error: string; step: string }>;
}

// Initialize stats
const stats: MigrationStats = {
  totalUsers: 0,
  processedUsers: 0,
  skippedUsers: 0,
  createdUsers: 0,
  uploadedAvatars: 0,
  createdCreators: 0,
  createdFans: 0,
  createdOwners: 0,
  errors: []
};

// Logging utilities
function logInfo(message: string) {
  console.log(`[INFO] ${new Date().toISOString()} - ${message}`);
}

function logError(message: string, error?: any) {
  console.error(`[ERROR] ${new Date().toISOString()} - ${message}`, error || '');
}

function logSuccess(message: string) {
  console.log(`[SUCCESS] ${new Date().toISOString()} - ${message}`);
}

function logWarning(message: string) {
  console.warn(`[WARNING] ${new Date().toISOString()} - ${message}`);
}

// Pre-check functions
async function performPreChecks() {
  logInfo('Starting pre-migration checks...');

  // Check environment variables
  if (!process.env.DATABASE_URL) {
    throw new Error('DATABASE_URL environment variable is required');
  }
  logSuccess('Environment variables validated');

  // Test Directus connection
  try {
    await targetDirectus.login('<EMAIL>', 'Fish24Time');
    logSuccess('Directus connection established');
  } catch (error) {
    throw new Error(`Failed to connect to Directus: ${error}`);
  }
}

export interface SourceUser {
  id: string;
  avatar_url: string;
  username: string;
  email: string;
  password: string;
  first_name: null;
  last_name: null;
  nickname: string;
  self_intro: string;
  birth_dt: null;
  apt_suite: null;
  city: null;
  province: null;
  country: null;
  postcode: null;
  comp_name: null;
  comp_cont_name: null;
  comp_role: null;
  comp_tel_no: null;
  comp_link: null;
  avatar_file_id: null;
  status: string;
  verify_code: string;
  verify_code_exp_dt: Date;
  created_by: string;
  updated_by: string;
  created_dt: Date;
  updated_dt: Date;
  version: number;
  tenant_code: string;
  user_role: string;
}

const targetDb = new Kysely<tDB>({
  dialect: new PostgresDialect({
    pool: new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: {
        rejectUnauthorized: false,
        checkServerIdentity: () => undefined
      }
    }),
  }),
});

const sourceDb = new Kysely<sDB>({
  dialect: new MysqlDialect({
    pool: createPool({
      uri: 'mysql://admin:<EMAIL>/smg_ipgo',
    }),
  }),
});

const ROLE_ID_MAP: Record<string, string> = {
  ROLE_OWNER: '15fa6fb9-5517-468f-92b6-f78fd6559a16',
  ROLE_CREATOR: '7daf2614-50ed-42de-a3ca-fc4185305eab',
  ROLE_FAN: '3d2684a9-7271-4df8-be37-6da1c9aa120b',
};

// Database connectivity checks
async function testDatabaseConnections() {
  logInfo('Testing database connections...');

  try {
    // Test target database connection
    await targetDb.selectFrom('directus_users').select('id').limit(1).execute();
    logSuccess('Target database (PostgreSQL) connection successful');
  } catch (error) {
    throw new Error(`Failed to connect to target database: ${error}`);
  }

  try {
    // Test source database connection
    await sourceDb.selectFrom('t_user').select('id').limit(1).execute();
    logSuccess('Source database (MySQL) connection successful');
  } catch (error) {
    throw new Error(`Failed to connect to source database: ${error}`);
  }
}

// Validation functions
async function validateRoleMapping() {
  logInfo('Validating role mappings...');

  for (const [roleCode, roleId] of Object.entries(ROLE_ID_MAP)) {
    try {
      const role = await targetDb
        .selectFrom('directus_roles')
        .select('id')
        .where('id', '=', roleId)
        .executeTakeFirst();

      if (!role) {
        throw new Error(`Role ID ${roleId} for ${roleCode} not found in target database`);
      }
    } catch (error) {
      throw new Error(`Failed to validate role ${roleCode}: ${error}`);
    }
  }

  logSuccess('All role mappings validated');
}

async function main() {
  try {
    logInfo('🚀 Starting user migration process...');

    // Perform pre-migration checks
    await performPreChecks();
    await testDatabaseConnections();
    await validateRoleMapping();

    logInfo('✅ All pre-checks passed, starting migration...');

    // Fetch users from source database
    logInfo('Fetching users from source database...');
    const currentUsers = await sql<SourceUser>`SELECT u.*,r.role_code AS user_role, f.path AS avatar_url
      FROM t_user u
      LEFT JOIN t_user_role ur ON u.id=ur.user_id
      LEFT JOIN t_role r ON ur.role_id=r.id
      LEFT JOIN t_file f ON u.avatar_file_id=f.id
      WHERE u.status='VERIFIED';`.execute(sourceDb);

    stats.totalUsers = currentUsers.rows.length;
    logInfo(`Found ${stats.totalUsers} verified users to migrate`);

    if (stats.totalUsers === 0) {
      logWarning('No users found to migrate');
      return;
    }

    // Create users in Directus if not yet exist
    for (const [index, user] of currentUsers.rows.entries()) {
      const progress = `[${index + 1}/${stats.totalUsers}]`;
      logInfo(`${progress} Processing user: ${user.email} (${user.user_role})`);

      try {
        // insert user, return id
        try {
          await targetDb
            .insertInto('directus_users')
            .values({
              id: user.id,
              email: user.email,
              role: ROLE_ID_MAP[user.user_role] ?? ROLE_ID_MAP.ROLE_FAN,
              status: 'active',
              password: user.password,
              password_migrated: false,
              provider: 'default',
              description: user.self_intro,
              first_name: user.first_name,
              last_name: user.last_name,
              // Add missing user profile fields
              nickname: user.nickname,
              birthday: user.birth_dt,
            })
            .returning('id')
            .executeTakeFirst();

          logSuccess(`${progress} User created: ${user.email}`);
          stats.createdUsers++;
        } catch (e: any) {
          // Check for duplicate key error in different ways
          const errorMessage = e.message || e.detail || e.toString();
          if (errorMessage.includes('already exist') || errorMessage.includes('duplicate key') || errorMessage.includes('UNIQUE constraint')) {
            logWarning(`${progress} User already exists: ${user.email}`);
            stats.skippedUsers++;
          } else {
            // If it's not a duplicate error, log and re-throw
            const error = `Error inserting user: ${errorMessage}`;
            logError(`${progress} ${error}`);
            stats.errors.push({ userId: user.id, error, step: 'user_creation' });
            continue; // Skip to next user instead of throwing
          }
        }

        // Upload avatar
        let fileRes;
        if (user.avatar_url) {
          try {
            // Check if user already has an avatar
            const existingUser = await targetDb
              .selectFrom('directus_users')
              .select('avatar')
              .where('id', '=', user.id)
              .executeTakeFirst();

            if (existingUser?.avatar) {
              logWarning(`${progress} User already has avatar`);
              // Use existing avatar for profile creation
              fileRes = { id: existingUser.avatar };
            } else {
              fileRes = await targetDirectus.request(
                importFile(user.avatar_url, { folder: 'e15ab896-96c2-4886-9071-c93e0529ff78' }),
              );
              logSuccess(`${progress} Avatar uploaded: ${fileRes.id}`);
              stats.uploadedAvatars++;

              // add to user
              await targetDb
                .updateTable('directus_users')
                .set({
                  avatar: fileRes.id,
                })
                .where('id', '=', user.id)
                .execute();
            }
          } catch (e: any) {
            const error = `Failed to upload avatar: ${e.message}`;
            logError(`${progress} ${error}`);
            stats.errors.push({ userId: user.id, error, step: 'avatar_upload' });
          }
        }

        // If ROLE_CREATOR, create a 'creator' profile in directus
        if (user.user_role === 'ROLE_CREATOR') {
          try {
            // Check if creator profile already exists
            const existingCreator = await targetDb
              .selectFrom('creator')
              .select('id')
              .where('user', '=', user.id)
              .executeTakeFirst();

            if (existingCreator) {
              logWarning(`${progress} Creator profile already exists`);
            } else {
              await targetDb
                .insertInto('creator')
                .values({
                  avatar: fileRes?.id ?? null,
                  status: 'active',
                  nickname: user.nickname,
                  introduction: user.self_intro,
                  user: user.id,
                  // Add missing creator profile fields
                  first_name: user.first_name,
                  last_name: user.last_name,
                  city: user.city,
                  country: user.country,
                  province: user.province,
                  postal_code: user.postcode,
                  street_address_1: user.apt_suite,
                })
                .execute();
              logSuccess(`${progress} Creator profile created`);
              stats.createdCreators++;
            }
          } catch (e: any) {
            const error = `Failed to create creator profile: ${e.message}`;
            logError(`${progress} ${error}`);
            stats.errors.push({ userId: user.id, error, step: 'creator_profile' });
          }
        }

        // If ROLE_FAN, create an address and set as default
        if (user.user_role === 'ROLE_FAN') {
          try {
            // Check if user already has a default address
            const existingUser = await targetDb
              .selectFrom('directus_users')
              .select('default_address')
              .where('id', '=', user.id)
              .executeTakeFirst();

            if (existingUser?.default_address) {
              logWarning(`${progress} Fan already has default address`);
            } else {
              // Create address entry
              const addressRes = await targetDb
                .insertInto('addresses')
                .values({
                  user: user.id,
                  name: `${user.first_name || ''} ${user.last_name || ''}`.trim() || null,
                  address_line_1: user.apt_suite,
                  city: user.city,
                  country: user.country,
                  postcode: user.postcode,
                  province: user.province,
                })
                .returning('id')
                .executeTakeFirst();

              // Update user to set default address
              if (addressRes?.id) {
                await targetDb
                  .updateTable('directus_users')
                  .set({
                    default_address: addressRes.id,
                  })
                  .where('id', '=', user.id)
                  .execute();
              }
              logSuccess(`${progress} Fan address created`);
              stats.createdFans++;
            }
          } catch (e: any) {
            const error = `Failed to create fan address: ${e.message}`;
            logError(`${progress} ${error}`);
            stats.errors.push({ userId: user.id, error, step: 'fan_address' });
          }
        }

        // If ROLE_OWNER, create an 'ip_owner' profile in directus
        if (user.user_role === 'ROLE_OWNER') {
          try {
            // Check if IP owner profile already exists
            const existingOwner = await targetDb
              .selectFrom('ip_owner')
              .select('id')
              .where('user', '=', user.id)
              .executeTakeFirst();

            if (existingOwner) {
              logWarning(`${progress} IP Owner profile already exists`);
            } else {
              await targetDb
                .insertInto('ip_owner')
                .values({
                  status: 'active',
                  image: fileRes?.id ?? null,
                  name: user.comp_cont_name,
                  country: user.country,
                  homepage_link: user.comp_link,
                  company_name: user.comp_name,
                  company_phone_number: user.comp_tel_no,
                  company_role: user.comp_role,
                  user: user.id,
                })
                .execute();
              logSuccess(`${progress} IP Owner profile created`);
              stats.createdOwners++;
            }
          } catch (e: any) {
            const error = `Failed to create IP owner profile: ${e.message}`;
            logError(`${progress} ${error}`);
            stats.errors.push({ userId: user.id, error, step: 'owner_profile' });
          }
        }

        stats.processedUsers++;
      } catch (e: any) {
        const error = `Unexpected error processing user: ${e.message}`;
        logError(`${progress} ${error}`);
        stats.errors.push({ userId: user.id, error, step: 'general' });
        stats.processedUsers++;
      }
    }

    // Post-migration validation and summary
    await performPostMigrationValidation();
    printMigrationSummary();

  } catch (error) {
    logError('Migration failed with critical error:', error);
    throw error;
  }
}

// Post-migration validation
async function performPostMigrationValidation() {
  logInfo('🔍 Performing post-migration validation...');

  try {
    // Count migrated users by role
    const userCounts = await targetDb
      .selectFrom('directus_users')
      .leftJoin('directus_roles', 'directus_users.role', 'directus_roles.id')
      .select([
        'directus_roles.name as role_name',
        sql<number>`count(*)`.as('count')
      ])
      .groupBy('directus_roles.name')
      .execute();

    logInfo('User counts by role:');
    for (const count of userCounts) {
      logInfo(`  ${count.role_name}: ${count.count} users`);
    }

    // Validate creator profiles
    const creatorCount = await targetDb
      .selectFrom('creator')
      .select(sql<number>`count(*)`.as('count'))
      .executeTakeFirst();
    logInfo(`Creator profiles: ${creatorCount?.count || 0}`);

    // Validate fan addresses
    const addressCount = await targetDb
      .selectFrom('addresses')
      .select(sql<number>`count(*)`.as('count'))
      .executeTakeFirst();
    logInfo(`Fan addresses: ${addressCount?.count || 0}`);

    // Validate owner profiles
    const ownerCount = await targetDb
      .selectFrom('ip_owner')
      .select(sql<number>`count(*)`.as('count'))
      .executeTakeFirst();
    logInfo(`IP Owner profiles: ${ownerCount?.count || 0}`);

    logSuccess('Post-migration validation completed');
  } catch (error) {
    logError('Post-migration validation failed:', error);
  }
}

// Print migration summary
function printMigrationSummary() {
  logInfo('📊 Migration Summary:');
  logInfo('='.repeat(50));
  logInfo(`Total users found: ${stats.totalUsers}`);
  logInfo(`Users processed: ${stats.processedUsers}`);
  logInfo(`Users created: ${stats.createdUsers}`);
  logInfo(`Users skipped (already exist): ${stats.skippedUsers}`);
  logInfo(`Avatars uploaded: ${stats.uploadedAvatars}`);
  logInfo(`Creator profiles created: ${stats.createdCreators}`);
  logInfo(`Fan addresses created: ${stats.createdFans}`);
  logInfo(`IP Owner profiles created: ${stats.createdOwners}`);
  logInfo(`Total errors: ${stats.errors.length}`);

  if (stats.errors.length > 0) {
    logError('Errors encountered:');
    for (const error of stats.errors) {
      logError(`  User ${error.userId} (${error.step}): ${error.error}`);
    }
  }

  const successRate = stats.totalUsers > 0 ?
    ((stats.processedUsers - stats.errors.length) / stats.totalUsers * 100).toFixed(2) :
    '0';

  logInfo(`Success rate: ${successRate}%`);
  logInfo('='.repeat(50));

  if (stats.errors.length === 0) {
    logSuccess('🎉 Migration completed successfully with no errors!');
  } else if (stats.errors.length < stats.totalUsers * 0.1) {
    logWarning('⚠️ Migration completed with minor issues (< 10% error rate)');
  } else {
    logError('❌ Migration completed with significant issues (>= 10% error rate)');
  }
}

// Execute migration
await main();
